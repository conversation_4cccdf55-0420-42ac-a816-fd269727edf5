/* ===== STANDARDIZED COLOR SYSTEM ===== */
:root {
    /* PRIMARY COLORS */
    --color-primary-50: #eff6ff;
    --color-primary-100: #d7e7ff;
    --color-primary-200: #b9d3ff;
    --color-primary-300: #8bb8ff;
    --color-primary-400: #5a9bff;
    --color-primary-500: #2349c8;
    --color-primary-600: #1a48ca;
    --color-primary-700: #0b63e5;
    --color-primary-800: #001a56;
    --color-primary-900: #091542;

    /* NEUTRAL COLORS */
    --color-white: #ffffff;
    --color-gray-50: #fafafa;
    --color-gray-100: #f4f4f4;
    --color-gray-200: #eeeff1;
    --color-gray-300: #e5e7eb;
    --color-gray-400: #d6d6d6;
    --color-gray-500: #dfdfdf;
    --color-gray-600: #808593;
    --color-gray-700: #545454;
    --color-gray-800: #303030;
    --color-gray-900: #282828;
    --color-black-50: #1a1a1a;
    --color-black-100: #151f2c;
    --color-black-200: #151515;
    --color-black-300: #111111;
    --color-black: #000000;

    /* BACKGROUND COLORS */
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #fff9f9;
    --color-bg-tertiary: #faf7f7;
    --color-bg-accent: #fef2ed;
    --color-bg-dark: #050a2f;
    --color-bg-dark-secondary: #0b1b40;
    --color-bg-dark-tertiary: #020617;
    --color-bg-gradient-start: #0b1b40;
    --color-bg-gradient-end: #020617;

    /* SEMANTIC COLORS */
    --color-success-50: #f2fff4;
    --color-success-500: #339933;
    --color-warning-50: #fff9e4;
    --color-warning-500: #efeb64;
    --color-error-500: #dd0031;
    --color-info-50: #ede8ff;
    --color-info-500: #3880ff;

    /* TECHNOLOGY COLORS */
    --color-html: #e34f26;
    --color-css: #1572b6;
    --color-javascript: #f7df1e;
    --color-react: #61dafb;
    --color-angular: #dd0031;
    --color-vue: #4fc08d;
    --color-node: #339933;
    --color-python: #3776ab;
    --color-php: #777bb4;
    --color-java: #007396;
    --color-csharp: #239120;
    --color-ruby: #cc342d;
    --color-sass: #cc6699;
    --color-bootstrap: #7952b3;
    --color-tailwind: #06b6d4;
    --color-webpack: #8dd6f9;
    --color-typescript: #3178c6;
    --color-graphql: #e10098;
    --color-flutter: #02569b;
    --color-swift: #fa7343;
    --color-kotlin: #7f52ff;
    --color-ionic: #3880ff;
    --color-xamarin: #3498db;
    --color-unity: #000000;
    --color-nativescript: #3655ff;
    --color-phonegap: #999999;
    --color-d3: #f68e56;
    --color-jquery: #0769ad;

    /* ACCENT COLORS */
    --color-accent-blue: #156bc1;
    --color-accent-purple: #7f52ff;
    --color-accent-pink: #e535ab;
    --color-accent-orange: #fa7343;
    --color-accent-green: #4fc08d;
    --color-accent-cyan: #06b6d4;

    /* SPECIAL COLORS */
    --color-transparent: transparent;
    --color-current: currentColor;
    --color-inherit: inherit;
    --color-shadow-light: rgba(255, 255, 255, 0.28);
    --color-shadow-dark: rgba(0, 0, 0, 0.1);
    --color-overlay-light: rgba(255, 255, 255, 0.1);
    --color-overlay-dark: rgba(0, 0, 0, 0.5);
}

/* ===== UTILITY CLASSES ===== */
@layer utilities {
    /* Background utilities */
    .bg-primary-50 {
        background-color: var(--color-primary-50);
    }
    .bg-primary-100 {
        background-color: var(--color-primary-100);
    }
    .bg-primary-200 {
        background-color: var(--color-primary-200);
    }
    .bg-primary-300 {
        background-color: var(--color-primary-300);
    }
    .bg-primary-400 {
        background-color: var(--color-primary-400);
    }
    .bg-primary-500 {
        background-color: var(--color-primary-500);
    }
    .bg-primary-600 {
        background-color: var(--color-primary-600);
    }
    .bg-primary-700 {
        background-color: var(--color-primary-700);
    }
    .bg-primary-800 {
        background-color: var(--color-primary-800);
    }
    .bg-primary-900 {
        background-color: var(--color-primary-900);
    }
    .bg-white {
        background-color: var(--color-white);
    }
    .bg-gray-50 {
        background-color: var(--color-gray-50);
    }
    .bg-gray-100 {
        background-color: var(--color-gray-100);
    }
    .bg-gray-200 {
        background-color: var(--color-gray-200);
    }
    .bg-gray-300 {
        background-color: var(--color-gray-300);
    }
    .bg-gray-400 {
        background-color: var(--color-gray-400);
    }
    .bg-gray-500 {
        background-color: var(--color-gray-500);
    }
    .bg-gray-600 {
        background-color: var(--color-gray-600);
    }
    .bg-gray-700 {
        background-color: var(--color-gray-700);
    }
    .bg-gray-800 {
        background-color: var(--color-gray-800);
    }
    .bg-gray-900 {
        background-color: var(--color-gray-900);
    }
    .bg-black-50 {
        background-color: var(--color-black-50);
    }
    .bg-black-100 {
        background-color: var(--color-black-100);
    }
    .bg-black-200 {
        background-color: var(--color-black-200);
    }
    .bg-black-300 {
        background-color: var(--color-black-300);
    }
    .bg-black {
        background-color: var(--color-black);
    }
    .bg-bg-primary {
        background-color: var(--color-bg-primary);
    }
    .bg-bg-secondary {
        background-color: var(--color-bg-secondary);
    }
    .bg-bg-tertiary {
        background-color: var(--color-bg-tertiary);
    }
    .bg-bg-accent {
        background-color: var(--color-bg-accent);
    }
    .bg-bg-dark {
        background-color: var(--color-bg-dark);
    }
    .bg-bg-dark-secondary {
        background-color: var(--color-bg-dark-secondary);
    }
    .bg-bg-dark-tertiary {
        background-color: var(--color-bg-dark-tertiary);
    }
    .bg-bg-gradient-start {
        background-color: var(--color-bg-gradient-start);
    }
    .bg-bg-gradient-end {
        background-color: var(--color-bg-gradient-end);
    }
    .bg-success-50 {
        background-color: var(--color-success-50);
    }
    .bg-success-500 {
        background-color: var(--color-success-500);
    }
    .bg-warning-50 {
        background-color: var(--color-warning-50);
    }
    .bg-warning-500 {
        background-color: var(--color-warning-500);
    }
    .bg-error-500 {
        background-color: var(--color-error-500);
    }
    .bg-info-50 {
        background-color: var(--color-info-50);
    }
    .bg-info-500 {
        background-color: var(--color-info-500);
    }
    .bg-html {
        background-color: var(--color-html);
    }
    .bg-css {
        background-color: var(--color-css);
    }
    .bg-javascript {
        background-color: var(--color-javascript);
    }
    .bg-react {
        background-color: var(--color-react);
    }
    .bg-angular {
        background-color: var(--color-angular);
    }
    .bg-vue {
        background-color: var(--color-vue);
    }
    .bg-node {
        background-color: var(--color-node);
    }
    .bg-python {
        background-color: var(--color-python);
    }
    .bg-php {
        background-color: var(--color-php);
    }
    .bg-java {
        background-color: var(--color-java);
    }
    .bg-csharp {
        background-color: var(--color-csharp);
    }
    .bg-ruby {
        background-color: var(--color-ruby);
    }
    .bg-sass {
        background-color: var(--color-sass);
    }
    .bg-bootstrap {
        background-color: var(--color-bootstrap);
    }
    .bg-tailwind {
        background-color: var(--color-tailwind);
    }
    .bg-webpack {
        background-color: var(--color-webpack);
    }
    .bg-typescript {
        background-color: var(--color-typescript);
    }
    .bg-graphql {
        background-color: var(--color-graphql);
    }
    .bg-flutter {
        background-color: var(--color-flutter);
    }
    .bg-swift {
        background-color: var(--color-swift);
    }
    .bg-kotlin {
        background-color: var(--color-kotlin);
    }
    .bg-ionic {
        background-color: var(--color-ionic);
    }
    .bg-xamarin {
        background-color: var(--color-xamarin);
    }
    .bg-unity {
        background-color: var(--color-unity);
    }
    .bg-nativescript {
        background-color: var(--color-nativescript);
    }
    .bg-phonegap {
        background-color: var(--color-phonegap);
    }
    .bg-d3 {
        background-color: var(--color-d3);
    }
    .bg-jquery {
        background-color: var(--color-jquery);
    }
    .bg-accent-blue {
        background-color: var(--color-accent-blue);
    }
    .bg-accent-purple {
        background-color: var(--color-accent-purple);
    }
    .bg-accent-pink {
        background-color: var(--color-accent-pink);
    }
    .bg-accent-orange {
        background-color: var(--color-accent-orange);
    }
    .bg-accent-green {
        background-color: var(--color-accent-green);
    }
    .bg-accent-cyan {
        background-color: var(--color-accent-cyan);
    }
    .bg-transparent {
        background-color: var(--color-transparent);
    }
    .bg-current {
        background-color: var(--color-current);
    }
    .bg-inherit {
        background-color: var(--color-inherit);
    }
    .bg-shadow-light {
        background-color: var(--color-shadow-light);
    }
    .bg-shadow-dark {
        background-color: var(--color-shadow-dark);
    }
    .bg-overlay-light {
        background-color: var(--color-overlay-light);
    }
    .bg-overlay-dark {
        background-color: var(--color-overlay-dark);
    }

    /* Text utilities */
    .text-primary-50 {
        color: var(--color-primary-50);
    }
    .text-primary-100 {
        color: var(--color-primary-100);
    }
    .text-primary-200 {
        color: var(--color-primary-200);
    }
    .text-primary-300 {
        color: var(--color-primary-300);
    }
    .text-primary-400 {
        color: var(--color-primary-400);
    }
    .text-primary-500 {
        color: var(--color-primary-500);
    }
    .text-primary-600 {
        color: var(--color-primary-600);
    }
    .text-primary-700 {
        color: var(--color-primary-700);
    }
    .text-primary-800 {
        color: var(--color-primary-800);
    }
    .text-primary-900 {
        color: var(--color-primary-900);
    }
    .text-white {
        color: var(--color-white);
    }
    .text-gray-50 {
        color: var(--color-gray-50);
    }
    .text-gray-100 {
        color: var(--color-gray-100);
    }
    .text-gray-200 {
        color: var(--color-gray-200);
    }
    .text-gray-300 {
        color: var(--color-gray-300);
    }
    .text-gray-400 {
        color: var(--color-gray-400);
    }
    .text-gray-500 {
        color: var(--color-gray-500);
    }
    .text-gray-600 {
        color: var(--color-gray-600);
    }
    .text-gray-700 {
        color: var(--color-gray-700);
    }
    .text-gray-800 {
        color: var(--color-gray-800);
    }
    .text-gray-900 {
        color: var(--color-gray-900);
    }
    .text-black-50 {
        color: var(--color-black-50);
    }
    .text-black-100 {
        color: var(--color-black-100);
    }
    .text-black-200 {
        color: var(--color-black-200);
    }
    .text-black-300 {
        color: var(--color-black-300);
    }
    .text-black {
        color: var(--color-black);
    }
    .text-bg-primary {
        color: var(--color-bg-primary);
    }
    .text-bg-secondary {
        color: var(--color-bg-secondary);
    }
    .text-bg-tertiary {
        color: var(--color-bg-tertiary);
    }
    .text-bg-accent {
        color: var(--color-bg-accent);
    }
    .text-bg-dark {
        color: var(--color-bg-dark);
    }
    .text-bg-dark-secondary {
        color: var(--color-bg-dark-secondary);
    }
    .text-bg-dark-tertiary {
        color: var(--color-bg-dark-tertiary);
    }
    .text-bg-gradient-start {
        color: var(--color-bg-gradient-start);
    }
    .text-bg-gradient-end {
        color: var(--color-bg-gradient-end);
    }
    .text-success-50 {
        color: var(--color-success-50);
    }
    .text-success-500 {
        color: var(--color-success-500);
    }
    .text-warning-50 {
        color: var(--color-warning-50);
    }
    .text-warning-500 {
        color: var(--color-warning-500);
    }
    .text-error-500 {
        color: var(--color-error-500);
    }
    .text-info-50 {
        color: var(--color-info-50);
    }
    .text-info-500 {
        color: var(--color-info-500);
    }
    .text-html {
        color: var(--color-html);
    }
    .text-css {
        color: var(--color-css);
    }
    .text-javascript {
        color: var(--color-javascript);
    }
    .text-react {
        color: var(--color-react);
    }
    .text-angular {
        color: var(--color-angular);
    }
    .text-vue {
        color: var(--color-vue);
    }
    .text-node {
        color: var(--color-node);
    }
    .text-python {
        color: var(--color-python);
    }
    .text-php {
        color: var(--color-php);
    }
    .text-java {
        color: var(--color-java);
    }
    .text-csharp {
        color: var(--color-csharp);
    }
    .text-ruby {
        color: var(--color-ruby);
    }
    .text-sass {
        color: var(--color-sass);
    }
    .text-bootstrap {
        color: var(--color-bootstrap);
    }
    .text-tailwind {
        color: var(--color-tailwind);
    }
    .text-webpack {
        color: var(--color-webpack);
    }
    .text-typescript {
        color: var(--color-typescript);
    }
    .text-graphql {
        color: var(--color-graphql);
    }
    .text-flutter {
        color: var(--color-flutter);
    }
    .text-swift {
        color: var(--color-swift);
    }
    .text-kotlin {
        color: var(--color-kotlin);
    }
    .text-ionic {
        color: var(--color-ionic);
    }
    .text-xamarin {
        color: var(--color-xamarin);
    }
    .text-unity {
        color: var(--color-unity);
    }
    .text-nativescript {
        color: var(--color-nativescript);
    }
    .text-phonegap {
        color: var(--color-phonegap);
    }
    .text-d3 {
        color: var(--color-d3);
    }
    .text-jquery {
        color: var(--color-jquery);
    }
    .text-accent-blue {
        color: var(--color-accent-blue);
    }
    .text-accent-purple {
        color: var(--color-accent-purple);
    }
    .text-accent-pink {
        color: var(--color-accent-pink);
    }
    .text-accent-orange {
        color: var(--color-accent-orange);
    }
    .text-accent-green {
        color: var(--color-accent-green);
    }
    .text-accent-cyan {
        color: var(--color-accent-cyan);
    }
    .text-transparent {
        color: var(--color-transparent);
    }
    .text-current {
        color: var(--color-current);
    }
    .text-inherit {
        color: var(--color-inherit);
    }
    .text-shadow-light {
        color: var(--color-shadow-light);
    }
    .text-shadow-dark {
        color: var(--color-shadow-dark);
    }
    .text-overlay-light {
        color: var(--color-overlay-light);
    }
    .text-overlay-dark {
        color: var(--color-overlay-dark);
    }

    /* Border utilities */
    .border-primary-50 {
        border-color: var(--color-primary-50);
    }
    .border-primary-100 {
        border-color: var(--color-primary-100);
    }
    .border-primary-200 {
        border-color: var(--color-primary-200);
    }
    .border-primary-300 {
        border-color: var(--color-primary-300);
    }
    .border-primary-400 {
        border-color: var(--color-primary-400);
    }
    .border-primary-500 {
        border-color: var(--color-primary-500);
    }
    .border-primary-600 {
        border-color: var(--color-primary-600);
    }
    .border-primary-700 {
        border-color: var(--color-primary-700);
    }
    .border-primary-800 {
        border-color: var(--color-primary-800);
    }
    .border-primary-900 {
        border-color: var(--color-primary-900);
    }
    .border-white {
        border-color: var(--color-white);
    }
    .border-gray-50 {
        border-color: var(--color-gray-50);
    }
    .border-gray-100 {
        border-color: var(--color-gray-100);
    }
    .border-gray-200 {
        border-color: var(--color-gray-200);
    }
    .border-gray-300 {
        border-color: var(--color-gray-300);
    }
    .border-gray-400 {
        border-color: var(--color-gray-400);
    }
    .border-gray-500 {
        border-color: var(--color-gray-500);
    }
    .border-gray-600 {
        border-color: var(--color-gray-600);
    }
    .border-gray-700 {
        border-color: var(--color-gray-700);
    }
    .border-gray-800 {
        border-color: var(--color-gray-800);
    }
    .border-gray-900 {
        border-color: var(--color-gray-900);
    }
    .border-black-50 {
        border-color: var(--color-black-50);
    }
    .border-black-100 {
        border-color: var(--color-black-100);
    }
    .border-black-200 {
        border-color: var(--color-black-200);
    }
    .border-black-300 {
        border-color: var(--color-black-300);
    }
    .border-black {
        border-color: var(--color-black);
    }
    .border-bg-primary {
        border-color: var(--color-bg-primary);
    }
    .border-bg-secondary {
        border-color: var(--color-bg-secondary);
    }
    .border-bg-tertiary {
        border-color: var(--color-bg-tertiary);
    }
    .border-bg-accent {
        border-color: var(--color-bg-accent);
    }
    .border-bg-dark {
        border-color: var(--color-bg-dark);
    }
    .border-bg-dark-secondary {
        border-color: var(--color-bg-dark-secondary);
    }
    .border-bg-dark-tertiary {
        border-color: var(--color-bg-dark-tertiary);
    }
    .border-bg-gradient-start {
        border-color: var(--color-bg-gradient-start);
    }
    .border-bg-gradient-end {
        border-color: var(--color-bg-gradient-end);
    }
    .border-success-50 {
        border-color: var(--color-success-50);
    }
    .border-success-500 {
        border-color: var(--color-success-500);
    }
    .border-warning-50 {
        border-color: var(--color-warning-50);
    }
    .border-warning-500 {
        border-color: var(--color-warning-500);
    }
    .border-error-500 {
        border-color: var(--color-error-500);
    }
    .border-info-50 {
        border-color: var(--color-info-50);
    }
    .border-info-500 {
        border-color: var(--color-info-500);
    }
    .border-html {
        border-color: var(--color-html);
    }
    .border-css {
        border-color: var(--color-css);
    }
    .border-javascript {
        border-color: var(--color-javascript);
    }
    .border-react {
        border-color: var(--color-react);
    }
    .border-angular {
        border-color: var(--color-angular);
    }
    .border-vue {
        border-color: var(--color-vue);
    }
    .border-node {
        border-color: var(--color-node);
    }
    .border-python {
        border-color: var(--color-python);
    }
    .border-php {
        border-color: var(--color-php);
    }
    .border-java {
        border-color: var(--color-java);
    }
    .border-csharp {
        border-color: var(--color-csharp);
    }
    .border-ruby {
        border-color: var(--color-ruby);
    }
    .border-sass {
        border-color: var(--color-sass);
    }
    .border-bootstrap {
        border-color: var(--color-bootstrap);
    }
    .border-tailwind {
        border-color: var(--color-tailwind);
    }
    .border-webpack {
        border-color: var(--color-webpack);
    }
    .border-typescript {
        border-color: var(--color-typescript);
    }
    .border-graphql {
        border-color: var(--color-graphql);
    }
    .border-flutter {
        border-color: var(--color-flutter);
    }
    .border-swift {
        border-color: var(--color-swift);
    }
    .border-kotlin {
        border-color: var(--color-kotlin);
    }
    .border-ionic {
        border-color: var(--color-ionic);
    }
    .border-xamarin {
        border-color: var(--color-xamarin);
    }
    .border-unity {
        border-color: var(--color-unity);
    }
    .border-nativescript {
        border-color: var(--color-nativescript);
    }
    .border-phonegap {
        border-color: var(--color-phonegap);
    }
    .border-d3 {
        border-color: var(--color-d3);
    }
    .border-jquery {
        border-color: var(--color-jquery);
    }
    .border-accent-blue {
        border-color: var(--color-accent-blue);
    }
    .border-accent-purple {
        border-color: var(--color-accent-purple);
    }
    .border-accent-pink {
        border-color: var(--color-accent-pink);
    }
    .border-accent-orange {
        border-color: var(--color-accent-orange);
    }
    .border-accent-green {
        border-color: var(--color-accent-green);
    }
    .border-accent-cyan {
        border-color: var(--color-accent-cyan);
    }
    .border-transparent {
        border-color: var(--color-transparent);
    }
    .border-current {
        border-color: var(--color-current);
    }
    .border-inherit {
        border-color: var(--color-inherit);
    }
    .border-shadow-light {
        border-color: var(--color-shadow-light);
    }
    .border-shadow-dark {
        border-color: var(--color-shadow-dark);
    }
    .border-overlay-light {
        border-color: var(--color-overlay-light);
    }
    .border-overlay-dark {
        border-color: var(--color-overlay-dark);
    }
}
