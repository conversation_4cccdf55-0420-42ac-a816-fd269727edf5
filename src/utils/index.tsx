// import React from 'react';

// export const renderRichText = (text: string) => {
//     // First split by newline sequences to handle line breaks
//     const lines = text.split(/(\r\n|\n|\r)/);

//     return lines.flatMap((line, lineIndex) => {
//         // Skip empty newline tokens (they'll be handled by <br>)
//         if (/^\r\n|\n|\r$/.test(line)) {
//             return lineIndex < lines.length - 1 ? [<br key={`br-${lineIndex}`} />] : [];
//         }

//         // Process bold tags within each line
//         const segments = line.split(/(<b>.*?<\/b>)/g);

//         return segments.map((segment, segmentIndex) => {
//             if (segment.startsWith('<b>')) {
//                 // Extract content and remove all <b> tags
//                 const content = segment.replace(/<\/?b>/g, '');
//                 return (
//                     <b key={`bold-${lineIndex}-${segmentIndex}`} className="font-bold">
//                         {content}
//                     </b>
//                 );
//             }
//             return (
//                 <React.Fragment key={`text-${lineIndex}-${segmentIndex}`}>{segment}</React.Fragment>
//             );
//         });
//     });
// };

import React from 'react';
import DOMPurify from 'dompurify';

export const renderRichText = (text: string) => {
    // Split by newline characters to preserve line breaks
    const lines = text.split(/(\r\n|\n|\r)/);

    return lines.flatMap((line, lineIndex) => {
        // Handle line breaks
        if (/^\r\n|\n|\r$/.test(line)) {
            return lineIndex < lines.length - 1 ? [<br key={`br-${lineIndex}`} />] : [];
        }

        // Match both <b>...</b> and <span ...>...</span> segments
        const segments = line.split(/(<b>.*?<\/b>|<span.*?>.*?<\/span>)/g);

        return segments.map((segment, segmentIndex) => {
            // Handle <b> tags
            if (segment.startsWith('<b>')) {
                const content = segment.replace(/<\/?b>/g, '');
                return (
                    <span key={`bold-${lineIndex}-${segmentIndex}`} className="font-bold">
                        {content}
                    </span>
                );
            }

            // Handle <span> tags (preserve inner attributes if needed)
            if (segment.startsWith('<span')) {
                // Extract inner text
                const content = segment.replace(/<span.*?>(.*?)<\/span>/, '$1');

                // Optionally extract class name from <span class="...">
                const classMatch = segment.match(/class=["'](.*?)["']/);
                const className = classMatch ? classMatch[1] : '';

                return (
                    <span key={`span-${lineIndex}-${segmentIndex}`} className={className}>
                        {content}
                    </span>
                );
            }

            // Default text
            return (
                <React.Fragment key={`text-${lineIndex}-${segmentIndex}`}>{segment}</React.Fragment>
            );
        });
    });
};

export const renderCleanedHtml = (html: string) => {
    const cleanHTML = DOMPurify.sanitize(html, {
        ALLOWED_TAGS: ['b', 'span', 'br'],
        ALLOWED_ATTR: ['class'],
    });
    return cleanHTML;
};
