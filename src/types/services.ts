export interface Service {
    iconKey: string;
    title: string;
    description: string;
}

export interface ServicesConfig {
    title: string;
    subtitle: string;
    services: Service[];
}

export interface ServiceCard {
    id: string;
    title: string;
    description: string;
    backgroundColor: string;
    icon: {
        type: 'svg';
        paths: string[];
        circles?: Array<{
            cx: number;
            cy: number;
            r: number;
            fill?: string;
            stroke?: string;
            strokeWidth?: number;
        }>;
        ellipses?: Array<{
            cx: number;
            cy: number;
            rx: number;
            ry: number;
            stroke?: string;
            strokeWidth?: number;
            fill?: string;
        }>;
    };
}

export interface DetailedServiceCard {
    id: string;
    title: string;
    description: string;
    backgroundColor: string;
    imageUrl: string;
    imageAlt: string;
    readMoreUrl?: string;
    icon: {
        type: 'svg';
        paths: string[];
        circles?: Array<{
            cx: number;
            cy: number;
            r: number;
            fill?: string;
            stroke?: string;
            strokeWidth?: number;
        }>;
        ellipses?: Array<{
            cx: number;
            cy: number;
            rx: number;
            ry: number;
            stroke?: string;
            strokeWidth?: number;
            fill?: string;
        }>;
    };
}

export interface ServicesData {
    topCards: ServiceCard[];
    bottomCards: DetailedServiceCard[];
}

export interface ServicesShowcaseProps {
    data: ServicesData;
}
