[{"id": 1, "stepNumber": "01", "title": "Ideation and research", "description": "Exploring innovative ideas and conducting market research to identify customer needs and gaps.", "image": "/images/lifecycle/ideation-research.png", "isActive": true}, {"id": 2, "stepNumber": "02", "title": "Concept and wire-framing", "description": "Creating initial concepts and wireframes to visualize the product structure and user experience.", "image": "/images/lifecycle/concept-wireframe.png", "isActive": false}, {"id": 3, "stepNumber": "03", "title": "Develop product functionality", "description": "Building core features and functionality based on validated concepts and user requirements.", "image": "/images/lifecycle/develop-functionality.png", "isActive": false}, {"id": 4, "stepNumber": "04", "title": "Product testing", "description": "Comprehensive testing to ensure quality, performance, and user satisfaction before launch.", "image": "/images/lifecycle/product-testing.png", "isActive": false}, {"id": 5, "stepNumber": "05", "title": "Launch and deployment", "description": "Strategic product launch and deployment with ongoing monitoring and support.", "image": "/images/lifecycle/launch-deployment.png", "isActive": false}]