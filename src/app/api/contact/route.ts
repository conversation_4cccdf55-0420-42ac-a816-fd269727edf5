import { promises as fs } from 'fs';
import path from 'path';

interface Contact {
    name: string;
    email: string;
    message: string;
    service: string;
    phone: string;
    createdAt: string;
}

export async function POST(request: Request) {
    try {
        const body = await request.json();

        const filePath = path.join(process.cwd(), 'src/data/ContactData.json');

        let data: Contact[] = [];
        try {
            const file = await fs.readFile(filePath, 'utf-8');
            data = JSON.parse(file);
        } catch (error) {
            console.log(error);
            data = [];
        }

        data.push({ ...body, createdAt: new Date().toISOString() });

        await fs.writeFile(filePath, JSON.stringify(data, null, 2));

        return new Response(JSON.stringify({ success: true }), { status: 200 });
    } catch (error) {
        console.error('Error writing file:', error);
        return new Response(JSON.stringify({ error: 'Failed to save data' }), { status: 500 });
    }
}
