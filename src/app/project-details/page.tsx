'use client';
import Image from 'next/image';
import projectData from '@/data/projectDetails.json';
import { useState } from 'react';
import CustomCarousel from '@/components/comman/CustomCarousel';
import FAQComponent from '@/components/comman/FAQpage';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import { getIconByKey } from '@/components/icons';
import TestimonialSection from '@/components/comman/Testimonial';
import Breadcrumb from '@/components/comman/Breadcrumb';
import Link from 'next/link';

export default function ProjectDetailsPage() {
    const {
        heroButtons,
        techStack,
        portfolioDetails,
        overview,
        services,
        quoteForm,
        resultArchived,
        images,
    } = projectData;

    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        message: '',
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Form Submitted:', formData);
    };

    const FacebookIcon = getIconByKey('facebookIcon');
    const LinkedinIcon = getIconByKey('linkedinIcon');
    const InstagramIcon = getIconByKey('instagramIcon');
    const UpArrow = getIconByKey('upArrow');

    return (
        <div className="font-outfit mx-auto max-w-7xl">
            <section className="w-full bg-black px-4 py-10 text-white md:px-6 md:py-16">
                <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-2">
                    {/* Left Side Content */}
                    <div className="flex max-w-3xl flex-col justify-center space-y-6">
                        {/* <div className="flex items-center space-x-2 text-sm text-gray-300">
                            <button>Home</button>
                            <span>{'>'}</span>
                            <button>Portfolio</button>
                            <span>{'>'}</span>
                            <button className="font-semibold text-white">Project Details</button>
                        </div> */}
                        <span className="flex justify-start">
                            <Breadcrumb />
                        </span>

                        <h2 className="text-3xl leading-snug md:text-3xl lg:text-4xl">
                            An <span className="text-[#1AF40E]">Grocery Platform</span> in Kuwait
                            that delivers fresh produce and other essential groceries.
                        </h2>

                        <div className="flex flex-wrap gap-3">
                            {heroButtons.map((btn, index) => (
                                <Link
                                    key={index}
                                    href={btn.link}
                                    className="cursor-pointer rounded-full bg-gray-700/60 px-4 py-2 text-sm text-white transition-all duration-200 hover:bg-gray-600/60"
                                >
                                    {btn.label}
                                </Link>
                            ))}
                        </div>
                    </div>

                    {/* Right Side Image */}
                    <div className="flex h-70 justify-end md:h-full">
                        <Image
                            src="/images/home_go_fresh.png"
                            alt="Grocery Platform"
                            width={400}
                            height={200}
                            className="shadow-md"
                        />
                    </div>
                </div>
            </section>

            <section className="w-full bg-[#FFF7F5] px-6 py-10 md:py-16">
                <div className="mx-auto flex max-w-6xl flex-col items-center justify-center text-center text-[var(--color-primary-900)]">
                    {/* Title */}
                    <h2 className="max-w-2xs text-4xl md:max-w-lg">
                        Technologies{' '}
                        <span className="font-bold text-[var(--color-primary-900)]">
                            Stack Used
                        </span>
                    </h2>
                    <p className="mx-auto mt-4 max-w-4xl text-2xl">
                        Our solutions are built using Link robust technology stack, ensuring
                        efficiency, scalability, and seamless performance to deliver the best
                        experience for our users.
                    </p>
                    {/* Tech Grid */}
                    <div className="mt-12 grid grid-cols-2 gap-6 md:grid-cols-6 md:gap-3 lg:gap-6">
                        {techStack.map((tech, index) => {
                            const Icon = getIconByKey(tech.icon);
                            return (
                                <div
                                    key={index}
                                    className="flex flex-col items-center justify-center rounded-lg bg-[#FDECE5] p-6"
                                >
                                    {Icon && (
                                        <Icon width="20" height="20" fill="var(--color-black)" />
                                    )}

                                    <p className="mt-3 font-medium text-gray-800">
                                        {tech.name.toUpperCase()}
                                    </p>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </section>

            <div className="mx-auto max-w-6xl">
                <div className="my-6 flex flex-col items-center justify-center text-center text-[var(--color-primary-900)] lg:flex-row lg:justify-between lg:text-start">
                    <h2 className="flex flex-col py-4 text-4xl leading-tight font-medium lg:text-5xl">
                        How We Brought
                        <span className="font-bold">This Project to Life</span>
                    </h2>
                    <p className="max-w-2xl text-2xl">
                        We put your ideas and thus your wishes in the form of Link unique web
                        project that inspires you and your customers.
                    </p>
                </div>

                {/* ----------------------------------- */}
                <div className="flex flex-col gap-6 py-4 md:items-center lg:flex lg:flex-row lg:items-start">
                    <div className="max-w-3xl px-4 md:px-8">
                        {/* Top Section with Image & Portfolio Details */}
                        <section className="bg-white">
                            <div className="">
                                {/* Left Image */}
                                <div className="md:col-span-2">
                                    <Image
                                        src={portfolioDetails.image}
                                        alt="Portfolio"
                                        width={800}
                                        height={500}
                                        className="rounded-lg shadow-md"
                                    />
                                </div>
                            </div>
                        </section>

                        {/* Overview */}
                        <section className="py-4 lg:py-6">
                            <h2 className="font-outfit text-3xl font-semibold text-[var(--color-primary-900)]">
                                {overview.title}
                            </h2>
                            <p className="mt-4 text-gray-700">{overview.description}</p>
                        </section>

                        {/* Services */}
                        <section className="py-4 lg:py-6">
                            <h2 className="font-outfit text-3xl font-semibold text-[var(--color-primary-900)]">
                                {services.title}
                            </h2>
                            <p className="mt-4 text-gray-700">{services.description}</p>

                            <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Services Offered */}
                                <div>
                                    <h3 className="font-outfit mb-4 text-2xl font-semibold text-[var(--color-primary-900)]">
                                        Services Offered
                                    </h3>
                                    <ul className="space-y-2">
                                        {services.servicesOffered.map((item, idx) => {
                                            const RightIcon = getIconByKey('rightIcon');
                                            return (
                                                <li key={idx} className="flex items-center gap-2">
                                                    <RightIcon
                                                        width="16"
                                                        height="16"
                                                        fill="var(--color-black)"
                                                    />{' '}
                                                    {item}
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </div>

                                {/* Our Approach */}
                                <div>
                                    <h3 className="font-outfit mb-4 text-2xl font-semibold text-[var(--color-primary-900)]">
                                        Our Approach
                                    </h3>
                                    <ul className="space-y-2">
                                        {services.ourApproach.map((item, idx) => {
                                            const RightIcon = getIconByKey('rightIcon');
                                            return (
                                                <li key={idx} className="flex items-center gap-2">
                                                    <RightIcon
                                                        width="16"
                                                        height="16"
                                                        fill="#155FFF"
                                                    />{' '}
                                                    {item}
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <div className="py-4">
                            <section className="bg-white text-[var(--color-primary-900)]">
                                <div className="mx-auto max-w-5xl">
                                    {/* Title */}
                                    <h2 className="font-outfit text-3xl font-semibold">
                                        {resultArchived.title}
                                    </h2>

                                    {/* Description */}
                                    <p className="mt-4 text-xl text-gray-700">
                                        {resultArchived.description}
                                    </p>

                                    {/* Stats Grid */}
                                    <div className="mt-10 grid grid-cols-2 gap-4 md:grid-cols-4">
                                        {resultArchived.stats.map((stat, idx) => (
                                            <div
                                                key={idx}
                                                className="relative flex flex-col items-center justify-center py-6"
                                            >
                                                {/* Background Image */}
                                                <Image
                                                    src={resultArchived.detailscountershape}
                                                    alt="Portfolio"
                                                    width={60}
                                                    height={70}
                                                    className="absolute inset-0 top-6"
                                                />

                                                {/* Stat Value */}
                                                <h3 className="font-outfit relative z-10 text-6xl font-bold text-[var(--color-primary-900)]">
                                                    {stat.value}
                                                </h3>

                                                {/* Label */}
                                                <p className="relative z-10 mt-2 font-normal text-black">
                                                    {stat.label}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>

                    {/* Portfolio Details */}
                    <div className="flex flex-col text-[var(--color-primary-900)] md:max-w-150 lg:max-w-110">
                        <div className="rounded-lg bg-[#F4F4F4] px-6 py-8 text-[var(--color-primary-900)]">
                            <h3 className="mb-6 text-2xl font-semibold">Portfolio Details</h3>

                            <ul className="space-y-4 text-sm text-[var(--color-primary-900)]">
                                <li className="flex items-center border-b border-gray-300 pb-4">
                                    <span className="text-xl font-semibold">Created By:</span>
                                    <span className="ml-4 text-xl">
                                        {portfolioDetails.createdBy}
                                    </span>
                                </li>
                                <li className="flex border-b border-gray-300 pb-4 text-xl">
                                    <span className="font-semibold">Category:</span>
                                    <span className="ml-4">{portfolioDetails.category}</span>
                                </li>
                                <li className="flex border-b border-gray-300 pb-4 text-xl">
                                    <span className="font-semibold">Timeframe:</span>
                                    <span className="ml-4">{portfolioDetails.timeframe}</span>
                                </li>
                                <li className="flex border-b border-gray-300 pb-4 text-xl">
                                    <span className="font-semibold">Location:</span>
                                    <span className="ml-4">{portfolioDetails.location}</span>
                                </li>
                                <li className="flex pb-4 text-xl">
                                    <span className="font-semibold">Cost:</span>
                                    <span className="ml-4">{portfolioDetails.cost}</span>
                                </li>
                            </ul>

                            {/* Social Icons */}
                            <div className="mt-6 flex gap-3">
                                <Link
                                    href={portfolioDetails.socials.facebook}
                                    target="_blank"
                                    className="transition-transform duration-200 hover:scale-110 hover:opacity-80"
                                >
                                    <FacebookIcon
                                        width="16"
                                        height="16"
                                        fill="var(--color-black)"
                                    />
                                </Link>
                                <Link
                                    href={portfolioDetails.socials.instagram}
                                    target="_blank"
                                    className="transition-transform duration-200 hover:scale-110 hover:opacity-80"
                                >
                                    <InstagramIcon
                                        width="16"
                                        height="16"
                                        fill="var(--color-black)"
                                    />
                                </Link>
                                <Link
                                    href={portfolioDetails.socials.linkedin}
                                    target="_blank"
                                    className="transition-transform duration-200 hover:scale-110 hover:opacity-80"
                                >
                                    <LinkedinIcon
                                        width="16"
                                        height="16"
                                        fill="var(--color-black)"
                                    />
                                </Link>
                            </div>
                        </div>

                        {/* Quote Form */}
                        <div className="mt-6 rounded-lg bg-[#F4F4F4] p-6">
                            <h2 className="mb-6 text-2xl font-semibold text-[var(--color-primary-900)]">
                                {quoteForm.title}
                            </h2>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                {quoteForm.fields.map((field, idx) =>
                                    field.type === 'textarea' ? (
                                        <textarea
                                            key={idx}
                                            name={field.name}
                                            placeholder={field.placeholder}
                                            className="w-full rounded-md border bg-white px-4 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                                            rows={4}
                                            value={formData[field.name as keyof typeof formData]}
                                            onChange={handleChange}
                                        />
                                    ) : (
                                        <input
                                            key={idx}
                                            type={field.type}
                                            name={field.name}
                                            placeholder={field.placeholder}
                                            className="w-full rounded-md border bg-white px-4 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                                            value={formData[field.name as keyof typeof formData]}
                                            onChange={handleChange}
                                        />
                                    )
                                )}
                                <button
                                    type="submit"
                                    className="flex cursor-pointer items-center justify-center rounded-4xl bg-blue-600 px-4 py-3 font-medium text-white transition hover:bg-blue-700"
                                >
                                    {quoteForm.buttonText}
                                    <UpArrow width="16" height="16" fill="var(--color-black)" />
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {/* -----------------image------------------- */}
            <div className="bg-[#F6FFF4] py-2 pb-15">
                <div className="mx-auto max-w-6xl px-3 md:px-6">
                    <div className="my-15 text-[var(--color-primary-900)]">
                        <h2 className="flex text-4xl leading-tight font-medium md:text-5xl">
                            Website
                            <span className="font-bold">Visual Design</span>
                        </h2>
                        <p className="max-w-xl text-2xl">
                            We put your ideas and thus your wishes in the form of Link unique web
                            project that inspires you and your customers.
                        </p>
                    </div>
                    {/* ---------------- */}
                    <div className="columns-1 gap-8 sm:columns-2 md:columns-3 lg:columns-2">
                        {images.map((img, idx) => (
                            <div key={idx} className="mb-4 break-inside-avoid">
                                <Image
                                    src={img.url}
                                    alt={`masonry-${idx}`}
                                    width={400}
                                    height={300}
                                    className="w-full"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <TestimonialSection />

            <CustomCarousel />
            <FAQComponent />
            <ContactForm />
            <CtaSection />
        </div>
    );
}
