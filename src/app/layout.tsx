import type { Metadata } from 'next';
import { Poppins, Unbounded, Outfit, Fahkwang, Inter } from 'next/font/google';
import './globals.css';
import Header from '@/components/layout/header';
import Footer from '@/components/layout/footer';

const poppins = Poppins({
    variable: '--font-poppins',
    subsets: ['latin'],
    display: 'swap',
    weight: ['400', '600', '700'],
});

const unbounded = Unbounded({
    subsets: ['latin'],
    weight: ['400', '600', '900'],
    variable: '--font-unbounded',
    display: 'swap',
});
const outfit = Outfit({
    subsets: ['latin'],
    variable: '--font-outfit',
    weight: ['100', '300', '400', '700'],
    display: 'swap',
});
const fahkwang = Fahkwang({
    subsets: ['latin'],
    variable: '--font-fahkwang',
    weight: ['400', '600', '700'],
    display: 'swap',
});
const inter = Inter({
    subsets: ['latin'],
    variable: '--font-inter',
    weight: ['400', '600', '700'],
    display: 'swap',
});

export const metadata: Metadata = {
    title: 'Create Next App',
    description: 'Generated by create next app',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${poppins.variable} ${unbounded.variable} ${outfit.variable} ${fahkwang.variable}${inter.variable} m-0 w-full max-w-full overflow-x-hidden p-0`}
            >
                <link rel="icon" href="/images/playstore.png" type="image/png" sizes="32x32" />
                <Header />
                {children}
                <Footer />
            </body>
        </html>
    );
}
