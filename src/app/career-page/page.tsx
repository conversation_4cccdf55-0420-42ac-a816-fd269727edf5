import Image from 'next/image';

import careerData from '@/data/careerPage.json';
import FAQComponent from '@/components/comman/FAQpage';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import { getIconByKey } from '@/components/icons';
import SecondaryHero from '@/components/comman/SecondaryHero';
import Link from 'next/link';
import LeftArrow from '@/components/icons/LeftArrow';
export default function CareerPage() {
    const { hero, benefits, testimonial, joblistings } = careerData;
    // console.log(benefits);
    const QuoteBubble = getIconByKey('quoteBubble');
    return (
        <div>
            <SecondaryHero data={hero} />
            <div className="font-outfit mx-auto max-w-7xl">
                <div className="mx-auto grid max-w-5xl gap-4 py-6 text-center sm:grid-cols-2 md:grid-cols-4 lg:gap-8 lg:py-10">
                    {benefits.map((item, index) => {
                        const Icon = getIconByKey(item.icon);
                        return (
                            <div key={index} className="flex flex-col items-center space-y-2 p-4">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-900 text-xl">
                                    {Icon && (
                                        <Icon width="24" height="24" fill="var(--color-black)" />
                                    )}
                                </div>
                                <h3 className="text-2xl font-semibold md:text-lg">{item.title}</h3>
                                <p className="max-w-[70%] text-lg text-gray-600 md:max-w-lg md:text-sm">
                                    {item.desc}
                                </p>
                            </div>
                        );
                    })}
                </div>

                {/* ---------------------------- */}

                <div className="mx-auto flex max-w-5xl flex-col px-4 py-6 md:flex-row md:gap-10 md:px-6 lg:gap-18 lg:py-12">
                    <div className="relative">
                        <Image
                            src={testimonial.image}
                            alt={testimonial.name}
                            width={400}
                            height={450}
                            className="rounded-lg object-cover"
                            style={{
                                clipPath: 'polygon(0 0, 100% 0, 100% 85%, 0 100%)',
                            }}
                        />
                        {/* Quote bubble */}{' '}
                        <div className="absolute bottom-35 left-65 flex h-13 w-13 items-center justify-center rounded-full bg-[#1DB0FF] text-5xl text-white md:bottom-44 md:left-35 md:h-10 md:w-10 lg:bottom-35 lg:left-75 lg:h-13 lg:w-13">
                            <QuoteBubble width="24" height="24" fill="var(--color-black)" />
                        </div>
                        <div className="ml-4 max-w-xs lg:ml-6">
                            <blockquote className="text-md mx-w-xs mt-2 text-gray-500">
                                “{testimonial.quote}“
                            </blockquote>
                            <div className="mt-2">
                                <span className="font-semibold">{testimonial.name}</span>
                                <div className="text-sm text-gray-500">{testimonial.role}</div>
                            </div>
                        </div>
                    </div>

                    <div className="max-w-md py-5">
                        <p className="text-md font-semibold tracking-wide text-[#1DB0FF] uppercase md:text-xs">
                            {testimonial.sectionTitle}
                        </p>
                        <h2 className="mt-4 max-w-xs text-4xl font-bold">{testimonial.headline}</h2>
                        <p className="mt-4">{testimonial.description}</p>
                    </div>
                </div>

                {/* ---------------------------------- */}

                <div className="mx-auto flex max-w-7xl flex-col items-center py-12">
                    <h2 className="font-semebold text-center text-5xl">
                        Current <span className="font-bold">Opportunities</span>
                    </h2>
                    <p className="max-w- py-6 text-center text-2xl">
                        We’re a truly global team with 17 offices around the world.
                    </p>

                    <div className="max-w-7xl divide-y divide-gray-200 px-4">
                        {joblistings.map((job, index) => {
                            const LocationPin = getIconByKey('locationPin');

                            return (
                                <div
                                    key={index}
                                    className="flex flex-col py-8 md:flex-row md:items-center md:gap-10 lg:gap-15"
                                >
                                    <div className="flex-1">
                                        <h3 className="text-2xl font-semibold md:text-xl">
                                            {job.title}
                                        </h3>
                                        <p className="md:text-md text-lg text-gray-700">
                                            {job.description}
                                        </p>
                                    </div>
                                    <div className="flex w-45 gap-4 py-6 md:flex-1 md:gap-6 lg:flex-1 lg:gap-8">
                                        <div className="flex text-left lg:w-40">
                                            <h4 className="text-lg font-semibold">{job.team}</h4>
                                        </div>

                                        <div className="flex items-center justify-center gap-2 lg:w-40">
                                            <LocationPin
                                                width="24"
                                                height="24"
                                                fill="var(--color-black)"
                                            />
                                            <h4 className="text-lg font-semibold">
                                                {job.location}
                                            </h4>
                                        </div>
                                    </div>

                                    <div>
                                        <Link
                                            href="#"
                                            className="flex w-full items-center justify-center gap-1 rounded-md bg-gray-100 px-8 py-3 text-lg font-medium hover:bg-gray-200"
                                        >
                                            <span>
                                                <LeftArrow className="h-5 w-5" />
                                            </span>
                                            <span>Apply</span>
                                        </Link>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                <FAQComponent />
                <ContactForm />
                <CtaSection />
            </div>
        </div>
    );
}
