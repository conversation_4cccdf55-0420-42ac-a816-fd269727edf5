'use client';

import BrandAndPartndersShowcase from '@/components/comman/BrandAndPArtnersShowcase';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import CustomCarousel from '@/components/comman/CustomCarousel';
import FAQComponent from '@/components/comman/FAQpage';
import LatestArticles from '@/components/comman/LatestarticalSection';
import CardCarousel from '@/components/comman/PortfolioCardCarousel';
import SecondaryHero from '@/components/comman/SecondaryHero';
// import TechnologyShowcase from '@/components/comman/TechnologyShowcase';
import TestimonialSection from '@/components/comman/Testimonial';
import React, { useState } from 'react';

export default function PortfolioPage() {
    const categories = [
        'All',
        'Mobile Apps',
        'Case Study',
        'Websites',
        'Software',
        'UI/UX',
        'E Commerce',
    ];

    const data = [
        {
            title: 'Case Study',
            categories: ['Case Study', 'Mockup'],
            image: '/images/case_study.png',
            link: '/case-study',
        },
        {
            title: 'World’s Relays',
            categories: ['Software', 'Campaign'],
            image: '/images/card_bg.svg',
            link: '/worlds-relays',
        },
        {
            title: 'Websites',
            categories: ['Creative', 'Websites'],
            image: '/images/web_dev.png',
            link: '/dinamica',
        },
        {
            title: 'New Concept',
            categories: ['Branding', 'UI/UX'],
            image: '/images/card_bg.svg',
            link: '/new-concept',
        },
    ];

    const [active, setActive] = useState(categories[0]);

    const handleClick = (category: string) => {
        setActive(category);
    };

    return (
        <div className="">
            <SecondaryHero
                data={{
                    title: 'Showcasing Our Work\na portfolio of creativity and expertise',
                    description:
                        'Discover Our Expertise: A Showcase of Innovative Projects and Solutions Delivered by Acelan Technologies',
                    primaryButtonText: 'Book Consultation',
                    primaryButtonLink: 'consult-us',
                    serviceReference: 'e-commerce',
                    secondaryButtonText: 'Get a Quote',
                    secondaryButtonLink: '/get-a-quote',
                    imageSrc: '/images/e-com.png',
                    imageAlt: 'eCommerce',
                    backgroundImage: '/images/hero_banner.png',
                }}
            />

            <div className="flex flex-col items-center space-y-6">
                {/* Title */}
                <div className="font-outfit text-center text-[var(--color-primary-900)]">
                    <h2 className="text-5xl font-bold">Our Recent Work</h2>
                    <p className="mt-2 max-w-xl text-xl font-normal">
                        We put your ideas and thus your wishes in the form of a unique web project
                        that inspires you and your customers.
                    </p>
                </div>

                {/* Tabs */}
                <div className="relative flex max-w-[22.75rem] items-start rounded-full bg-[#1E2040] px-4 py-1 md:max-w-2xl lg:max-w-3xl">
                    <div className="z-20 flex-shrink-0">
                        <button
                            onClick={() => handleClick(categories[0])}
                            className={`rounded-full px-5 py-2 text-sm font-semibold transition-all ${
                                active === categories[0]
                                    ? 'bg-gradient-to-r from-blue-500 to-blue-800 text-white shadow-md'
                                    : 'text-white hover:text-blue-300'
                            }`}
                        >
                            {categories[0]}
                        </button>
                    </div>

                    {/* Scroll bar*/}
                    <div className="scrollbar-hide ml-2 flex space-x-2 overflow-x-auto md:space-x-3">
                        {categories.slice(1).map((category) => (
                            <button
                                key={category}
                                onClick={() => handleClick(category)}
                                className={`flex-shrink-0 rounded-full px-2 py-2 text-sm font-semibold transition-all md:px-5 ${
                                    active === category
                                        ? 'bg-gradient-to-r from-blue-500 to-blue-800 text-white shadow-md'
                                        : 'text-white hover:text-blue-300'
                                }`}
                            >
                                {category}
                            </button>
                        ))}
                        <div className="w-4 flex-shrink-0 md:hidden" />
                    </div>
                </div>
            </div>

            <CardCarousel
                data={
                    active === 'All'
                        ? data
                        : data.filter((item) => item.categories.includes(active))
                }
            />

            <CustomCarousel />

            <BrandAndPartndersShowcase />
            <TestimonialSection />
            <LatestArticles />
            {/* <TechnologyShowcase /> */}
            <FAQComponent />

            <ContactForm />
            <CtaSection />
        </div>
    );
}
