@import 'tailwindcss';

:root {
    background-color: var(--color-white);
    /* ===== STANDARDIZED COLOR SYSTEM ===== */
    /* PRIMARY COLORS */
    --color-primary-50: #eff6ff;
    --color-primary-100: #d7e7ff;
    --color-primary-200: #b9d3ff;
    --color-primary-300: #8bb8ff;
    --color-primary-400: #5a9bff;
    --color-primary-500: #2349c8;
    --color-primary-600: #1a48ca;
    --color-primary-700: #0b63e5;
    --color-primary-800: #001a56;
    --color-primary-900: #091542;

    /* NEUTRAL COLORS */
    --color-white: #ffffff;
    --color-gray-50: #fafafa;
    --color-gray-100: #f4f4f4;
    --color-gray-200: #eeeff1;
    --color-gray-300: #e5e7eb;
    --color-gray-400: #d6d6d6;
    --color-gray-500: #dfdfdf;
    --color-gray-600: #333333;
    --color-gray-700: #545454;
    --color-gray-800: #303030;
    --color-gray-900: #282828;
    --color-black-50: #1a1a1a;
    --color-black-100: #171717;
    --color-black-200: #151515;
    --color-black-300: #111111;
    --color-black: #000000;

    /* BACKGROUND COLORS */
    --color-bg-primary: var(--color-white);
    --color-bg-secondary: #fff9f9;
    --color-bg-tertiary: #faf7f7;
    --color-bg-accent: #fef2ed;
    --color-bg-dark: #050a2f;
    --color-bg-dark-secondary: #0b1b40;
    --color-bg-dark-tertiary: #020617;
    --color-bg-gradient-start: #0b1b40;
    --color-bg-gradient-end: #020617;

    /* SEMANTIC COLORS */
    --color-success-50: #f2fff4;
    --color-success-500: #339933;
    --color-warning-50: #fff9e4;
    --color-warning-500: #efeb64;
    --color-error-500: #dd0031;
    --color-info-50: #ede8ff;
    --color-info-500: #3880ff;

    /* TECHNOLOGY BRAND COLORS */
    --color-html: #e34f26;
    --color-css: #1572b6;
    --color-javascript: #f7df1e;
    --color-react: #61dafb;
    --color-angular: #dd0031;
    --color-vue: #4fc08d;
    --color-node: #339933;
    --color-python: #3776ab;
    --color-php: #777bb4;
    --color-java: #007396;
    --color-csharp: #239120;
    --color-ruby: #cc342d;
    --color-sass: #cc6699;
    --color-bootstrap: #7952b3;
    --color-tailwind: #06b6d4;
    --color-webpack: #8dd6f9;
    --color-typescript: #3178c6;
    --color-graphql: #e10098;
    --color-flutter: #02569b;
    --color-swift: #fa7343;
    --color-kotlin: #7f52ff;
    --color-ionic: #3880ff;
    --color-xamarin: #3498db;
    --color-unity: #000000;
    --color-nativescript: #3655ff;
    --color-phonegap: #999999;
    --color-d3: #f68e56;
    --color-jquery: #0769ad;

    /* ACCENT COLORS */
    --color-accent-blue: #156bc1;
    --color-accent-purple: #7f52ff;
    --color-accent-pink: #e535ab;
    --color-accent-orange: #fa7343;
    --color-accent-green: #4fc08d;
    --color-accent-cyan: #06b6d4;

    /* SERVICE CARD COLORS */
    --color-service-ecommerce: #d9d2f3;
    --color-service-qa: #ddd9d6;
    --color-service-seo: #efeb64;
    --color-service-uiux: #c5d1c3;

    /* SPECIAL COLORS */
    --color-transparent: transparent;
    --color-current: currentColor;
    --color-inherit: inherit;
    --color-shadow-light: rgba(255, 255, 255, 0.28);
    --color-shadow-dark: rgba(0, 0, 0, 0.1);
    --color-overlay-light: rgba(255, 255, 255, 0.1);
    --color-overlay-dark: rgba(0, 0, 0, 0.5);
}

/* ------------------- */

/* @import "tailwindcss"; */

@theme {
    /* override existing breakpoints */
    /* --breakpoint-sm: 36rem;  
  --breakpoint-md: 52rem;    
  --breakpoint-lg: 80rem;    */

    /* add new breakpoints if needed */
    --breakpoint-xl: 90rem;
    --breakpoint-3xl: 100rem;

    /* remove a default breakpoint, reset to initial */
    --breakpoint-2xl: initial;
}
/* -------------------- */

@layer theme {
    .font-Poppins {
        font-family: 'Poppins';
    }
    .font-unbounded {
        font-family: 'Unbounded';
    }
    .font-outfit {
        font-family: 'outfit';
    }
    .font-fahkwang {
        font-family: 'fahkwang';
    }
    .font-inter {
        font-family: 'inter';
    }
}

.no-shell footer {
    display: none !important;
}

.text-unbounded {
    font-family: Unbounded;
}
.footer-links {
    color: var(--color-gray-500);
    font-family: Unbounded;
    font-size: 14px;
}
.footer-input {
    display: flex;
    height: 60px;
    padding: 5px 26px;
    justify-content: center;
    align-items: center;
    width: 90%;
    border: 1px solid var(--color-gray-900);
    background: var(--color-black-200);
    font-family: Unbounded;
}
.footer-infobox {
    border-radius: 10px;
    border: var(--stroke-weight-1, 1px) solid var(--color-gray-800);
}

.button {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.button::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -2;
}

.button::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0%;
    height: 100%;
    background-color: blue;
    transition: width 0.3s ease-in-out;
    z-index: -1;
}

.button:hover::before {
    width: 100%;
    left: 0;
    right: auto;
}

.custom-ping {
    animation: custom-ping 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@tailwind utilities;

@layer utilities {
    .wrapper-gap {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    @media (min-width: 1024px) {
        .lg\:wrapper-gap {
            padding-top: 70px;
            padding-bottom: 70px;
        }
    }
}

.wrapper {
    padding: 100px;
}

@keyframes custom-ping {
    100% {
        transform: scale(1);
        opacity: 0.4;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@keyframes bounce-once-smooth {
    0% {
        transform: translateY(-30%);
        opacity: 0.8;
    }
    50% {
        transform: translateY(0);
        opacity: 1;
    }
    70% {
        transform: translateY(-10%);
    }
    100% {
        transform: translateY(0);
    }
}

.bounce-once-smooth {
    animation: bounce-once-smooth 0.8s ease-out both;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }

    .scrollbar-hide {
        -ms-overflow-style: none; /* Internet Explorer 10+ */
        scrollbar-width: none; /* Firefox */
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none; /* Safari and Chrome */
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes progressBar {
    from {
        width: 0%;
    }
    to {
        width: 100%;
    }
}

.animate-progress-bar {
    animation: progressBar 5s linear forwards;
}

html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}



@keyframes windowSlide {
  0% {
    transform: translateX(var(--from-x));
    opacity: 0.6;
  }
  100% {
    transform: translateX(var(--to-x));
    opacity: 0;
  }
}

.animate-window-slide {
  animation: windowSlide 0.6s ease-in-out forwards;
}


