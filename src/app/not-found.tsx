'use client';

import PrimaryBlueButton from '@/components/atoms/PrimaryBlueButton';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect } from 'react';

const NotFound = () => {
    useEffect(() => {
        document.body.classList.add('no-shell');
        document.body.style.overflow = 'hidden'; // Disable scrolling

        return () => {
            document.body.classList.remove('no-shell');
            document.body.style.overflow = ''; // Restore scrolling
        };
    }, []);

    return (
        <div className="mt-6 flex w-screen flex-col items-center justify-center px-4 text-center">
            {/* Title */}
            <h1 className="mb-2 text-4xl font-bold md:text-5xl">404 – Page Not Found</h1>

            {/* Subtitle */}
            <p className="mb-6 text-gray-500">
                The page you’re looking for doesn’t exist or may have been moved.
            </p>

            {/* Image */}
            <div className="relative mb-6">
                <Image
                    src="/images/404-center.png"
                    alt="404 Illustration"
                    width={300}
                    height={300}
                    className="h-[40vh] w-auto"
                />
            </div>

            {/* Buttons */}
            <div className="mt-6 flex items-center justify-center gap-4 md:mt-12">
                <PrimaryBlueButton primaryButtonLink={'/'}>Go Back to Home Page</PrimaryBlueButton>
                <Link
                    href="/contact-us"
                    className="rounded-md bg-gray-200 px-6 py-3 text-gray-700 shadow transition hover:bg-gray-300"
                >
                    Contact Us
                </Link>
            </div>
        </div>
    );
};

export default NotFound;
