'use client';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import FAQComponent from '@/components/comman/FAQpage';
import LatestArticles from '@/components/comman/LatestarticalSection';
import SecondaryHero from '@/components/comman/SecondaryHero';
import acelanBlogsData from '@/data/acelanBlogsData.json';
import Image from 'next/image';
import { useState } from 'react';

export default function Page() {
    const [formData, setFormData] = useState({
        fullName: '',
        email: '',
        contact: '',
        budget: '',
        about: '',
        agree: false,
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        const checked = (e.target as HTMLInputElement).checked;

        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? checked : value,
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log(formData);
        alert('Form submitted successfully!');
    };

    return (
        <div className="mx-auto max-w-7xl">
            <SecondaryHero data={acelanBlogsData.hero} />

            <div className="font-outfit mt-5 border-[#CCC] md:mt-10 md:flex md:border-t lg:mt-15 lg:px-12">
                {/* ------------------left--------------- */}
                <div className="sticky top-0 mb-6 h-fit w-full self-start bg-white px-6 md:max-w-[12rem] md:px-0 md:py-6 md:pl-6 lg:max-w-[15rem]">
                    <h2 className="mb-3 text-2xl font-bold text-gray-900 md:text-lg">
                        {acelanBlogsData.title}
                    </h2>
                    <ul className="max-w-2xs list-outside list-disc space-y-2 px-4 text-xl text-gray-700 md:max-w-xl md:text-sm lg:text-base">
                        {acelanBlogsData.items.map((item, index) => (
                            <li
                                key={index}
                                className="transition-colors duration-200 hover:text-blue-600"
                            >
                                <a href={`#${item.name.toLowerCase().replace(/\s+/g, '-')}`}>
                                    {item.name}
                                </a>
                            </li>
                        ))}
                    </ul>
                </div>

                {/* ------------------center--------------- */}
                <div className="flex-1 md:max-h-[calc(100vh-5rem)] md:overflow-y-auto lg:max-h-[calc(100vh-5rem)]">
                    {acelanBlogsData.acelan_blogs.map((item) => (
                        <section
                            key={item.id}
                            id={item.title.toLowerCase().replace(/\s+/g, '-')}
                            className="mx-auto mb-2 max-w-3xl rounded-md bg-[#FBFCFF] p-6 lg:mb-4"
                        >
                            {/* Image */}
                            <div className="mb-6 w-full overflow-hidden">
                                <Image
                                    src={item.image}
                                    alt={item.title}
                                    width={1000}
                                    height={600}
                                    className="h-auto w-full object-cover"
                                />
                            </div>

                            {/* Content */}
                            <div>
                                <h2 className="mb-3 text-xl font-bold text-gray-900 md:text-2xl">
                                    {item.title}
                                </h2>
                                <p className="mb-4 leading-relaxed text-gray-700">
                                    {item.description}
                                </p>
                                <h3 className="mb-2 font-semibold text-gray-900">
                                    {item.subheading}
                                </h3>
                                <ul className="list-outside list-disc space-y-1 px-4 text-gray-700">
                                    {item.points.map((point, index) => (
                                        <li key={index}>{point}</li>
                                    ))}
                                </ul>
                            </div>
                        </section>
                    ))}
                </div>

                {/* ------------------right--------------- */}
                <div className="sticky top-0 h-fit w-full self-start bg-white px-6 md:max-w-[12rem] md:px-6 md:py-6 lg:max-w-[15rem]">
                    <h2 className="mb-4 max-w-[12rem] text-2xl text-gray-900">
                        Let’s talk about your project!
                    </h2>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            {/* <label className="mb-1 block text-sm font-medium text-gray-700">
                                Full Name*
                            </label> */}
                            <input
                                type="text"
                                name="fullName"
                                value={formData.fullName}
                                onChange={handleChange}
                                placeholder="Full Name"
                                className="w-full border border-gray-300 px-3 py-2 outline-none focus:border-blue-500 focus:ring focus:ring-blue-200"
                                required
                            />
                        </div>

                        <div>
                            {/* <label className="mb-1 block text-sm font-medium text-gray-700">
                                Email Address*
                            </label> */}
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                placeholder="Email"
                                className="w-full border border-gray-300 px-3 py-2 outline-none focus:border-blue-500 focus:ring focus:ring-blue-200"
                                required
                            />
                        </div>

                        <div>
                            {/* <label className="mb-1 block text-sm font-medium text-gray-700">
                                Contact Number*
                            </label> */}
                            <input
                                type="text"
                                name="contact"
                                value={formData.contact}
                                onChange={handleChange}
                                placeholder="Contact Number"
                                className="w-full border border-gray-300 px-3 py-2 outline-none focus:border-blue-500 focus:ring focus:ring-blue-200"
                                required
                            />
                        </div>

                        <div>
                            {/* <label className="mb-1 block text-sm font-medium text-gray-700">
                                Budget*
                            </label> */}
                            <input
                                type="text"
                                name="budget"
                                value={formData.budget}
                                onChange={handleChange}
                                placeholder="Budget"
                                className="w-full border border-gray-300 px-3 py-2 outline-none focus:border-blue-500 focus:ring focus:ring-blue-200"
                                required
                            />
                        </div>

                        <div>
                            {/* <label className="mb-1 block text-sm font-medium text-gray-700">
                                About Project*
                            </label> */}
                            <textarea
                                name="about"
                                value={formData.about}
                                onChange={handleChange}
                                placeholder="About Project"
                                className="w-full border border-gray-300 px-3 py-2 outline-none focus:border-blue-500 focus:ring focus:ring-blue-200"
                                rows={3}
                                required
                            />
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                name="agree"
                                checked={formData.agree}
                                onChange={handleChange}
                                className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                                required
                            />
                            <label className="text-sm">I agree to the privacy policy</label>
                        </div>

                        <button
                            type="submit"
                            className="flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 py-2 font-medium text-white transition-all duration-200 hover:bg-blue-700"
                        >
                            Submit Now
                        </button>
                    </form>
                </div>
            </div>

            <LatestArticles />
            <FAQComponent />
            <ContactForm />
            <CtaSection />
        </div>
    );
}
