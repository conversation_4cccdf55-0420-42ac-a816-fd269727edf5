import Image from 'next/image';
import React from 'react';
import acelanBlogsData from '@/data/caseStudyData.json';
import { getIconByKey } from '@/components/icons';
import { renderRichText } from '@/utils';
import ClientSection from '@/components/comman/ClientSection';
import CatWorkingSvg from '@/components/icons/CatWorkingSvg';
import WorriedWorkingCat from '@/components/icons/WorriedWorkingCat';

const { hero, overview, sections, decisions, empathyData } = acelanBlogsData;

const Page = () => {
    const LaMercerLogo = getIconByKey(hero.LaMercerLogo);
    const AcelanBlogSvg = getIconByKey(hero.AcelanBlogSvg);
    const AcelanBlogSvg2 = getIconByKey(hero.AcelanBlogSvg2);
    const CatSvgCaseStudy = getIconByKey(overview.CatSvgCaseStudy);

    const { title, brand, empathySections } = empathyData;
    const sectionEntries = Object.entries(empathySections);
    return (
        <div className="mx-auto max-w-7xl">
            <div className="inset-0 mx-auto max-w-7xl bg-[linear-gradient(to_right,rgb(235,235,235)_1px,transparent_1px),linear-gradient(to_bottom,rgba(235,235,235)_1px,transparent_1px)] bg-[size:40px_40px] px-6 lg:px-12 lg:pt-12 lg:pb-6">
                <div className="flex justify-center">
                    {/* ---- Title ---- */}
                    <div className="flex flex-col">
                        <div className="flex items-end justify-between py-6 md:py-0">
                            <div className="mb-12 flex h-[2rem] w-[15rem] md:h-[5rem] md:w-[20rem] lg:w-[28rem]">
                                {LaMercerLogo ? (
                                    <LaMercerLogo
                                        width="100"
                                        height="130"
                                        fill="var(--color-black)"
                                        className="mb-3"
                                    />
                                ) : (
                                    <div className="mb-3 text-sm text-red-500">Missing icon</div>
                                )}
                            </div>
                            <div className="h-[6rem] w-[5rem] md:hidden">
                                {AcelanBlogSvg ? (
                                    <AcelanBlogSvg
                                        width="300"
                                        height="300"
                                        fill="var(--color-black)"
                                        className="h-full w-full"
                                    />
                                ) : (
                                    <div className="mb-3 text-sm text-red-500">Missing icon</div>
                                )}
                            </div>
                        </div>

                        <h1 className="font-inter text-2xl md:text-3xl lg:text-5xl">
                            {/* {hero.title} */}
                            {renderRichText(hero.title)}
                        </h1>
                    </div>
                    <div className="hidden h-auto md:flex md:w-xl lg:w-full">
                        {AcelanBlogSvg ? (
                            <AcelanBlogSvg
                                width="300"
                                height="300"
                                fill="var(--color-black)"
                                className="w-full"
                            />
                        ) : (
                            <div className="mb-3 text-sm text-red-500">Missing icon</div>
                        )}
                    </div>
                </div>

                {/* ---- Brand Section ---- */}
                <div className="flex flex-col md:flex-row">
                    <div className="relative bottom-0 flex items-center justify-end md:h-[16rem] md:w-xs lg:flex lg:h-[25rem] lg:w-2xl">
                        {AcelanBlogSvg2 ? (
                            <AcelanBlogSvg2
                                width="60"
                                height="40"
                                fill="var(--color-black)"
                                className="absolute mt-7 h-[5rem] w-[5rem] md:mt-0 md:h-full md:w-full"
                            />
                        ) : (
                            <div className="mb-3 text-sm text-red-500">Missing icon</div>
                        )}
                    </div>
                    <div className="mt-12 mb-6 md:mt-0 md:mb-0">
                        <Image
                            src={`/images/${hero.image}`}
                            // width={40}
                            // height={20}
                            alt="case_study image"
                            width={200}
                            height={100}
                            quality={100}
                            className="h-auto w-100 object-contain md:w-100 lg:w-200"
                            unoptimized
                        />
                    </div>
                </div>
            </div>

            {/* ----------------------------------- */}

            <section className="wrapper-gap lg:wrapper-gap relative inset-0 bg-[#012A26] bg-[linear-gradient(to_right,rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:40px_40px] px-6 pt-12 lg:px-12">
                <div className="flex flex-col-reverse justify-between md:mb-5 md:flex-row">
                    <div className='" flex flex-col items-center justify-between md:w-xs md:items-start lg:h-[100vh]'>
                        <h3 className="hidden text-center text-3xl font-medium text-[#1AF40E] md:flex lg:text-4xl">
                            {overview.title}
                        </h3>
                        {/* Right image section */}
                        <div className="pt-10 md:pt-0 lg:mb-15">
                            {CatSvgCaseStudy ? (
                                <CatSvgCaseStudy
                                    width="40"
                                    height="40"
                                    fill="var(--color-black)"
                                    className="h-[20rem] w-[15rem] md:h-[15rem] md:w-[12rem] lg:h-full lg:w-full"
                                />
                            ) : (
                                <div className="mb-3 text-sm text-red-500">Missing icon</div>
                            )}
                        </div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10 flex flex-col items-start justify-center md:flex-row md:items-start lg:gap-10">
                        {/* Left text section */}

                        <div className="max-w-3xl text-white">
                            <h3 className="flex text-center text-2xl font-medium text-[#1AF40E] md:hidden md:text-3xl lg:text-4xl">
                                {overview.title}
                            </h3>
                            <p className="mb-5 text-2xl leading-relaxed md:text-3xl lg:text-4xl">
                                {overview.description1}{' '}
                                <span className="font-semibold text-[#1AF40E]">
                                    {overview.highlight}
                                </span>
                            </p>

                            <p className="text-2xl leading-relaxed md:text-3xl lg:text-4xl">
                                {overview.description2}
                            </p>
                        </div>
                    </div>
                </div>

                {/* ============================================== */}

                <div className="mb-10">
                    {Object.keys(sections).map((pageKey) => (
                        <ClientSection key={pageKey} pageData={sections[pageKey]} />
                    ))}
                </div>

                {/* ==================================== */}
                <div className="min-h-screen bg-[url('/images/grid-bg.png')] bg-cover bg-center font-sans text-white">
                    <div className="flex justify-between md:items-center">
                        {/* Title */}
                        <h2 className="text-2xl md:mb-6 md:text-3xl lg:mb-0">
                            {title.split(':')[0]}:<span className="text-[#1AF40E]"> {brand}</span>
                        </h2>
                        <div>
                            <WorriedWorkingCat className="h-auto w-[8rem] lg:w-[15rem]" />
                        </div>
                    </div>

                    {/* Grid */}
                    <div className="w-full">
                        <div className="grid gap-8 md:grid-cols-2 md:border-t md:border-[#1AF40E]">
                            {sectionEntries.map(([sectionTitle, items], index) => (
                                <React.Fragment key={sectionTitle}>
                                    {/* Section */}
                                    <div>
                                        <h3 className="mb-2 text-3xl text-[#1AF40E]">
                                            {sectionTitle}
                                        </h3>
                                        <ul className="space-y-2 text-lg leading-normal font-extralight">
                                            {items.map((item, i) => (
                                                <li key={i} className="text-gray-200">
                                                    {sectionTitle === 'Thinks' ||
                                                    sectionTitle === 'Says'
                                                        ? `“${item}”`
                                                        : item}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>

                                    {/* Insert Logo after every 2 sections */}
                                    {(index + 1) % 2 === 0 &&
                                        index !== sectionEntries.length - 1 && (
                                            <div className="flex items-center justify-center md:col-span-2">
                                                <div className="flex h-auto w-[15rem] items-end">
                                                    {LaMercerLogo ? (
                                                        <LaMercerLogo
                                                            width="20"
                                                            height="7"
                                                            fill="var(--color-black)"
                                                            className="flex"
                                                        />
                                                    ) : (
                                                        <div className="mb-3 text-sm text-red-500">
                                                            Missing icon
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                </React.Fragment>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* ------------------------------- */}

            <div className="font-inter wrapper-gap lg:wrapper-gap inset-0 mx-auto max-w-7xl bg-[linear-gradient(to_right,rgb(235,235,235)_1px,transparent_1px),linear-gradient(to_bottom,rgba(235,235,235)_1px,transparent_1px)] bg-[size:40px_40px] px-6 lg:px-12 lg:pt-12 lg:pb-1">
                <div className="max-w-xl">
                    <h2 className="pb-5 text-2xl text-[#EB0A7F] md:text-3xl lg:pb-10">
                        {decisions.title}
                    </h2>
                    <p className="text-2xl text-[#313030] md:text-3xl">{decisions.description}</p>
                </div>
                {/* ---- Brand Section ---- */}
                <div className="flex flex-col md:flex-row">
                    <div className="bottom-0 flex items-end py-5 md:w-xl md:justify-center lg:h-[23rem] lg:w-2xl lg:py-0">
                        {CatWorkingSvg ? (
                            <CatWorkingSvg className="h-auto w-[8rem] md:w-[10rem]" />
                        ) : (
                            <div className="mb- text-sm text-red-500">Missing icon</div>
                        )}
                    </div>
                    <div>
                        <Image
                            src={`/images/${decisions.image}`}
                            // width={40}
                            // height={20}
                            alt="case_study image"
                            width={200}
                            height={200}
                            quality={100}
                            className="h-auto w-200 object-contain"
                            unoptimized
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Page;
