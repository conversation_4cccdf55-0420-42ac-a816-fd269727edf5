import ContactForm from '@/components/comman/contactForm';
import FAQComponent from '@/components/comman/FAQpage';
import { getIconByKey } from '@/components/icons';
import data from '@/data/careerOverview.json';
import Link from 'next/link';

export default function Page() {
    const { about, help, qualities, requirements } = data;
    const Icon = getIconByKey('rightIcon');
    const ContactIcon = getIconByKey('contactIcon');
    const MessageIcon = getIconByKey('messageIcon');

    return (
        <div>
            {/* Header */}
            <div className="w-full bg-gradient-to-r from-[#0a1a6e] to-[#123baf] px-6 py-15 text-white">
                <div className="flex max-w-5xl flex-col items-center justify-center gap-6 lg:mx-auto lg:flex-row lg:items-center lg:justify-between">
                    {/* Left Content */}
                    <div className="flex flex-col items-center lg:items-start lg:justify-start">
                        {/* Breadcrumb */}
                        <div className="mb-3 flex items-center gap-2 text-sm text-gray-200 lg:items-start">
                            <span className="cursor-pointer hover:underline">Home</span>
                            <span className="cursor-pointer hover:underline">Careers</span>
                            <span className="font-semibold text-white">Career Overview</span>
                        </div>

                        {/* Job Title */}
                        <h1 className="text-4xl font-bold">Full Stack Engineer</h1>
                        <p className="mt-2 text-gray-200">Indore, India • Full time</p>
                    </div>

                    {/* Right Buttons */}
                    <div className="flex items-center gap-3">
                        <button className="cursor-pointer rounded-md bg-white/20 px-6 py-2 text-base text-white transition hover:bg-white/30">
                            Refer a friend
                        </button>
                        <button className="cursor-pointer rounded-md bg-blue-600 px-6 py-2 text-base text-white transition hover:bg-blue-700">
                            Apply Now
                        </button>
                    </div>
                </div>
            </div>

            {/* About & Help Section */}
            <div className="mx-auto grid w-full max-w-5xl grid-cols-1 gap-15 px-6 py-12 lg:grid-cols-3">
                {/* Left Side */}
                <div className="w-[80%] space-y-8 lg:col-span-2">
                    <div>
                        <h2 className="text-xl font-bold text-[#0a0a3a]">{about.whoWeAreTitle}</h2>
                        <p className="mt-3 leading-relaxed text-gray-700">{about.whoWeAreText}</p>
                    </div>

                    <div>
                        <h2 className="text-xl font-bold text-[#0a0a3a]">
                            {about.whatLookingForTitle}
                        </h2>
                        <p className="mt-3 leading-relaxed text-gray-700">
                            {about.whatLookingForText}
                        </p>
                    </div>
                </div>

                {/* Right Side - Help Card */}
                <div className="hidden space-y-4 rounded-xl bg-[#150742] p-6 text-white lg:block">
                    <h3 className="text-lg font-semibold">{help.title}</h3>
                    <p className="text-sm leading-relaxed text-gray-300">{help.description}</p>

                    {/* Phone */}
                    <div className="mt-12 flex items-center gap-2">
                        <ContactIcon width="16" height="16" fill="var(--color-black)" />
                        <Link href={`tel:${help.phone}`} className="text-white hover:underline">
                            {help.phone}
                        </Link>
                    </div>

                    {/* Email */}
                    <div className="mt-5 flex items-center gap-2">
                        <MessageIcon width="16" height="16" fill="var(--color-black)" />
                        <Link href={`mailto:${help.email}`} className="text-white hover:underline">
                            {help.email}
                        </Link>
                    </div>
                </div>
            </div>

            <div className="mx-auto mb-10 w-full max-w-5xl items-start space-y-8 px-6 py-1">
                {/* Qualities Section */}
                <div className="space-y-4">
                    {qualities.map((quality, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                            <Icon
                                bgColor="var(--color-primary-900)"
                                width="16"
                                height="16"
                                fill="var(--color-black)"
                            />
                            {/* <CheckCircle className="mt-1 h-5 w-5 text-[#0a1a6e]" /> */}
                            <p className="max-w-2xs text-gray-800 md:max-w-lg">{quality}</p>
                        </div>
                    ))}
                </div>

                {/* Requirements Section */}
                <div>
                    <h2 className="mb-6 text-xl font-bold text-[#0a0a3a]">{requirements.title}</h2>
                    <div className="space-y-4">
                        {requirements.items.map((req, idx) => {
                            return (
                                <div key={idx} className="flex items-center gap-3">
                                    <span>
                                        <Icon
                                            bgColor="var(--color-primary-900)"
                                            width="16"
                                            height="16"
                                            fill="var(--color-black)"
                                        />
                                    </span>
                                    <p className="text-gray-800">{req}</p>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>

            {/*  Help Card */}
            <div className="mx-4 mb-6 block space-y-4 rounded-xl bg-[#150742] p-6 text-white md:max-w-xs lg:hidden">
                <h3 className="text-lg font-semibold">{help.title}</h3>
                <p className="text-sm leading-relaxed text-gray-300">{help.description}</p>

                {/* Phone */}
                <div className="mt-12 flex items-center gap-2">
                    <ContactIcon width="16" height="16" fill="var(--color-black)" />
                    <Link href={`tel:${help.phone}`} className="text-white hover:underline">
                        {help.phone}
                    </Link>
                </div>

                {/* Email */}
                <div className="mt-5 flex items-center gap-2">
                    <MessageIcon width="16" height="16" fill="var(--color-black)" />
                    <Link href={`mailto:${help.email}`} className="text-white hover:underline">
                        {help.email}
                    </Link>
                </div>
            </div>

            <ContactForm />
            <FAQComponent />
            {/* <CtaSection /> */}
        </div>
    );
}
