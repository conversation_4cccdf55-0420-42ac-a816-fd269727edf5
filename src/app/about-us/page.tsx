import SecondaryHero from '@/components/comman/SecondaryHero';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import LatestArticles from '@/components/comman/LatestarticalSection';
import StrategyComparison from '@/components/comman/StrategyComparison';
import TechnologyShowcase from '@/components/comman/TechnologyShowcase';
import InfoAndFeedback from '@/components/comman/InfoAndFeedback';
import HeroFeedback from '@/components/comman/HeroFeedback';
import MissionVisionGoals from '@/components/comman/MissionVisionGoals';
import aboutUsData from '@/data/aboutUsData.json';
import FAQComponent from '@/components/comman/FAQpage';

export default function Page() {
    return (
        <>
            <SecondaryHero data={aboutUsData.hero} />
            {/* <BrandAndPartndersShowcase /> */}
            <StrategyComparison />
            <InfoAndFeedback />
            <HeroFeedback />
            <MissionVisionGoals />
            {/* <CompanyMindset /> */}
            <TechnologyShowcase />
            <LatestArticles />
            {/* <IndustryShowcase /> */}
            {/* <ProductLifecycle /> */}
            {/* <StrategyComparison /> */}
            {/* <LatestArticles /> */}
            {/* <DigitalSolutions /> */}

            <FAQComponent />
            <ContactForm />
            <CtaSection />
        </>
    );
}
