import React from 'react';
import privacyPolicyData from '@/data/privacyPolicy.json';
import SecondaryHero from '@/components/comman/SecondaryHero';
import ContactForm from '@/components/comman/contactForm';

const PrivacyPolicyPage = () => {
    const { hero, privacyData } = privacyPolicyData;

    return (
        <>
            <SecondaryHero data={hero} />

            <div className="mx-auto max-w-3xl px-4 py-8 text-[var(--color-primary-900)] md:max-w-2xl lg:max-w-4xl">
                {/* Sections */}
                <div className="space-y-10">
                    {privacyData.sections.map((section) => (
                        <section key={section.id} className="mb-6">
                            <h2 className="pb-2 text-lg font-semibold text-gray-900 sm:text-xl md:text-2xl">
                                {section.title}
                            </h2>

                            {/* Nested items */}
                            {section.type === 'nested' && section.items && (
                                <div>
                                    <h3 className="md:text-md mb-4 text-gray-800">
                                        {section.subtitle}
                                    </h3>
                                    <ul className="list-disc space-y-2 marker:text-xs">
                                        {section.items.map((item, idx) => (
                                            <li
                                                key={idx}
                                                className="ml-4 list-disc text-sm leading-tight text-gray-700 sm:text-base md:text-lg"
                                            >
                                                {item.text}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            {section.type === 'list' && section.items && (
                                <div>
                                    <h3 className="text-md mb-4 text-gray-800 sm:text-lg">
                                        {section.subtitle}
                                    </h3>
                                    <ul className="list-disc space-y-2 pl-5 text-sm text-gray-700 marker:text-xs sm:text-base md:text-lg">
                                        {section.items.map((item, idx) => (
                                            <li className="leading-none" key={idx}>
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            {section.type === 'cookies' && section.items && (
                                <div>
                                    <h3 className="text-md mb-4 text-gray-800 sm:text-lg">
                                        {section.subtitle}
                                    </h3>
                                    <ul className="list-disc space-y-2 pl-5 text-sm text-gray-700 marker:text-xs sm:text-base md:text-lg">
                                        {section.items.map((item, idx) => (
                                            <li className="leading-none" key={idx}>
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                            {section.content && !section.type && (
                                <div className="space-y-4">
                                    {section.content.map((paragraph, idx) => (
                                        <p
                                            key={idx}
                                            className="text-sm leading-6 text-gray-700 sm:text-base md:text-lg md:leading-7"
                                        >
                                            {paragraph}
                                        </p>
                                    ))}
                                </div>
                            )}
                            {section.type === 'rights' && section.items && (
                                <div>
                                    <h3 className="text-md mb-4 text-gray-800 sm:text-lg">
                                        {section.subtitle}
                                    </h3>
                                    <ul className="flex list-disc flex-col space-y-2 pl-5 text-sm text-gray-700 marker:text-xs sm:text-base md:text-lg">
                                        {section.items.map((item, idx) => (
                                            <li className="leading-none" key={idx}>
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </section>
                    ))}
                </div>
            </div>

            <ContactForm />
        </>
    );
};

export default PrivacyPolicyPage;
