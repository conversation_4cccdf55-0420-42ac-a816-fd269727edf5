'use client';
import BlogCarousel from '@/components/comman/BlogCarousel';
import SecondaryHero from '@/components/comman/SecondaryHero';
import blogData from '@/data/blogData.json';
import React, { useState, useMemo, useEffect, useRef } from 'react';
import Image from 'next/image';
import LatestArticles from '@/components/comman/LatestarticalSection';
import ContactForm from '@/components/comman/contactForm';
import SkeletonLoader from '@/components/comman/SkeletonLoader';
import FAQComponent from '@/components/comman/FAQpage';
import CtaSection from '@/components/comman/CTASection';
import SearchIcon from '@/components/icons/SerchIcon';
import CustomArrow from '@/components/icons/CustomArrow';
import PaginationNextIcon from '@/components/icons/PaginationNextIcon';
import PaginationPreveIcon from '@/components/icons/PaginationPreveIcon';

type Blog = {
    id: number;
    title: string;
    description: string;
    image: string;
    date: string;
    category: string;
};

export default function BlogPage() {
    const [carouselLoading, setCarouselLoading] = useState<boolean>(true);
    useEffect(() => {
        setCarouselLoading(false);
    }, []);

    const [search, setSearch] = useState<string>('');
    const [category, setCategory] = useState<string>('All');
    const [currentPage, setCurrentPage] = useState<number>(1);

    const blogs: Blog[] = blogData.blogs;
    const categories: string[] = ['All', ...new Set(blogs.map((b) => b.category))];
    const blogsPerPage: number = 6;

    const filteredBlogs = useMemo(() => {
        return blogs.filter((blog) => {
            const matchesSearch =
                blog.title.toLowerCase().includes(search.toLowerCase()) ||
                blog.description.toLowerCase().includes(search.toLowerCase());

            const matchesCategory = category === 'All' || blog.category === category;
            return matchesSearch && matchesCategory;
        });
    }, [blogs, search, category]);

    const totalPages = Math.ceil(filteredBlogs.length / blogsPerPage);
    const startIndex = (currentPage - 1) * blogsPerPage;
    const currentBlogs = filteredBlogs.slice(startIndex, startIndex + blogsPerPage);

    const { hero } = blogData;

    const blogGridRef = useRef<HTMLDivElement>(null);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        if (blogGridRef.current) {
            blogGridRef.current.scrollIntoView({ behavior: 'auto', block: 'start' });
        }
    };

    return (
        <main>
            <SecondaryHero data={hero} />

            {carouselLoading ? <SkeletonLoader /> : <BlogCarousel />}

            <div>
                <div className="mx-auto flex max-w-7xl flex-col px-4 md:flex-row md:justify-between">
                    {/* Header */}
                    <div className="text-center md:max-w-[45%] md:text-start">
                        <h2 className="text-2xl md:text-3xl lg:text-4xl">
                            All Blog <span className="text-black-600 font-bold">and Insights</span>
                        </h2>
                        <p className="mt-4 px-4 text-lg md:px-0 md:text-xl">
                            Read our thoughts and insights on the latest tech and business trends.
                        </p>
                    </div>

                    {/* Filters */}
                    <div className="mx-4 flex flex-col items-end justify-end gap-2 py-4 md:mx-0 md:flex-row md:py-0">
                        {/* Search Box */}
                        <div className="relative flex w-full items-center md:w-auto">
                            <span className="flex h-[2.8125rem] w-full items-center rounded-lg border border-[var(--color-primary-900)] px-2 text-start md:h-[30px] md:w-[12rem] lg:h-auto lg:w-[15rem]">
                                <SearchIcon
                                    width={32}
                                    height={32}
                                    className="absolute left-1 w-4 lg:w-5"
                                />
                                <input
                                    type="text"
                                    placeholder="Search here"
                                    value={search}
                                    onChange={(e) => {
                                        setSearch(e.target.value);
                                        setCurrentPage(1);
                                    }}
                                    className="w-full rounded-md border-none px-4 py-2.5 text-xs placeholder-[var(--color-primary-900)] focus:outline-none lg:px-6 lg:text-lg"
                                />
                            </span>
                        </div>

                        {/* Dropdown */}
                        <div className="relative w-full">
                            <select
                                value={category}
                                onChange={(e) => {
                                    setCategory(e.target.value);
                                    setCurrentPage(1);
                                }}
                                className="h-[2.8125rem] w-full appearance-none truncate overflow-hidden rounded-md border px-2 text-xs text-ellipsis whitespace-nowrap focus:outline-none md:h-[30px] md:w-[10rem] lg:h-auto lg:w-[10rem] lg:px-3 lg:py-2.5 lg:text-lg"
                            >
                                {categories.map((cat) => (
                                    <option key={cat} value={cat}>
                                        {cat}
                                    </option>
                                ))}
                            </select>
                            <span className="pointer-events-none absolute inset-y-0 right-1 flex items-center text-gray-500">
                                <CustomArrow />
                            </span>
                        </div>
                    </div>
                </div>

                {/* Blog Grid */}
                <div ref={blogGridRef} className="mx-auto max-w-7xl py-10 md:px-15">
                    {currentBlogs.length === 0 ? (
                        <p className="text-center text-lg text-gray-600">
                            No blogs found for your search.
                        </p>
                    ) : (
                        <>
                            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                                {currentBlogs.map((blog) => (
                                    <div
                                        key={blog.id}
                                        className="flex flex-col items-center justify-center overflow-hidden rounded-lg bg-white transition-shadow hover:shadow-md"
                                    >
                                        <Image
                                            src={blog.image}
                                            alt={blog.title}
                                            width={600}
                                            height={300}
                                            className="h-48 w-[90%] object-cover"
                                        />
                                        <div className="p-4">
                                            <h3 className="my-4 mt-2 text-lg font-bold">
                                                {blog.title}
                                            </h3>
                                            <div className="flex items-center gap-3">
                                                <p className="text-xs font-bold">{blog.date}</p>
                                                <span className="inline-block bg-[#0F31B1] px-2 py-1 text-xs text-white">
                                                    {blog.category}
                                                </span>
                                            </div>
                                            <p className="mt-2 line-clamp-3 text-sm text-gray-600">
                                                {blog.description}
                                            </p>
                                            <button className="mt-3 cursor-pointer font-medium text-blue-600 hover:underline">
                                                Read More
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Pagination */}
                            <div className="mt-8 flex space-x-2">
                                <button
                                    type="button"
                                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1}
                                    className="cursor-pointer rounded-md border px-3 py-1 disabled:opacity-50"
                                >
                                    <PaginationPreveIcon />
                                </button>

                                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                    <button
                                        key={page}
                                        type="button"
                                        onClick={() => handlePageChange(page)}
                                        className={`cursor-pointer rounded-md border px-3 py-1 ${
                                            currentPage === page ? 'bg-black text-white' : ''
                                        }`}
                                    >
                                        {page}
                                    </button>
                                ))}

                                <button
                                    type="button"
                                    onClick={() =>
                                        handlePageChange(Math.min(totalPages, currentPage + 1))
                                    }
                                    disabled={currentPage === totalPages}
                                    className="cursor-pointer rounded-md border px-3 py-1 disabled:opacity-50"
                                >
                                    <PaginationNextIcon />
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </div>

            <LatestArticles />
            <FAQComponent />
            <ContactForm />
            <CtaSection />
        </main>
    );
}
