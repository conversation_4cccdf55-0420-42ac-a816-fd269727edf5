import BrandAndPartndersShowcase from '@/components/comman/BrandAndPArtnersShowcase';
import React from 'react';
import servicesData from '@/data/services.json';
import SecondaryHero from '@/components/comman/SecondaryHero';
import ServicesShowcase from '@/components/comman/ServicesShowcase';
import DigitalSolutions from '@/components/comman/DigitalSolutions';
import IndustriesSection from '@/components/comman/IndustriesSection';
import IndustryShowcase from '@/components/comman/IndustryShowcase';
import TrustSection from '@/components/comman/TrustSection';
import ProductLifecycle from '@/components/comman/ProductLifecycle';
import CustomCarousel from '@/components/comman/CustomCarousel';
import TechnologyShowcase from '@/components/comman/TechnologyShowcase';
import LatestArticles from '@/components/comman/LatestarticalSection';
import FAQComponent from '@/components/comman/FAQpage';
import ContactForm from '@/components/comman/contactForm';
import CtaSection from '@/components/comman/CTASection';
import BestAtApp from '@/components/comman/BestAtApp';

const page = () => {
    return (
        <div>
            <SecondaryHero data={servicesData['_'].hero} />
            <BrandAndPartndersShowcase />
            <ServicesShowcase showcaseData={servicesData['_'].showcase} />

            <DigitalSolutions />
            <IndustriesSection />
            <IndustryShowcase />
            <BestAtApp data={servicesData['_'].why} />

            <TrustSection />
            <ProductLifecycle />
            <CustomCarousel />
            <TechnologyShowcase />
            <LatestArticles />
            <FAQComponent />
            <ContactForm />
            <CtaSection />
        </div>
    );
};

export default page;
