'use client';

import { notFound } from 'next/navigation';
import { useParams } from 'next/navigation';
import servicesData from '@/data/services.json';
import ServicesHero from '@/components/comman/ServicesHero';
import ServiceDetails from '@/components/comman/ServiceDetails';
import ServiceWhy from '@/components/comman/ServiceWhy';
import CustomCarousel from '@/components/comman/CustomCarousel';
import ProductLifecycle from '@/components/comman/ProductLifecycle';
import CompanyMindset from '@/components/comman/CompanyMindset';
import BestAtApp from '@/components/comman/BestAtApp';
import ContactForm from '@/components/comman/contactForm';
import TestimonialSection from '@/components/comman/Testimonial';
import CTASection from '@/components/comman/CTASection';
import TechnologyShowcase from '@/components/comman/TechnologyShowcase';
import IndustryShowcase from '@/components/comman/IndustryShowcase';
import LatestArticles from '@/components/comman/LatestarticalSection';
import FAQComponent from '@/components/comman/FAQpage';
import IndustriesSection from '@/components/comman/IndustriesSection';
import CallToAction from '@/components/comman/CallToAction';
import StatsSection from '@/components/comman/StatsSection';

const DynamicServicePage = () => {
    const { slug } = useParams();
    const serviceData = servicesData[slug as string];

    if (!serviceData) return notFound();

    return (
        <div>
            <ServicesHero data={serviceData.hero} />
            <ServiceDetails data={serviceData.section} />
            {serviceData.why && slug !== 'app-dev' ? (
                <ServiceWhy data={serviceData.why} />
            ) : (
                <BestAtApp data={serviceData.why} />
            )}

            {serviceData.carousel && <CustomCarousel />}
            {serviceData.productLifecycle && <ProductLifecycle />}
            {serviceData.companyMindset && <CompanyMindset />}
            {}
            <CallToAction />
            <IndustriesSection />
            <IndustryShowcase />
            <CustomCarousel />
            <ProductLifecycle />
            <CompanyMindset />
            <StatsSection />

            <TechnologyShowcase />
            <TestimonialSection />
            <LatestArticles />
            <FAQComponent />
            <ContactForm />
            <CTASection />
        </div>
    );
};

export default DynamicServicePage;
