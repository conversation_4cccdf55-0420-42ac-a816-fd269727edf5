import Link from 'next/link';
import React from 'react';

const infoData = [
    {
        header: 'Vision Driven innovation',
        description:
            "We have an ambitious road ahead, and we're looking for people to join our global team.",
    },
    {
        header: 'Seamless experiences',
        description:
            "We have an ambitious road ahead, and we're looking for people to join our global team.",
    },
    {
        header: 'Built On Trust',
        description:
            'We believe debt + equity capital should be thoughtfully customized, not commoditized.',
    },
    {
        header: 'Beyond Expectation',
        description:
            'Admins and regular users boost Hello with on-demand learning for all proficiency levels.',
    },
];

// --- <PERSON> now takes index prop instead of icon.
const Card = ({
    header,
    description,
    index,
}: {
    header: string;
    description: string;
    index: number;
}) => (
    <div className="h-full w-full border-l border-[var(--color-gray-300)] bg-transparent p-6 text-left first:border-l-0">
        {/* Index Box */}
        <div className="">
            <div className="h-8 w-8 text-xs font-bold">
                {(index + 1).toString().padStart(2, '0')}
            </div>
        </div>

        <div className="mb-2 max-w-[15rem] text-lg font-bold tracking-wide text-[#121212]">
            {header}
        </div>

        <div className="max-w-[15rem] text-sm text-[#353751]">{description}</div>

        <div className="mt-10">
            <Link className="text-sm font-medium underline underline-offset-2" href={'/'}>
                Read more
            </Link>
        </div>
    </div>
);

const CompanyMindset = () => {
    return (
        <div className="wrapper-gap lg:wrapper-gap bg-[#EFF6FF]">
            <div className="mx-auto max-w-7xl">
                {/* Header */}
                <section className="mb-4 flex flex-col justify-between px-6 md:flex-row md:px-12 md:text-start">
                    <span className="font-outfit mb-4 text-3xl text-[var(--color-primary-900)] md:mb-0 lg:text-5xl">
                        Why Acelan is the
                        <div className="font-bold">Right choice for you</div>
                    </span>
                    <div className="max-w-xs text-[#333] md:text-start lg:max-w-lg">
                        <span className="text-lg font-medium text-wrap">
                            Acelan combines innovation, expertise, and a client-first approach to
                            deliver seamless solutions that drive success and growth.
                        </span>
                    </div>
                </section>

                {/* Cards Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:px-12 lg:grid-cols-4">
                    {infoData.map((item, idx) => (
                        <Card key={item.header} {...item} index={idx} />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CompanyMindset;
