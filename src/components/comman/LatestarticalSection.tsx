import Image from 'next/image';
import LastestArticalsData from '@/data/LastestArticalsData.json';
import ExploreMore from '../icons/ExploreMore';
import AriticleTag from '../icons/AriticleTag';
import DateIcon from '../icons/DateIcon';
import Link from 'next/link';

export default function LatestArticles() {
    return (
        <section className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl px-6 lg:px-12">
            <div className="mb:5 flex flex-col items-start md:mb-8 md:flex-row md:justify-between">
                <div>
                    <h2 className="text-4xl text-[var(--color-primary-900)] lg:text-5xl">
                        <span className="text-unbounded font-medium">What’s New</span>
                    </h2>
                    <p className="font-fahkwang pt-2 text-4xl tracking-wider md:pt-4 lg:text-5xl">
                        At Acelan
                    </p>
                </div>
                <div className="mt-6 w-[450px] max-w-7xl md:mt-0 md:max-w-xs">
                    <h3 className="text-unbounded text-lg font-semibold text-[var(--color-black-50)]">
                        Explore more
                        <span className="ml-1 inline-block">
                            <ExploreMore />
                        </span>
                    </h3>
                    <p className="font-outfit mb-6 max-w-xs py-2 text-base/5 text-gray-600">
                        Services to help businesses establish and enhance theironline presence.
                    </p>
                </div>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
                {LastestArticalsData.map((article) => (
                    <div key={article.id} className="overflow-hidden bg-white shadow-sm">
                        <div className="relative h-56 w-full md:max-h-40 lg:max-h-50">
                            <Image
                                src={article.image}
                                alt={article.title}
                                fill
                                className="object-cover"
                            />
                        </div>
                        <div className="px-2 py-4 lg:px-7">
                            <div className="font-outfit flex w-2xs items-center justify-between text-xs text-[var(--color-gray-700)] md:w-full lg:max-w-xs lg:gap-4">
                                <span className="inline-flex items-center lg:py-4 lg:text-lg">
                                    <AriticleTag />

                                    {article.category}
                                </span>
                                <span className="inline-flex items-center lg:text-lg">
                                    <DateIcon />

                                    {article.date}
                                </span>
                            </div>
                            <h4 className="lg:max-2xs mt-2 line-clamp-2 max-w-xs text-lg font-semibold text-[var(--color-black-50)] lg:max-w-xs lg:text-xl">
                                {article.title}
                            </h4>
                            <Link
                                href="#"
                                className="font-unbounded mt-4 inline-block text-sm font-semibold text-[var(--color-black-50)]"
                            >
                                Read More
                                <span className="ml-2 inline-block">
                                    <ExploreMore />
                                </span>
                            </Link>
                        </div>
                    </div>
                ))}
            </div>
        </section>
    );
}
