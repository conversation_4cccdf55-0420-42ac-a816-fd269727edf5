'use client';

import Image from 'next/image';
import PrimaryBlueButton from '../atoms/PrimaryBlueButton';
import SecondaryOutlinesButton from '../atoms/SecondaryOutlinesButton';
import Breadcrumb from './Breadcrumb';

const ServicesHero = ({ data }) => {
    const {
        title,
        description,
        primaryButtonText,
        primaryButtonLink,
        secondaryButtonText,
        secondaryButtonLink,
        serviceReference,
        imageSrc,
        imageAlt,
    } = data;

    return (
        <div className="mx-auto w-full bg-[var(--color-bg-dark)] px-6 py-6 lg:px-12 lg:py-12">
            <div className="mx-auto flex max-w-7xl flex-col items-center gap-6 md:flex-row lg:gap-10">
                {/* Left Text Content */}
                <div className="flex-1 text-white lg:w-[100px]">
                    <Breadcrumb />
                    <h1 className="mt-4 text-4xl leading-tight font-bold md:mt-2">{title}</h1>
                    <p className="line-clamp- overflow-hidden py-2 text-base leading-6 text-gray-300 lg:text-lg">
                        {description}
                    </p>

                    <div className="block flex-1 rounded-lg bg-[radial-gradient(circle_at_center,_rgba(8,27,100,0.9)_0%,_rgba(2,8,41,0.8)_65%,_rgba(45,1,80,0.7)_100%)] p-3 md:hidden">
                        <div className="relative aspect-[4/2] h-full w-full">
                            <Image
                                src={imageSrc}
                                alt={imageAlt}
                                fill
                                className="object-cover"
                                priority
                            />
                        </div>
                    </div>

                    <div className="mt-4 flex gap-5 md:mt-8">
                        <PrimaryBlueButton
                            primaryButtonLink={primaryButtonLink}
                            serviceReference={serviceReference}
                            className="px-3 py-2 text-sm"
                        >
                            {primaryButtonText}
                        </PrimaryBlueButton>
                        <SecondaryOutlinesButton
                            secondaryButtonLink={secondaryButtonLink}
                            serviceReference={serviceReference}
                            className="px-3 py-2 text-sm"
                        >
                            {secondaryButtonText}
                        </SecondaryOutlinesButton>
                    </div>
                </div>
                {/* Right Image */}
                <div className="hidden flex-1 rounded-lg bg-[radial-gradient(circle_at_center,_rgba(8,27,100,0.9)_0%,_rgba(2,8,41,0.8)_65%,_rgba(45,1,80,0.7)_100%)] p-3 md:block">
                    <div className="relative aspect-[4/2] w-full">
                        <Image
                            src={imageSrc}
                            alt={imageAlt}
                            fill
                            className="z-10 object-cover"
                            priority
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ServicesHero;
