import React from 'react';
import Link from 'next/link';
// import Image from 'next/image';
import { getIconByKey } from '../icons';

// Define types for the cards
type TopCardProps = {
    id: string;
    title: string;
    description: string;
    backgroundColor: string;
    icon: string;
    servicePath: string;
};

type BottomCardProps = {
    id: string;
    title: string;
    description: string;
    imageUrl: string;
    imageAlt: string;
    servicePath: string;
};

type ShowcaseData = {
    topCards: TopCardProps[];
    bottomCards: BottomCardProps[];
};

interface ServicesShowcaseProps {
    showcaseData: ShowcaseData;
}

// Top Card Component
function TopCard({ title, description, backgroundColor, icon, servicePath }: TopCardProps) {
    const Icon = getIconByKey(icon);
    return (
        <Link href={`/services/${servicePath}`} passHref>
            <div
                className="h-90 cursor-pointer rounded-2xl p-8 transition-all hover:scale-[1.02] hover:shadow-lg"
                style={{ backgroundColor }}
            >
                <div className="mb-8">
                    <div className="mb-6 flex h-20 w-20 items-center justify-center">
                        {Icon && (
                            <Icon
                                width={'10'}
                                height={'10'}
                                fill={'var(--color-white)'}
                                className="h-12 w-12 text-[var(--color-black-50)]"
                            />
                        )}
                    </div>
                </div>
                <h3 className="font-unbounded mb-4 text-2xl font-medium whitespace-pre-line text-[var(--color-black-50)]">
                    {title}
                </h3>
                <p className="text-xl leading-relaxed font-normal text-[var(--color-black-50)]">
                    {description}
                </p>
            </div>
        </Link>
    );
}

export default function ServicesShowcase({ showcaseData }: ServicesShowcaseProps) {
    return (
        <div className="bg-white p-8 py-8">
            <div className="mx-auto max-w-7xl">
                <section className="py-12 text-center">
                    {/* Small Tag */}
                    <div className="font-unbounded inline-block rounded-full border border-gray-300 px-4 py-1 text-sm font-semibold text-gray-700">
                        Our Expertise in
                    </div>

                    {/* Heading */}
                    <h2 className="mt-6 text-3xl leading-snug font-semibold text-black md:text-5xl">
                        Driving excellence through Innovation and Strategy
                    </h2>
                </section>
                {/* Top Service Cards Grid */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {showcaseData.topCards.map((card) => (
                        <TopCard key={card.id} {...card} />
                    ))}
                </div>

                {/* Bottom Service Cards Grid */}
                {/* <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                    {showcaseData.bottomCards.map((card) => (
                        <BottomCard key={card.id} {...card} />
                    ))}
                </div> */}
            </div>
        </div>
    );
}
