import { renderRichText } from '@/utils';
import Breadcrumb from './Breadcrumb';
import PrimaryBlueButton from '../atoms/PrimaryBlueButton';
import SecondaryOutlinesButton from '../atoms/SecondaryOutlinesButton';

const SecondaryHero = ({ data }) => {
    const {
        backgroundImage = '/images/hero_banner.png',
        title,
        description,
        primaryButtonText,
        primaryButtonLink,
        secondaryButtonText,
        secondaryButtonLink,
    } = data;

    return (
        <>
            <div
                className="font-outfit wrapper-gap lg:wrapper-gap flex flex-col items-center justify-center px-6 lg:px-12"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'top',
                }}
            >
                <span className="flex justify-center">
                    <Breadcrumb />
                </span>
                <div className="flex flex-col items-center justify-center">
                    <h1 className="text-center text-2xl font-semibold tracking-wide text-white md:text-4xl lg:text-5xl">
                        {renderRichText(title)}
                    </h1>

                    <p className="pt-4 text-center text-xl text-white md:text-2xl">
                        {renderRichText(description)}
                    </p>

                    <div className="mt-6 flex items-center justify-center gap-4 md:mt-6 lg:mt-8">
                        {primaryButtonText && primaryButtonLink && (
                            <PrimaryBlueButton
                                primaryButtonLink={primaryButtonLink}
                                className="px-2 py-4 text-xs lg:text-sm"
                            >
                                {primaryButtonText}
                            </PrimaryBlueButton>
                        )}

                        {secondaryButtonText && secondaryButtonLink && (
                            <SecondaryOutlinesButton
                                secondaryButtonLink={secondaryButtonLink}
                                className="px-2 py-4 text-xs lg:text-sm"
                            >
                                {secondaryButtonText}
                            </SecondaryOutlinesButton>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};

export default SecondaryHero;
