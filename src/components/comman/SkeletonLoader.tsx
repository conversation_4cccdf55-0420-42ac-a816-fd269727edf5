export default function SkeletonLoader() {
    return (
        <div className="flex">
            <div className="mx-auto w-full max-w-7xl px-6 py-10">
                <div className="flex items-center justify-between gap-6">
                    {/* Left Arrow */}
                    <div className="h-10 w-10 animate-pulse rounded-full bg-gray-200" />

                    {/* Main Card */}
                    <div className="grid flex-1 grid-cols-1 gap-6 md:grid-cols-2">
                        {/* Image Skeleton */}
                        <div className="h-60 w-full animate-pulse rounded-xl bg-gray-200" />

                        {/* Text Skeleton */}
                        <div className="flex flex-col justify-center gap-4">
                            <div className="h-6 w-3/4 animate-pulse rounded bg-gray-200" />
                            <div className="h-4 w-1/3 animate-pulse rounded bg-gray-200" />
                            <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
                            <div className="h-12 w-2/3 animate-pulse rounded bg-gray-200" />
                            <div className="h-8 w-32 animate-pulse rounded bg-gray-300" />
                        </div>
                    </div>
                </div>

                {/* Dots Indicator */}
                <div className="mt-6 flex justify-center gap-3">
                    {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="h-3 w-3 animate-pulse rounded-full bg-gray-300" />
                    ))}
                </div>
            </div>
            <div className="mx-auto w-full max-w-7xl px-6 py-10">
                <div className="flex items-center justify-between gap-6">
                    {/* Main Card */}
                    <div className="grid flex-1 grid-cols-1 gap-6 md:grid-cols-2">
                        {/* Image Skeleton */}
                        <div className="h-60 w-full animate-pulse rounded-xl bg-gray-200" />

                        {/* Text Skeleton */}
                        <div className="flex flex-col justify-center gap-4">
                            <div className="h-6 w-3/4 animate-pulse rounded bg-gray-200" />
                            <div className="h-4 w-1/3 animate-pulse rounded bg-gray-200" />
                            <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
                            <div className="h-12 w-2/3 animate-pulse rounded bg-gray-200" />
                            <div className="h-8 w-32 animate-pulse rounded bg-gray-300" />
                        </div>
                    </div>

                    {/* Right Arrow */}
                    <div className="h-10 w-10 animate-pulse rounded-full bg-gray-200" />
                </div>

                {/* Dots Indicator */}
                <div className="mt-6 flex justify-center gap-3">
                    {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="h-3 w-3 animate-pulse rounded-full bg-gray-300" />
                    ))}
                </div>
            </div>
        </div>
    );
}
