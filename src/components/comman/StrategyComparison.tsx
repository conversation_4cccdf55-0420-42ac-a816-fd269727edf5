import Image from 'next/image';
// import BrandAndPartndersShowcase from './BrandAndPArtnersShowcase';
import StrategyComparisonIcon from '../icons/StrategyComparisonIcon';

export default function StrategyComparison() {
    return (
        <section className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl bg-white px-6 lg:px-12 lg:text-center">
            {/* <BrandAndPartndersShowcase /> */}
            <h2 className="font-outfit pb-6 text-center text-3xl font-light text-[var(--color-primary-900)] md:text-5xl lg:pb-10">
                We compare the
                <span className="text-[var(--color-primary-900) font-bold"> reality</span>
                <br className="hidden md:block" />
                <span className="font-bold text-[var(--color-primary-900)]"> of your strategy</span>
            </h2>

            <div className="font-outfit flex w-full flex-col md:flex-row md:items-center md:justify-center md:gap-6 md:border-gray-200 md:shadow-[0_-4px_2px_-1px_rgba(0,0,0,0.01)]">
                <div className="grid grid-cols-3 justify-center gap-2 border-gray-200 bg-white md:mt-0 md:flex md:flex-row md:items-center lg:gap-6">
                    <div className="flex max-w-[140px] flex-col items-start rounded-md pr-2 md:max-w-xs md:flex-row md:items-center md:gap-2 lg:p-3">
                        <span className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-[#D7E7FF] text-xs font-semibold text-[var(--color-primary-900)] lg:h-8 lg:w-8">
                            01
                        </span>
                        <div className="mt-3 flex text-start text-xs leading-tight text-[var(--color-primary-900)] md:mt-0 lg:text-sm">
                            Benchmark
                            <br />
                            performance against
                            <br />
                            business goals
                        </div>
                        <span className="hidden text-xl text-gray-400 md:inline-block lg:ml-2">
                            <StrategyComparisonIcon />
                        </span>
                    </div>

                    <div className="flex max-w-[140px] flex-col items-start rounded-md pr-2 md:max-w-xs md:flex-row md:items-center md:gap-2 lg:p-3">
                        <span className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-[#D7E7FF] text-xs font-semibold text-[var(--color-primary-900)] lg:h-8 lg:w-8">
                            02
                        </span>
                        <div className="mt-3 flex text-start text-xs leading-tight text-[var(--color-primary-900)] md:mt-0 lg:text-sm">
                            Identify gaps
                            <br />
                            in execution <br /> flow
                        </div>
                        <span className="hidden text-xl text-gray-400 md:inline-block lg:ml-2">
                            <StrategyComparisonIcon />
                        </span>
                    </div>

                    <div className="flex max-w-[140px] flex-col items-start rounded-md pr-2 md:max-w-xs md:flex-row md:items-center md:gap-2 lg:p-3">
                        <span className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-[#D7E7FF] text-xs font-semibold text-[var(--color-primary-900)] lg:h-8 lg:w-8">
                            03
                        </span>
                        <div className="mt-3 flex text-start text-xs leading-tight text-[var(--color-primary-900)] md:mt-0 lg:text-sm">
                            Align actions
                            <br />
                            with strategic <br /> vision
                        </div>

                        <span className="hidden text-xl text-gray-400 md:inline-block lg:ml-2">
                            <StrategyComparisonIcon />
                        </span>
                    </div>
                </div>

                <div className="flex items-center justify-center md:items-center md:justify-center">
                    <div className="flex items-center space-x-3 pt-4 md:border-l md:border-gray-400 md:p-3">
                        {/* Google Logo */}
                        <Image
                            src="/images/google_icon.png"
                            alt="Google"
                            width={40}
                            height={40}
                            className="shrink-0"
                        />

                        {/* Rating + Text */}
                        <div className="flex flex-col">
                            <div className="flex items-center justify-center text-sm font-bold text-gray-800">
                                4.9
                                <span className="ml-1 text-yellow-400">★★★★★</span>
                            </div>
                            <div className="text-xs text-gray-500">Client Reviews</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
