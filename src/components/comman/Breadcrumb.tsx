'use client';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

const Breadcrumb = () => {
    const pathname = usePathname();
    const pathSegments = pathname.split('/').filter(Boolean);

    // Always start with Home
    const crumbs = [
        { name: 'Home', href: '/' },
        ...pathSegments.map((segment, idx) => {
            const href = '/' + pathSegments.slice(0, idx + 1).join('/');
            // Capitalize and replace dashes with spaces
            const name = segment.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
            return { name, href };
        }),
    ];

    return (
        <nav className="breadcrumbs flex py-2 text-sm" aria-label="Breadcrumb">
            <ol className="list-reset flex size-fit rounded-3xl border border-[#136ED3] px-4 py-2 text-gray-400">
                {crumbs.map((crumb, idx) => (
                    <li key={crumb.href} className="flex cursor-pointer items-center">
                        {idx !== 0 && <span className="mx-1">&gt;</span>}
                        {idx !== crumbs.length - 1 ? (
                            <Link href={crumb.href} className="text-grey-800 hover:underline">
                                {crumb.name}
                            </Link>
                        ) : (
                            <span className="text-white">{crumb.name}</span>
                        )}
                    </li>
                ))}
            </ol>
        </nav>
    );
};

export default Breadcrumb;
