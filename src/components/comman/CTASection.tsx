import Image from 'next/image';
import Link from 'next/link';
import ContactArrow from '../icons/ContactArrow';

export default function CtaSection() {
    return (
        <div className="font-outfit mx-auto mb-[50px] max-w-7xl px-6 lg:mb-[70px] lg:px-12">
            <section className="rounded-lg bg-[var(--color-primary-500)] p-6 text-white md:p-10">
                <div className="flex flex-col items-center justify-between gap-6 md:flex-row md:items-start">
                    <div className="flex-1 md:text-left">
                        <p className="text-base font-[400] uppercase">Start Now</p>
                        <h2 className="my-2 text-2xl font-[400] md:my-4 md:text-3xl">
                            Let’s Work <span className="font-bold text-white">Together</span>
                        </h2>
                        <p className="mx-auto mb-5 line-clamp-2 max-w-md text-white md:mx-0">
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus,
                            luctus nec ullamcorper.
                        </p>
                    </div>

                    <div className="flex w-full flex-col items-center md:w-1/3 md:items-end">
                        <Image
                            src="/images/cta_Illustration.png"
                            alt="Illustration"
                            width={300}
                            height={300}
                            className="md:mr-8"
                        />
                    </div>
                </div>
                <div className="mt-4 w-[100%] md:mt-0">
                    <Link href="/contact">
                        <button className="flex w-[100%] cursor-pointer items-center justify-center gap-x-2 rounded-md bg-[var(--color-primary-700)] px-5 py-3 text-center text-white transition duration-300 hover:bg-blue-700 md:w-auto">
                            <span>
                                <ContactArrow />
                            </span>{' '}
                            Request a demo
                        </button>
                    </Link>
                </div>
            </section>
        </div>
    );
}
