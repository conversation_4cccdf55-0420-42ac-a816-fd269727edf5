import { RightArrowIcon } from '../icons';

export default function CallToAction() {
    return (
        <div className="mx-auto px-4 py-12 lg:max-w-6xl">
            <div className="flex flex-col justify-between gap-6 rounded-2xl bg-gradient-to-r from-[var(--color-bg-dark-secondary)] to-[var(--color-bg-dark-tertiary)] px-8 py-10 md:flex-row md:items-center md:gap-0">
                {/* Text Section */}
                <div className="md:max-w-sm md:text-left lg:max-w-2xl">
                    <h2 className="text-2xl font-semibold text-white md:text-3xl lg:text-4xl">
                        Ready to Turn Your Idea into Reality?
                    </h2>
                    <p className="mt-2 max-w-xl py-4 text-xl font-normal text-white">
                        From idea to launch, we build secure, scalable, and user-focused software —
                        web, mobile, or enterprise.
                    </p>
                </div>

                {/* Button Section */}
                <div className="pr-4">
                    <button className="flex cursor-pointer items-center justify-center rounded-lg border-2 border-white px-6 py-3 text-white transition hover:bg-white hover:text-[var(--color-bg-dark-secondary)]">
                        Schedule A Call
                        <RightArrowIcon
                            width="23"
                            height="7"
                            fill="currentColor"
                            className="ml-2 h-4 w-7 transform transition-transform duration-300 group-hover:translate-x-1"
                        />
                        {/* <svg
                            className="ml-2 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M17 8l4 4m0 0l-4 4m4-4H3"
                            />
                        </svg> */}
                    </button>
                </div>
            </div>
        </div>
    );
}
