'use client';

import Image from 'next/image';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import PrimaryBlueButton from '@/components/atoms/PrimaryBlueButton';
import blogData from '@/data/blogData.json';
import NextButton from '../icons/NextButton';
import PrevButton from '../icons/PrevButton';

const { heroImageBlocks } = blogData;
const BlogCarousel = () => {
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [mounted, setMounted] = useState<boolean>(false);
    const [isLoop] = useState<boolean>(true);
    const carouselRef = useRef<HTMLDivElement | null>(null);

    const totalSlides = heroImageBlocks.length;
    const clonedSlides = [
        ...heroImageBlocks.slice(-2),
        ...heroImageBlocks,
        ...heroImageBlocks.slice(0, 2),
    ];

    const goToSlide = useCallback(
        (index: number) => {
            if (isLoop) {
                const realIndex = ((index - 2 + totalSlides) % totalSlides) + 2;
                setCurrentIndex(realIndex);
                setTimeout(() => {
                    if (carouselRef.current) {
                        const slideWidth = carouselRef.current.offsetWidth / 2;
                        carouselRef.current.scrollTo({
                            left: realIndex * slideWidth,
                            behavior: 'smooth',
                        });
                    }
                }, 10);
            } else {
                setCurrentIndex(Math.max(0, Math.min(index, totalSlides - 1)));
            }
        },
        [isLoop, totalSlides]
    );

    const nextSlide = useCallback(() => goToSlide(currentIndex + 1), [goToSlide, currentIndex]);
    const prevSlide = useCallback(() => goToSlide(currentIndex - 1), [goToSlide, currentIndex]);

    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        if (mounted && carouselRef.current) {
            const slideWidth = carouselRef.current.offsetWidth / 2;
            carouselRef.current.scrollTo({
                left: 2 * slideWidth,
                behavior: 'auto',
            });
        }
    }, [mounted]);

    useEffect(() => {
        if (!isLoop || !mounted) return;
        const timer = setInterval(() => {
            nextSlide();
        }, 3000);
        return () => clearInterval(timer);
    }, [currentIndex, isLoop, mounted, nextSlide]);

    useEffect(() => {
        if (mounted && carouselRef.current) {
            const slideWidth = carouselRef.current.offsetWidth / 2;
            carouselRef.current.scrollTo({
                left: currentIndex * slideWidth,
                behavior: 'smooth',
            });
        }
    }, [currentIndex, mounted]);

    if (!mounted) return null;

    const formattedDate = new Intl.DateTimeFormat('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
    }).format(new Date());

    return (
        <div className="mx-auto max-w-7xl">
            {/* Blog Carousel */}
            <div className="relative px-4 py-10">
                <div
                    ref={carouselRef}
                    className="flex snap-x snap-mandatory gap-4 overflow-x-hidden scroll-smooth"
                    style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                    {clonedSlides.map((block, index) => (
                        <div
                            key={`${block.id}-${index}`}
                            className="gap- flex w-full flex-shrink-0 flex-col items-center justify-center bg-[#F1F1F1] p-4 md:w-1/2 lg:w-1/2 lg:flex-row"
                        >
                            {/* Image Section */}
                            <div className="w-full">
                                <Image
                                    src={block.image}
                                    alt={block.title}
                                    width={800}
                                    height={400}
                                    className="h-auto w-full object-cover shadow-lg"
                                />
                            </div>

                            {/* Text Section */}
                            <div className="flex w-full flex-col items-start justify-center space-y-4 text-left md:w-xs lg:w-full lg:pl-4">
                                <h2 className="text-xl font-bold md:text-2xl">{block.title}</h2>
                                <div className="flex items-center gap-3">
                                    <p className="text-xs text-gray-500">{formattedDate}</p>
                                    <PrimaryBlueButton
                                        primaryButtonLink={block.buttonLink}
                                        className="rounded-none bg-[#0F31B1] px-[15px] py-[5px] text-xs"
                                    >
                                        {block.buttonText}
                                    </PrimaryBlueButton>
                                </div>
                                +<p className="font-medium text-gray-600">{block.description}</p>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Prev Button */}
                <button
                    onClick={prevSlide}
                    className={`absolute top-1/2 left-2 flex h-8 w-8 -translate-y-1/2 transform items-center justify-center rounded-full bg-white/20 shadow-md transition-all hover:bg-white/50 sm:left-4 sm:h-10 sm:w-10 ${
                        !isLoop && currentIndex === 0
                            ? 'cursor-not-allowed opacity-50'
                            : 'hover:shadow-lg'
                    }`}
                >
                    <PrevButton />
                </button>

                {/* Next Button */}
                <button
                    onClick={nextSlide}
                    className={`absolute top-1/2 right-2 flex h-8 w-8 -translate-y-1/2 transform items-center justify-center rounded-full bg-white/20 shadow-md transition-all hover:bg-white/70 sm:right-4 sm:h-10 sm:w-10 ${
                        !isLoop && currentIndex === totalSlides - 1
                            ? 'cursor-not-allowed opacity-50'
                            : 'hover:shadow-lg'
                    }`}
                >
                    <NextButton />
                </button>

                {/* Dots */}
                <div className="mt-6 flex justify-center space-x-2">
                    {heroImageBlocks.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`h-3 w-3 cursor-pointer rounded-full transition-all ${
                                index === (currentIndex - 2 + totalSlides) % totalSlides
                                    ? 'bg-blue-500'
                                    : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default BlogCarousel;
