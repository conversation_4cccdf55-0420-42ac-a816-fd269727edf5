import Image from 'next/image';

const BrandAndPartndersShowcase = () => {
    return (
        <div className="mx-auto max-w-7xl px-6 lg:px-12">
            <div className="wrapper-gap lg:wrapper-gap flex max-w-6xl flex-col text-left lg:flex-row lg:justify-between">
                <div className="mb-10 flex gap-4 lg:mb-0">
                    <div className="md:block">
                        <p className="ml-0 h-12 w-0.5 bg-[var(--color-primary-900)]"></p>
                    </div>
                    <div className="text-md font-outfit w-full font-semibold text-[var(--color-black-50)] md:w-auto">
                        Worked with world
                        <br />
                        famous brands and partners
                    </div>
                </div>
                <div className="flex flex-wrap items-center justify-center gap-7">
                    {[
                        'energy_2.png',
                        'fiducia_2.png',
                        'go_fresh_2.png',
                        'eagle_2.png',
                        'la_mercer_2.png',
                        'gosk_2.png',
                        // 'lu_ve_2.png',

                        // 'beka.png',
                        // 'aloha.png',
                        // 'athlete.png',
                        // 'next.png',
                        // 'walmark.png',
                        // 'pilot.png',
                    ].map((logo, i) => (
                        <div key={i} className="flex items-center justify-center">
                            <Image
                                src={`/images/${logo}`}
                                alt={`Brand ${i + 1}`}
                                // width={40}
                                // height={20}
                                width={200}
                                height={100}
                                quality={100}
                                className="h-7 w-auto object-contain"
                            />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
export default BrandAndPartndersShowcase;
