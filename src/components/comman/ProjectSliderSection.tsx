'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

const slides = [
    {
        id: 1,
        title: 'Fresh & Seamless',
        subtitle: 'Our Latest Grocery App Design',
        description:
            'An on-demand grocery delivery app to shop for groceries and get them delivered on your doorstep.',
        image: '/images/user.jpg',
        cta: 'Explore Now',
    },
    {
        id: 2,
        title: 'Smart Shopping',
        subtitle: 'Personalized Recommendations',
        description: 'Get AI-powered suggestions based on your shopping habits and preferences.',
        image: '/images/user.jpg',
        cta: 'Discover More',
    },
    {
        id: 3,
        title: 'Lightning Fast',
        subtitle: 'Delivery in Minutes',
        description:
            'Our optimized delivery network ensures you get your groceries when you need them.',
        image: '/images/user.jpg',
        cta: 'Order Now',
    },
];

export default function ProjectSliderSection() {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(false);
    const [mounted, setMounted] = useState(false);

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
    };

    const goToSlide = (index: number) => {
        setCurrentSlide(index);
    };

    useEffect(() => {
        setMounted(true);
        setIsAutoPlaying(true);
    }, []);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (isAutoPlaying && mounted) {
            interval = setInterval(() => {
                nextSlide();
            }, 5000);
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [currentSlide, isAutoPlaying, mounted]);

    return (
        <main className="container mx-auto px-4 py-8">
            <div
                className="relative h-[500px] w-full overflow-hidden rounded-xl shadow-lg"
                onMouseEnter={() => setIsAutoPlaying(false)}
                onMouseLeave={() => setIsAutoPlaying(true)}
            >
                {/* Slides */}
                <div
                    className="flex transition-transform duration-500 ease-in-out"
                    style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                >
                    {slides.map((slide) => (
                        <div key={slide.id} className="relative w-full flex-shrink-0">
                            {/* Background Image */}
                            <div className="absolute inset-0">
                                <Image
                                    src={slide.image}
                                    alt={slide.title}
                                    fill
                                    style={{ objectFit: 'cover' }}
                                    className="brightness-75"
                                />
                            </div>

                            {/* Content Overlay */}
                            <div className="relative flex h-[500px] max-w-2xl flex-col items-start justify-center p-12 text-white">
                                <h1 className="mb-2 text-4xl font-bold">{slide.title}</h1>
                                <h2 className="mb-4 text-2xl font-semibold">{slide.subtitle}</h2>
                                <p className="mb-6 text-lg">{slide.description}</p>
                                <button className="rounded-full bg-green-500 px-6 py-3 font-medium text-white transition duration-300 hover:bg-green-600">
                                    {slide.cta}
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Navigation Arrows */}
                <button
                    onClick={prevSlide}
                    className="absolute top-1/2 left-4 -translate-y-1/2 rounded-full bg-white/30 p-2 text-white backdrop-blur-sm transition hover:bg-white/50"
                    aria-label="Previous slide"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                        />
                    </svg>
                </button>
                <button
                    onClick={nextSlide}
                    className="absolute top-1/2 right-4 -translate-y-1/2 rounded-full bg-white/30 p-2 text-white backdrop-blur-sm transition hover:bg-white/50"
                    aria-label="Next slide"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                        />
                    </svg>
                </button>

                {/* Dots Indicator */}
                <div className="absolute bottom-6 left-1/2 flex -translate-x-1/2 space-x-2">
                    {slides.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`h-3 w-3 rounded-full transition ${currentSlide === index ? 'w-6 bg-white' : 'bg-white/50'}`}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            </div>
        </main>
    );
}
