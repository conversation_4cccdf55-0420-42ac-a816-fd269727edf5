'use client';
import Link from 'next/link';
import { getIconByKey } from '../icons';
import Image from 'next/image';
import { Dispatch, SetStateAction } from 'react';

type Service = {
    label: string;
    href: string;
    iconKey: string;
};

type ServicesDropdownProps = {
    services: Service[];
    showDropdown?: boolean;
    setShowDropdown?: Dispatch<SetStateAction<boolean>>;
    onClose?: () => void;
};

export default function ServicesDropdown({
    services,
    // showDropdown,
    // setShowDropdown,
    onClose,
}: ServicesDropdownProps) {
    return (
        <div className="mx-auto flex max-w-7xl justify-center px-4">
            <div className="z-50 mr-auto flex transform items-center justify-between gap-8 p-4 px-6 text-white w-full max-w-full">
                {/* Header text */}
                <div className="gap- flex max-w-2xl items-center">
                    <div className="h-16 w-2">
                        <Image
                            src="/images/Rectangle.png"
                            alt="404 Illustration"
                            width={3}
                            height={3}
                            className="h-16 w-auto"
                        />
                    </div>
                    <div className="ml-3 flex flex-col items-start">
                        <h3 className="text-2xl font-normal">Explore Our</h3>
                        <h2 className="text-4xl font-semibold">Services</h2>
                    </div>
                </div>

                {/* Services list */}
                <div className="flex max-w-4xl flex-wrap justify-start gap-4">
                    {services.map((service, index) => {
                        const Icon = getIconByKey(service.iconKey || 'defaultIcon');
                        return (
                            <Link
                                key={index}
                                href={service.href || '/services'}
                                onClick={onClose}
                                className="flex min-w-0 flex-1 items-center gap-4 text-base font-normal transition-all hover:text-blue-300"
                            >
                                {Icon && <Icon width={16} height={16} fill="white" />}
                                <span className="font-normal">{service.label}</span>
                            </Link>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
