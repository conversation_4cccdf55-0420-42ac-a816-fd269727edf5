import Image from 'next/image';
import Reviews from '@/data/testimonialData.json';

const TestimonialSection = () => {
    return (
        <section className="relative overflow-hidden bg-[var(--color-bg-secondary)]">
            <div className="wrapper-gap lg:wrapper-gap mx-auto grid max-w-7xl grid-cols-1 gap-12 px-6 md:grid-cols-2 lg:px-12">
                <div className="font-outfit w-full pb-3 md:pb-0">
                    <h2 className="font-outfit relative z-10 text-5xl leading-none font-thin text-[var(--color-primary-900)] lg:text-5xl">
                        What
                        <br />
                        <span className="font-bold">client’s say</span>
                        <br />
                        <span>about us</span>
                    </h2>
                    <Image
                        src="/images/testimonial_logo.png"
                        alt="Testimonial Logo"
                        width={300}
                        height={250}
                        className="absolute top-39 ml-25 flex h-auto w-full max-w-[250px] md:relative md:top-0 md:mt-4 md:ml-1 md:max-w-[300px] lg:max-w-[350px]"
                    />
                </div>

                <div className="font-outfit text-[var(--color-primary-900)]">
                    <span className="mb-4 inline-block rounded-full bg-gray-100 px-4 py-1 text-sm text-gray-600">
                        {Reviews[0].tag}
                    </span>

                    <p className="mb-6 text-base leading-relaxed md:text-lg">
                        {Reviews[0].reviewMessage}
                    </p>

                    <div className="mb-4">
                        <p className="text-base font-semibold">{Reviews[0].clientName}</p>
                        <p className="text-sm">{Reviews[0].position}</p>
                    </div>

                    <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                            <Image
                                key={i}
                                src={Reviews[0].avatar}
                                alt={`Avatar ${i}`}
                                width={40}
                                height={40}
                                className="rounded-full border-2 border-white"
                            />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default TestimonialSection;
