import Image from 'next/image';

export default function StatsSection() {
    const stats = [
        {
            value: '98%',
            label: 'Project Success Rate',
            icon: '/images/detailscountershapeIcon.png',
        },
        {
            value: '10+',
            label: 'Years of Experience',
            icon: '/images/details_counter_shape_icon.png',
        },
        {
            value: '16k+',
            label: 'Projects Delivered',
            icon: '/images/details_counter_shape_icon.png',
        },
        {
            value: '70+',
            label: 'Experienced Professional',
            icon: '/images/details_counter_shape_icon.png',
        },
    ];

    return (
        <div className="font-outfit lg:wrapper-gap mx-auto max-w-7xl lg:px-12">
            <div className="mx-auto grid max-w-6xl overflow-hidden bg-gradient-to-r from-[var(--color-bg-dark-secondary)] to-[var(--color-bg-dark-tertiary)] py-5 text-white sm:grid-cols-4 md:grid-cols-2 lg:grid-cols-4 lg:rounded-2xl">
                {stats.map((stat, idx) => (
                    <div
                        key={idx}
                        className="relative flex flex-col px-12 py-10 md:items-center md:justify-center"
                    >
                        {/* Background Shape Icon */}
                        <Image
                            src={stat.icon}
                            alt={stat.label}
                            width={60}
                            height={70}
                            className="absolute inset-0 top-6 left-6 md:left-25 lg:left-20"
                        />

                        {/* Foreground Content */}
                        <div className="relative z-10 flex flex-col">
                            <span className="text-5xl font-bold md:text-5xl">{stat.value}</span>
                            <span className="md:text-normal mt-2 text-xs font-normal">
                                {stat.label}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
