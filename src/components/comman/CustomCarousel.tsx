'use client';
import Image from 'next/image';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import PrevButton from '../icons/PrevButton';
import NextButton from '../icons/NextButton';
import { RightArrowIcon } from '../icons';

export default function CustomCarousel() {
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [isLoop] = useState<boolean>(true);
    const [mounted, setMounted] = useState<boolean>(false);
    const carouselRef = useRef<HTMLDivElement | null>(null);

    const slides = [
        {
            id: 1,
            color: 'bg-[#271251]',
            logo: '/images/logo_slider_2.png',
            title: 'Fresh & Seamless',
            subtitle: 'Our Latest Grocery App Design',
            description:
                'An on-demand grocery delivery app to shop for groceries and get them delivered on your doorstep.',
            image: '/images/project_slider_2.png',
            cta: 'Explore Now',
        },
        {
            id: 2,
            color: 'bg-[#271251]',
            logo: '/images/logo_slider_1.png',
            title: 'Smart Shopping',
            subtitle: 'Personalized Recommendations',
            description:
                'Get AI-powered suggestions based on your shopping habits and preferences.',
            image: '/images/project_slider_1.png',
            cta: 'Discover More',
        },
        {
            id: 3,
            color: 'bg-[#271251]',
            logo: '/images/logo_slider_2.png',
            title: 'Lightning Fast',
            subtitle: 'Delivery in Minutes',
            description:
                'Our optimized delivery network ensures you get your groceries when you need them.',
            image: '/images/project_slider_2.png',
            cta: 'Order Now',
        },
    ];
    const totalSlides = slides.length;
    // const clonedSlides = [slides[slides.length - 1], ...slides, slides[0]];

    const goToSlide = useCallback(
        (index: number) => {
            if (index < 0) {
                setCurrentIndex(totalSlides - 1);
            } else if (index >= totalSlides) {
                setCurrentIndex(0);
            } else {
                setCurrentIndex(index);
            }
        },
        [totalSlides]
    );

    const nextSlide = useCallback(() => {
        goToSlide(currentIndex + 1);
    }, [goToSlide, currentIndex]);

    const prevSlide = useCallback(() => {
        goToSlide(currentIndex - 1);
    }, [goToSlide, currentIndex]);

    // Auto-slide
    useEffect(() => {
        if (!isLoop || !mounted) return;

        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) => {
                if (prevIndex >= totalSlides - 1) {
                    return 0;
                } else {
                    return prevIndex + 1;
                }
            });
        }, 4000);

        return () => clearInterval(timer);
    }, [isLoop, mounted, totalSlides]);

    useEffect(() => {
        if (mounted && carouselRef.current) {
            const slideWidth = carouselRef.current.offsetWidth;
            carouselRef.current.scrollTo({
                left: currentIndex * slideWidth,
                behavior: 'smooth',
            });
        }
    }, [currentIndex, mounted]);

    useEffect(() => {
        setMounted(true);
    }, []);

    return (
        <div className="font-outfit wrapper-gap lg:wrapper-gap mx-auto mt-5 w-full max-w-7xl rounded-lg">
            <div className="font-outfit px-6 pb-8 text-center lg:px-12">
                <h2 className="text-2xl text-[var(--color-primary-900)] md:text-4xl lg:text-5xl">
                    Discover Our <span className="font-bold">Latest Projects</span>
                </h2>
                <p className="mx-auto mt-1 line-clamp-4 max-w-2xl py-2 text-xl md:mt-2 lg:text-lg">
                    User experience is key! We craft solutions by understanding customer behaviours,
                    emotions, and needs—building businesses that make a difference.
                </p>
            </div>
            <div className="relative">
                <div
                    ref={carouselRef}
                    className="flex snap-x snap-mandatory gap-4 overflow-x-auto scroll-smooth md:rounded-lg xl:overflow-hidden"
                    style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                    {slides.map((slide, index) => (
                        <div
                            key={`${slide.id}-${index}`}
                            className={`relative flex h-[500px] w-[100%] flex-shrink-0 gap-6 text-white md:h-[450px] md:w-[100%] lg:h-[500px] lg:w-[80%] ${slide.color} snap-center`}
                        >
                            {/* Left Content */}
                            <div className="flex max-w-lg flex-col items-start p-6 sm:p-8 lg:max-w-2xl lg:justify-center lg:p-12">
                                <div className="w-[120px] py-4 md:w-[150px] lg:w-[200px]">
                                    <Image width={150} height={60} src={slide.logo} alt="hello" />
                                </div>

                                <h2 className="mb-3 py-2 pt-4 text-3xl font-semibold tracking-wider sm:text-3xl md:pt-0 md:text-4xl md:font-bold">
                                    {slide.subtitle} {slide.title}
                                </h2>

                                <p className="mb-10 line-clamp-3 max-w-[220px] pt-5 text-xl font-light md:mb-6 md:max-w-full md:pt-0 md:text-base lg:text-lg">
                                    {slide.description}
                                </p>

                                <button className="group border-whitebg-white/10 flex cursor-pointer items-center rounded-xl border-2 px-2 py-2 text-lg font-medium text-white transition duration-300 hover:bg-white/20 sm:px-6 sm:py-3 md:px-4 md:py-4">
                                    Success Story
                                    <span className="ml-2 transform pt-1 transition-transform group-hover:translate-x-1">
                                        <RightArrowIcon width="23" height="14" fill="white" />
                                    </span>
                                </button>
                            </div>

                            <div className="absolute right-0 bottom-0 w-[200px] md:w-[300px] lg:w-[400px]">
                                <Image
                                    src={slide.image}
                                    alt={slide.title}
                                    width={200}
                                    height={200}
                                    className="h-auto w-full rounded-lg object-cover object-bottom"
                                />
                            </div>
                        </div>
                    ))}
                </div>

                {/* Prev Button */}
                <button
                    onClick={prevSlide}
                    className={`absolute top-1/2 left-2 flex h-8 w-8 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-white/80 shadow-md transition-all hover:bg-white sm:left-4 sm:h-10 sm:w-10 ${
                        !isLoop && currentIndex === 0
                            ? 'cursor-not-allowed opacity-50'
                            : 'hover:shadow-lg'
                    }`}
                >
                    <PrevButton />
                </button>

                {/* Next Button */}
                <button
                    onClick={nextSlide}
                    className={`absolute top-1/2 right-2 flex h-8 w-8 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-white/80 shadow-md transition-all hover:bg-white sm:right-4 sm:h-10 sm:w-10 ${
                        !isLoop && currentIndex === totalSlides - 1
                            ? 'cursor-not-allowed opacity-50'
                            : 'hover:shadow-lg'
                    }`}
                >
                    <NextButton />
                </button>

                {/* Indicators */}
                <div className="mt-4 flex justify-center space-x-2">
                    {slides.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`h-2 w-2 rounded-full transition-all sm:h-3 sm:w-3 ${
                                index === currentIndex % totalSlides
                                    ? 'bg-blue-500'
                                    : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
}
