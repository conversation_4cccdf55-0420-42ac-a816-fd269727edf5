import { getIconBy<PERSON>ey } from '../icons';
import acelanBlogsData from '@/data/caseStudyData.json';

interface Section {
    title: string;
    points: string[];
}

interface PageData {
    LaMercerLogo: string;
    content: Section[];
}

interface ClientSectionProps {
    pageData: PageData;
}

const { sections } = acelanBlogsData;
export default function ClientSection({ pageData }: ClientSectionProps) {
    const LaMercerLogo = getIconByKey(sections.page1.LaMercerLogo);
    const { content } = pageData;

    return (
        <section className="relative mx-auto max-w-7xl pb-5 lg:pb-10">
            {/* Content */}
            <div className="relative flex flex-col-reverse justify-between md:flex-row">
                {/*  Logo */}
                <div className="flex h-auto items-end md:w-xs lg:w-[15rem]">
                    {LaMercerLogo ? (
                        <LaMercerLogo
                            width="40"
                            height="10"
                            fill="var(--color-black)"
                            className="my-4 h-[4rem] w-[15rem] md:my-0 md:h-[7rem] md:w-[12rem] lg:h-[6rem] lg:w-[17rem]"
                        />
                    ) : (
                        <div className="mb-3 text-sm text-red-500">Missing icon</div>
                    )}
                </div>
                <div className="md:w-3xl">
                    {content.map((section, index) => (
                        <div key={index}>
                            <h3 className="my-5 text-2xl font-semibold text-[#1AF40E] md:text-4xl lg:my-10">
                                {section.title}
                            </h3>

                            <ul className="list-none space-y-2 text-lg leading-normal font-extralight text-wrap text-white md:text-2xl">
                                {section.points.map((point, i) => (
                                    <li key={i}>{point}</li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}
