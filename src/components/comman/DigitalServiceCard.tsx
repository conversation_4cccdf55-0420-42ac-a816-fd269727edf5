import Image from 'next/image';
// import Link from 'next/link';
// import { RightArrowIcon } from '../icons';
import PrimaryBlueButton from '../atoms/PrimaryBlueButton';

interface ServiceCardProps {
    title: string;
    description: string;
    image: string;
    alt: string;
    slug: string;
}

export default function DigitalServiceCard({
    title,
    description,
    image,
    alt,
    slug,
}: ServiceCardProps) {
    return (
        <div className="group mx-auto flex max-w-7xl flex-col overflow-hidden border border-gray-100 bg-white shadow-sm transition-all duration-500 hover:shadow-lg md:flex-row">
            {/* Image Section */}
            <div className="relative w-full overflow-hidden md:h-70 md:w-1/2">
                <Image
                    src={image}
                    alt={alt}
                    width={600}
                    height={400}
                    quality={80}
                    className="h-56 w-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105 md:h-full"
                />
                {/* Overlay on hover */}
                <div className="absolute inset-0 bg-black/20 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
            </div>

            {/* Content Section */}
            <div className="flex w-full flex-col justify-center p-6 md:w-1/1">
                <h3 className="font-outfit mb-3 text-xl md:text-2xl">{title}</h3>
                <p className="mb-5 line-clamp-3 text-xl leading-5 text-gray-600 md:text-lg">
                    {description}
                </p>

                <PrimaryBlueButton
                    primaryButtonLink={slug}
                    className="rounded-sm bg-[#0F31B1] px-[15px] py-[10px] text-xs"
                >
                    Read more
                </PrimaryBlueButton>
                {/* <Link
                    href={slug}
                    className="inline-flex w-max items-center gap-2 rounded-md border border-[#044C7C] px-5 py-2 text-sm font-medium text-[#044C7C] transition-all duration-500 group-hover:bg-gradient-to-r group-hover:from-[#1b3069] group-hover:to-[#6480cf] group-hover:text-white"
                >
                    <span>Read more</span>
                    <RightArrowIcon
                        width="23"
                        height="12"
                        fill="currentColor"
                        className="h-5 w-7 transform pt-1 transition-transform duration-300 group-hover:translate-x-1 md:ml-2"
                    />
                </Link> */}
            </div>
        </div>
    );
}
