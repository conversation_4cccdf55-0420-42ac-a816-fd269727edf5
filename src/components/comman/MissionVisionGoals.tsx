import React from 'react';
import CornerIcon from '../icons/CornerIcon';

const infoCards = [
    {
        title: 'Our \n Mission',
        description: (
            <div>
                At Acelan, we craft innovative solutions that drive growth today and create lasting
                impact for the future.
                <hr className="mt-5"></hr>
                <ul className="mt-3 list-outside list-disc space-y-0.5 pl-4 text-sm text-[#353751]">
                    <li>Innovative Design Solutions</li>
                    <li>Client-Centered Approach</li>
                    <li>Continuous Growth & Excellence</li>
                    <li>Sustainable Impact</li>
                </ul>
            </div>
        ),
        bg: 'bg-[#EDE8FF]',
    },
    {
        title: 'Our \n Vision',
        description: (
            <>
                To be a global leader in digital experience, inspiring innovation and transforming
                businesses with creativity and technology.
                <hr className="mt-5"></hr>
                <ul className="mt-3 list-outside list-disc space-y-0.5 pl-4 text-sm text-[#353751] md:mt-2">
                    <li>Designing the Future</li>
                    <li>Embracing New Frontiers</li>
                    <li>Creative Excellence</li>
                    <li>Inspiring Growth</li>
                </ul>
            </>
        ),
        bg: 'bg-[#F2FFF4]',
    },
    {
        title: 'Future \n Goals',
        description: (
            <>
                We aim to expand globally, embrace emerging technologies, and continuously deliver
                impactful solutions that drive client success.
                <hr className="mt-5"></hr>
                <ul className="mt-3 list-outside list-disc space-y-0.5 pl-4 text-sm text-[#353751]">
                    <li>Expand Global Reach</li>
                    <li>Innovate with Technology</li>
                    <li>Build a Creative Community</li>
                    <li>Sustainable Growth</li>
                </ul>
            </>
        ),
        bg: 'bg-[#FFF9E4]',
    },
];

const MissionVisionGoals = () => (
    <div className="hi wrapper-gap lg:wrapper-gap flex w-full justify-center bg-gradient-to-r from-[#221E3A] via-[#23203E] to-[#11101D]">
        <div className="w-full max-w-7xl">
            {/* Headings and Description */}
            <div className="flex flex-col px-6 md:mb-4 md:flex-row md:items-end md:justify-between lg:mb-8 lg:px-12">
                <h1 className="mb-2 text-center text-2xl leading-tight text-white md:text-start lg:text-4xl">
                    Shaping Today,
                    <br />
                    <span className="font-bold">Inspiring Tomorrow</span>
                </h1>
                <p className="mt-3 mb-8 flex w-full text-center text-xl leading-6 text-[#E4E9F2] md:mt-0 md:mb-0 md:max-w-sm md:text-left md:text-sm lg:max-w-md lg:text-lg">
                    At Acelan, we craft innovative solutions that drive growth today and create
                    lasting impact for the future.
                </p>
            </div>
            {/* Cards Below */}
            <div className="h grid w-full grid-cols-1 place-items-center gap-6 px-6 py-4 md:grid-cols-3 lg:gap-7 lg:px-12">
                {infoCards.map((card) => (
                    <div
                        key={card.title}
                        className={`relative h-95 w-full rounded-xl md:h-full ${card.bg} flex max-w-[300px] min-w-[120px] flex-col p-6 shadow-sm transition-transform duration-150 hover:scale-[1.02] md:max-w-lg`}
                    >
                        <CornerIcon />
                        <span className="mb-2 block text-2xl font-semibold whitespace-pre-line text-[#23233E]">
                            {card.title}
                        </span>
                        <span className="text-lg text-[#23233E] md:text-xs lg:text-lg">
                            {card.description}
                        </span>
                    </div>
                ))}
            </div>
        </div>
    </div>
);

export default MissionVisionGoals;
