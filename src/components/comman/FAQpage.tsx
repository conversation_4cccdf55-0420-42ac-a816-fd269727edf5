'use client';
import { useState } from 'react';
import faqItems from '@/data/FAQData.json';
import Left<PERSON>rrow from '../icons/LeftArrow';
import CrossIcon from '../icons/CrossIcon';
import OpenIcon from '../icons/OpenIcon';

interface FAQItem {
    question: string;
    answer: string;
    category: string;
}

const FAQComponent = () => {
    const [activeCategory, setActiveCategory] = useState<string>('General Questions');
    const [openQuestion, setOpenQuestion] = useState<number | null>(0);

    const categories = Array.from(new Set(faqItems.map((item: FAQItem) => item.category)));

    const currentFAQs = faqItems.filter((item: FAQItem) => item.category === activeCategory);

    const toggleQuestion = (index: number) => {
        setOpenQuestion(index === openQuestion ? null : index);
    };

    return (
        <div className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl bg-[var(--color-bg-secondary)] px-6 lg:px-12">
            <div className=" ">
                <h1 className="font-outfit mb-2 py-6 text-4xl text-[var(--color-primary-900)] md:text-start md:text-5xl">
                    Find Your{' '}
                    <span className="block text-4xl font-bold md:inline md:text-5xl">
                        Answers Here....
                    </span>
                </h1>

                <div className="font-outfit flex flex-col overflow-hidden rounded-xl bg-[var(--color-bg-secondary)] md:flex-row">
                    {/* Left Sidebar */}
                    <div className="space-y-4 bg-[var(--color-bg-secondary)] md:w-1/3">
                        {categories.map((category) => (
                            <div key={category}>
                                <button
                                    onClick={() => {
                                        setActiveCategory(category);
                                        setOpenQuestion(null);
                                    }}
                                    className={`flex w-full items-center justify-between rounded-md border p-4 text-left text-lg text-[#0D0D0D]`}
                                >
                                    {category}
                                    <LeftArrow />
                                </button>

                                {/* MOBILE ONLY */}
                                {activeCategory === category && (
                                    <div className="mt-4 md:hidden">
                                        {currentFAQs.map((faq, index) => (
                                            <div key={index} className="rounded-md py-3">
                                                <button
                                                    onClick={() => toggleQuestion(index)}
                                                    className="flex w-full items-center justify-between text-left text-[#0D0D0D]"
                                                >
                                                    <span className="max-w-[250px] text-lg">
                                                        {faq.question}
                                                    </span>
                                                    {openQuestion === index ? (
                                                        <CrossIcon />
                                                    ) : (
                                                        <OpenIcon />
                                                    )}
                                                </button>
                                                {openQuestion === index && (
                                                    <p className="text-md mt-2 text-[#808080]">
                                                        {faq.answer}
                                                    </p>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* DESKTOP ONLY */}
                    <div className="font-outfit hidden space-y-4 px-4 md:block md:w-2/3">
                        {currentFAQs.map((faq, index) => (
                            <div
                                key={index}
                                className="rounded-md border border-[var(--color-gray-400)] p-4"
                            >
                                <button
                                    onClick={() => toggleQuestion(index)}
                                    className="flex w-full items-center justify-between text-left text-[#0D0D0D]"
                                >
                                    <span className="text-lg">{faq.question}</span>
                                    {openQuestion === index ? (
                                        // X icon

                                        <CrossIcon />
                                    ) : (
                                        // + open icon

                                        <OpenIcon />
                                    )}
                                </button>
                                {openQuestion === index && (
                                    <p className="text-md mt-3 text-[#808080]">{faq.answer}</p>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FAQComponent;
