'use client';
import { useState } from 'react';
import LinkedIn from '../icons/LinkedIn';
import Twitter from '../icons/Twitter';
import Facebook from '../icons/Facebook';
import CustomArrow from '../icons/CustomArrow';
import Link from 'next/link';

export default function ContactForm() {
    const [form, setForm] = useState({ name: '', email: '', message: '', service: '', phone: '' });
    const [errors, setErrors] = useState({
        name: '',
        email: '',
        service: '',
        phone: '',
        message: '',
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setForm({ ...form, [e.target.name]: e.target.value });
        setErrors({ ...errors, [e.target.name]: '' });
    };

    const validate = () => {
        let valid = true;
        const newErrors = { name: '', email: '', service: '', phone: '', message: '' };

        if (!form.name.trim()) {
            newErrors.name = 'Name is required';
            valid = false;
        }

        if (!form.email.trim()) {
            newErrors.email = 'Email is required';
            valid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
            newErrors.email = 'Email is invalid';
            valid = false;
        }

        if (!form.service.trim()) {
            newErrors.service = 'Please select a service';
            valid = false;
        }
        if (!form.message.trim()) {
            newErrors.message = 'Message is invalid';
            valid = false;
        }

        if (!form.phone.trim() || (form.phone && !/^\+?[0-9\s\-()]{7,15}$/.test(form.phone))) {
            newErrors.phone = 'Phone number is invalid';
            valid = false;
        }

        setForm({
            ...form,
            name: form.name.trim(),
            email: form.email.trim(),
            phone: form.phone.trim(),
            message: form.message.trim(),
        });
        setErrors(newErrors);
        return valid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validate()) return;

        const res = await fetch('/api/contact', {
            method: 'POST',
            body: JSON.stringify(form),
        });

        if (res.ok) {
            setForm({ name: '', email: '', message: '', service: '', phone: '' });
        }
    };

    return (
        <div className="font-outfit wrapper-gap lg:wrapper-gap mx-auto flex max-w-7xl items-center justify-center px-6 lg:px-12">
            <div className="grid w-full max-w-6xl grid-cols-1 items-start gap-10 md:grid-cols-3">
                <div className="font-outfit text-center md:col-span-1">
                    <h2 className="text-3xl leading-snug font-[400] text-[var(--color-primary-500)]">
                        Let’s
                    </h2>
                    <h1 className="text-4xl leading-[55px] font-[600] text-[var(--color-primary-500)]">
                        contact
                    </h1>
                    <h2 className="text-3xl leading-snug font-[400] text-[var(--color-primary-500)]">
                        for
                    </h2>
                    <h2 className="text-3xl leading-snug font-[400] text-[var(--color-primary-500)]">
                        better
                    </h2>
                    <h2 className="text-3xl leading-snug font-[400] text-[var(--color-primary-500)]">
                        result
                    </h2>
                    <div className="flex justify-center">
                        <p className="my-8 ml-0 h-24 w-0.5 bg-[var(--color-primary-500)]"></p>
                    </div>

                    <div className="font-outfit">
                        <Link
                            href="mailto:<EMAIL>"
                            className="font-[400] text-[#103142] underline"
                        >
                            <EMAIL>
                        </Link>
                        <p className="mt-2 font-[400] text-[#4C6876]">
                            919, Salasar Central, Palasia Square, Manormaganj, Indore
                        </p>
                        {/* Social icons */}
                        <div className="mt-4 ml-0 flex items-center justify-center gap-4">
                            {/* LinkedIn */}
                            <Link
                                href="https://www.linkedin.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="transition hover:opacity-80"
                            >
                                <LinkedIn />
                            </Link>

                            <Link
                                href="https://twitter.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="transition hover:opacity-80"
                            >
                                <Twitter />
                            </Link>

                            {/* Facebook */}
                            <Link
                                href="https://facebook.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="transition hover:opacity-80"
                            >
                                <Facebook />
                            </Link>
                        </div>
                    </div>
                </div>

                <div className="font-outfit space-y-6 md:col-span-2">
                    <form className="space-y-4" onSubmit={handleSubmit}>
                        <div>
                            <label className="block text-sm font-medium text-[#103142]">Name</label>
                            <input
                                type="text"
                                className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-2 focus:ring-blue-300 focus:outline-none"
                                name="name"
                                value={form.name}
                                onChange={handleChange}
                                required
                            />
                            {errors.name && (
                                <p className="mt-1 text-sm text-red-500">{errors.name}</p>
                            )}
                        </div>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <label className="block text-sm font-medium text-[#103142]">
                                    Email
                                </label>
                                <input
                                    type="email"
                                    className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-2 focus:ring-blue-300 focus:outline-none"
                                    name="email"
                                    value={form.email}
                                    onChange={handleChange}
                                    required
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                                )}
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-[#103142]">
                                    Phone
                                </label>
                                <input
                                    type="tel"
                                    className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-2 focus:ring-blue-300 focus:outline-none"
                                    name="phone"
                                    value={form.phone}
                                    onChange={handleChange}
                                    required
                                />
                                {errors.phone && (
                                    <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-[#103142]">
                                Interested in
                            </label>
                            <div className="relative w-full">
                                <select
                                    className="font-outfit mt-1 w-full appearance-none rounded-md border border-gray-300 p-2 pr-8 focus:ring-2 focus:ring-blue-300 focus:outline-none"
                                    name="service"
                                    value={form.service}
                                    onChange={(e) => setForm({ ...form, service: e.target.value })}
                                    required
                                >
                                    <option>Select Service</option>
                                    <option value="DevOps Solution">DevOps Solution</option>
                                    <option value="Web Development">Web Development</option>
                                    <option value="UI/UX Design">UI/UX Design</option>
                                </select>

                                {/* Custom Arrow */}
                                <span className="pointer-events-none absolute inset-y-0 right-2 flex items-center text-gray-500">
                                    <CustomArrow />
                                </span>
                            </div>

                            {errors.service && (
                                <p className="mt-1 text-sm text-red-500">{errors.service}</p>
                            )}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-[#103142]">
                                Message
                            </label>
                            <textarea
                                rows={4}
                                className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-2 focus:ring-blue-300 focus:outline-none"
                                name="message"
                                value={form.message}
                                onChange={handleChange}
                                required
                                placeholder="Write your message here...."
                            ></textarea>
                            {errors.message && (
                                <p className="mt-1 text-sm text-red-500">{errors.message}</p>
                            )}
                        </div>

                        <button
                            type="submit"
                            className="font-outfit button button w-full cursor-pointer rounded-md bg-[var(--color-primary-500)] py-3 text-white transition duration-300 hover:bg-blue-800 md:max-w-[150px] md:px-6 md:py-3"
                        >
                            Submit Now
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
}
