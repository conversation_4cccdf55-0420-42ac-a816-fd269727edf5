'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { getIconByK<PERSON> } from '../icons';

type CardItem = {
    title: string;
    categories: string[];
    image: string;
    link?: string; // 👈 optional link
};

interface CardCarouselProps {
    data: CardItem[];
}

export default function CardCarousel({ data }: CardCarouselProps) {
    const router = useRouter();
    const itemsPerPage = 3;
    const [currentPage, setCurrentPage] = useState(0);

    const totalPages = Math.ceil(data.length / itemsPerPage);
    const startIndex = currentPage * itemsPerPage;
    const visibleItems = data.slice(startIndex, startIndex + itemsPerPage);

    return (
        <div className="mx-auto max-w-6xl py-10">
            {/* Cards Container */}
            <div className="grid grid-cols-1 gap-6 px-4 md:grid-cols-3">
                {visibleItems.map((item, index) => {
                    const PlayIcon = getIconByKey('play');
                    return (
                        <div
                            key={index}
                            onClick={() => item.link && router.push(item.link)} // 👈 navigate on click
                            className="group relative cursor-pointer overflow-hidden rounded-xl bg-gray-100 shadow-lg transition hover:scale-[1.02]"
                        >
                            <div className="relative h-80 w-full md:h-60 lg:h-84">
                                <Image
                                    src={item.image}
                                    alt={item.title}
                                    fill
                                    className="object-cover"
                                />
                                {/* Play Button Overlay */}
                                <div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 transition group-hover:opacity-100">
                                    <button className="rounded-full bg-white shadow-md">
                                        <PlayIcon
                                            width="24"
                                            height="24"
                                            fill="var(--color-black)"
                                            className="mb-4"
                                        />
                                    </button>
                                </div>
                            </div>

                            {/* Card Footer */}
                            <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-[var(--color-grey-750,rgba(17,16,19,0.5))] to-[var(--color-grey-7,#111013)] px-4 py-4 md:py-2 lg:py-4">
                                <h3 className="font-semibold text-white">{item.title}</h3>
                                <p className="text-sm text-gray-300">
                                    {item.categories.join(', ')}
                                </p>
                            </div>
                        </div>
                    );
                })}
            </div>

            {/* Pagination */}
            <div className="mt-6 flex space-x-3 pl-4">
                {Array.from({ length: totalPages }).map((_, i) => (
                    <button
                        key={i}
                        onClick={() => setCurrentPage(i)}
                        className={`flex h-8 w-8 items-center justify-center rounded-lg text-sm font-medium ${
                            i === currentPage
                                ? 'bg-black text-white'
                                : 'cursor-pointer bg-gray-200 text-gray-600 hover:bg-gray-300'
                        }`}
                    >
                        {i + 1}
                    </button>
                ))}
            </div>
        </div>
    );
}
