'use client';
import React, { useState, useRef, useEffect } from 'react';
import productLifecycleData from '@/data/productLifecycleData.json';

const ProductLifecycle: React.FC = () => {
    const [activeStep, setActiveStep] = useState<number>(1);
    const [progress, setProgress] = useState('0% 100%');
    const [isTransitioning, setIsTransitioning] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    // Progress map (same as before)
    const progressMap: Record<number, number> = {
        1: 0,
        2: 27,
        3: 56,
        4: 88,
        5: 130,
    };

    const updateProgress = (stepId: number) => {
        const percentage = progressMap[stepId] || 0;
        setProgress(`${percentage}% 130%`);
    };
    // Fix: dependency warning solved by wrapping fn
    useEffect(() => {
        updateProgress(activeStep);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeStep]);

    const handleStepClick = (stepId: number) => {
        if (stepId === activeStep || isTransitioning) return;
        setIsTransitioning(true);

        // fade-out
        if (contentRef.current) {
            contentRef.current.classList.add('opacity-0', 'translate-y-5');
        }

        setTimeout(() => {
            setActiveStep(stepId);
            updateProgress(stepId);

            if (contentRef.current) {
                contentRef.current.classList.remove('opacity-0', 'translate-y-5');
                contentRef.current.classList.add('opacity-100', 'translate-y-0');
            }

            setTimeout(() => setIsTransitioning(false), 500);
        }, 400);
    };

    return (
        <section id="how-we-do" className="font-outfit wrapper-gap lg:wrapper-gap bg-white">
            <div className="mx-auto max-w-7xl px-6 lg:px-12">
                {/* Header */}
                <div className="mb-12 flex flex-col gap-4 lg:mb-16 lg:flex-row lg:items-center lg:justify-between lg:gap-8">
                    <div className="text-start">
                        <h2 className="font-outfit text-3xl leading-tight text-gray-900 md:max-w-3xl md:text-4xl lg:max-w-3xl lg:text-4xl">
                            Product Development Lifecycle
                            <span className="font-outfit ml-2 inline text-3xl font-bold text-gray-900 md:ml-0 md:block md:text-4xl lg:mt-3 lg:text-4xl xl:text-5xl">
                                At Acelan Technologies
                            </span>
                        </h2>
                        {/* <h2 className="font-outfit mt-3 text-3xl font-bold text-gray-900 sm:mt-4 sm:text-2xl lg:text-4xl xl:text-5xl">
                                                   </h2> */}
                    </div>
                    <div className="max-w-lg lg:max-w-md">
                        <p className="font-outfit text-base leading-relaxed text-[#333333] sm:text-lg md:text-xl lg:text-xl">
                            We deliver exceptional results with innovation, reliability, and
                            customer-focused solutions tailored for success.
                        </p>
                    </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid gap-12 lg:grid-cols-2 lg:gap-16">
                    {/* Left Side - Steps List */}
                    <div className="space-y-6">
                        {productLifecycleData.map((step) => (
                            <div
                                key={step.id}
                                className={`group cursor-pointer transition-all duration-300 ${
                                    activeStep === step.id
                                        ? 'opacity-100'
                                        : 'opacity-60 hover:opacity-80'
                                }`}
                                onClick={() => handleStepClick(step.id)}
                            >
                                <div className="flex items-start gap-2 md:w-lg md:gap-4">
                                    <span className="font-outfit flex text-sm font-bold text-gray-600 md:text-lg">
                                        {step.stepNumber}
                                    </span>
                                    <div className="flex-1">
                                        <div className="mb-2 flex items-center gap-2 sm:gap-3">
                                            <h3
                                                className={`font-outfit text-lg font-bold transition-colors duration-300 sm:text-xl lg:text-2xl ${
                                                    activeStep === step.id
                                                        ? 'text-gray-900'
                                                        : 'text-gray-700'
                                                }`}
                                            >
                                                {step.title}
                                            </h3>
                                            <div
                                                className={`transition-transform duration-300 ${
                                                    activeStep === step.id
                                                        ? 'rotate-45'
                                                        : 'rotate-0'
                                                }`}
                                            >
                                                {activeStep === step.id ? (
                                                    <svg
                                                        className="h-5 w-5 text-gray-400 sm:h-6 sm:w-6"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M6 18L18 6M6 6l12 12"
                                                        />
                                                    </svg>
                                                ) : (
                                                    <svg
                                                        className="h-5 w-5 text-gray-400 sm:h-6 sm:w-6"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                                        />
                                                    </svg>
                                                )}
                                            </div>
                                        </div>

                                        {/* Description */}
                                        {activeStep === step.id && (
                                            <p className="font-outfit text-sm leading-relaxed text-gray-600 sm:text-base">
                                                {step.description}
                                            </p>
                                        )}
                                    </div>
                                </div>

                                {/* Mobile-only right section with smooth animation */}
                                <div
                                    className={`mt-2 transform overflow-hidden transition-all duration-500 ease-in-out lg:hidden ${
                                        activeStep === step.id
                                            ? 'max-h-[600px] translate-y-0 opacity-100'
                                            : 'max-h-0 -translate-y-5 opacity-0'
                                    }`}
                                >
                                    <div className="ml-6 rounded-tr-2xl rounded-br-2xl bg-[var(--color-bg-accent)] py-4 md:ml-8 md:w-lg">
                                        <div className="relative h-65 md:h-96">
                                            {/* --- Progress Curve SVG --- */}
                                            <svg
                                                className="h-full w-full pb-3"
                                                viewBox="0 0 500 300"
                                                preserveAspectRatio="none"
                                            >
                                                <path
                                                    d="M50,280 C155,270 240,250 360,130 S420,0 480,-10"
                                                    stroke="#2A50BD"
                                                    strokeWidth="2"
                                                    fill="none"
                                                    strokeDasharray="8,4"
                                                    opacity="0.2"
                                                />
                                                <path
                                                    d="M50,280 C155,270 240,250 360,130 S420,0 480,-10"
                                                    stroke="#2A50BD"
                                                    strokeWidth="2"
                                                    fill="none"
                                                    style={{
                                                        strokeDasharray: progress,
                                                        transition:
                                                            'stroke-dasharray 1s ease-in-out',
                                                    }}
                                                />
                                                {productLifecycleData.map((s, i) => {
                                                    const positions = [
                                                        { x: 50, y: 280 },
                                                        { x: 152, y: 263 },
                                                        { x: 260, y: 215 },
                                                        { x: 360, y: 130 },
                                                        { x: 447, y: 7 },
                                                    ];
                                                    const pos = positions[i];
                                                    const isActive = activeStep >= s.id;

                                                    return (
                                                        <g key={s.id}>
                                                            <line
                                                                x1={pos.x}
                                                                y1={pos.y}
                                                                x2={pos.x}
                                                                y2={300}
                                                                stroke="#2A50BD"
                                                                strokeWidth="1"
                                                                strokeDasharray="4,4"
                                                                opacity="0.6"
                                                            />
                                                            <circle
                                                                cx={pos.x}
                                                                cy={pos.y}
                                                                r={7}
                                                                fill={
                                                                    isActive
                                                                        ? '#2A50BD'
                                                                        : 'var(--color-gray-300)'
                                                                }
                                                                onClick={() =>
                                                                    handleStepClick(s.id)
                                                                }
                                                                className="cursor-pointer"
                                                            />
                                                            {/* {isActive && (
                                                                <circle
                                                                    cx={pos.x}
                                                                    cy={pos.y}
                                                                    r="4"
                                                                    fill="white"
                                                                    onClick={() =>
                                                                        handleStepClick(s.id)
                                                                    }
                                                                    className="cursor-pointer transition-all duration-500"
                                                                />
                                                            )} */}
                                                        </g>
                                                    );
                                                })}
                                            </svg>

                                            {/* Labels */}
                                            <div className="absolute right-0 bottom-0 left-0 w-full pb-4">
                                                {productLifecycleData.map((s, index) => {
                                                    const labelPositions = [
                                                        { left: '10%' },
                                                        { left: '30%' },
                                                        { left: '52%' },
                                                        { left: '71%' },
                                                        { left: '87%' },
                                                    ];
                                                    const isCurrent = activeStep === s.id;

                                                    return (
                                                        <div
                                                            key={s.id}
                                                            className="absolute -translate-x-1/2 transform text-center"
                                                            style={labelPositions[index]}
                                                        >
                                                            <span
                                                                className={`font-outfit text-xs transition-all duration-300 ${
                                                                    isCurrent
                                                                        ? 'text-[#2A50BD]'
                                                                        : 'text-gray-600'
                                                                }`}
                                                            >
                                                                Step {s.stepNumber}
                                                            </span>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {step.id !== productLifecycleData.length && (
                                    <div className="mt-4 ml-6 h-px bg-gray-200 sm:mt-6"></div>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Right Side - Visual Representation */}
                    <div className="relative hidden md:hidden lg:block">
                        <div className="relative h-full w-full rounded-tr-3xl rounded-br-3xl bg-[var(--color-bg-accent)] p-8 lg:rounded-tr-4xl lg:rounded-br-4xl lg:p-8">
                            {/* Progress Curve */}
                            <div className="relative h-90 md:h-96">
                                <svg
                                    className="h-full w-full"
                                    viewBox="0 0 500 300"
                                    preserveAspectRatio="none"
                                >
                                    {/* Gray dashed background line */}
                                    <path
                                        d="M50,280 C155,270 240,250 360,130 S420,0 480,-10"
                                        stroke="#2A50BD"
                                        strokeWidth="2"
                                        fill="none"
                                        strokeDasharray="8,4"
                                        opacity="0.2"
                                    />
                                    {/* Blue progress line */}
                                    <path
                                        d="M50,280 C155,270 240,250 360,130 S420,0 480,-10"
                                        stroke="#2A50BD"
                                        strokeWidth="2"
                                        fill="none"
                                        style={{
                                            strokeDasharray: progress,
                                            transition: 'stroke-dasharray 1s ease-in-out',
                                        }}
                                    />

                                    {/* Step circles */}
                                    {productLifecycleData.map((step, index) => {
                                        const positions = [
                                            { x: 50, y: 280 },
                                            { x: 154, y: 263 },
                                            { x: 263, y: 212 },
                                            { x: 363, y: 127 },
                                            { x: 449, y: 5 },
                                        ];
                                        const pos = positions[index];
                                        const isActive = activeStep >= step.id;

                                        return (
                                            <g key={step.id}>
                                                <line
                                                    x1={pos.x}
                                                    y1={pos.y}
                                                    x2={pos.x}
                                                    y2={300}
                                                    stroke="#2A50BD"
                                                    strokeWidth="1"
                                                    strokeDasharray="4,4"
                                                    opacity="0.6"
                                                />
                                                <circle
                                                    cx={pos.x}
                                                    cy={pos.y}
                                                    r={5}
                                                    fill={
                                                        isActive
                                                            ? '#2A50BD'
                                                            : 'var(--color-gray-300)'
                                                    }
                                                    onClick={() => handleStepClick(step.id)}
                                                    className="aspect-square cursor-pointer"
                                                />
                                                {/* {isActive && (
                                                    <circle
                                                        cx={pos.x}
                                                        cy={pos.y}
                                                        r="4"
                                                        fill="white"
                                                        onClick={() => handleStepClick(step.id)}
                                                        className="cursor-pointer transition-all duration-500"
                                                    />
                                                )} */}
                                            </g>
                                        );
                                    })}
                                </svg>

                                {/* Labels under steps */}
                                <div className="absolute right-0 bottom-0 left-0 px-2 sm:px-4">
                                    {productLifecycleData.map((step, index) => {
                                        const labelPositions = [
                                            { left: '10%' },
                                            { left: '30%' },
                                            { left: '52%' },
                                            { left: '72%' },
                                            { left: '89%' },
                                        ];
                                        const isCurrent = activeStep === step.id;

                                        return (
                                            <div
                                                key={step.id}
                                                className="absolute -translate-x-1/2 transform text-center"
                                                style={labelPositions[index]}
                                            >
                                                <span
                                                    className={`font-outfit text-xs transition-all duration-300 ${
                                                        isCurrent
                                                            ? 'text-[#2A50BD]'
                                                            : 'text-gray-600'
                                                    }`}
                                                >
                                                    Step {step.stepNumber}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default ProductLifecycle;
