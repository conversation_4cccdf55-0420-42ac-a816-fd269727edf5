'use client';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import industryDataJson from '@/data/industryData.json';
import Link from 'next/link';
import { RightArrowIcon } from '../icons';
import NextButton from '../icons/NextButton';
import PrevButton from '../icons/PrevButton';

interface IndustrySection {
    id: number;
    title: string;
    subtitle: string;
    description: string;
    backgroundImage: string;
    ctaText: string;
    ctaLink: string;
}

const industryData: IndustrySection[] = industryDataJson;

const IndustryShowcase: React.FC = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [previousActiveIndex, setPreviousActiveIndex] = useState(0);
    const [scrollOffset, setScrollOffset] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);

    const visibleTabs = 4;

    const getVisibleSections = () => {
        const sections: (IndustrySection & { originalIndex: number })[] = [];
        for (let i = 0; i < visibleTabs; i++) {
            const index = (scrollOffset + i) % industryData.length;
            sections.push({ ...industryData[index], originalIndex: index });
        }
        return sections;
    };

    const handlePrevious = () => {
        if (isTransitioning) return;
        setIsTransitioning(true);

        setPreviousActiveIndex(activeIndex);
        const newActiveIndex = activeIndex === 0 ? industryData.length - 1 : activeIndex - 1;
        setActiveIndex(newActiveIndex);

        setScrollOffset((prev) => (prev - 1 + industryData.length) % industryData.length);
    };

    const handleNext = () => {
        if (isTransitioning) return;
        setIsTransitioning(true);

        setPreviousActiveIndex(activeIndex);
        const newActiveIndex = activeIndex === industryData.length - 1 ? 0 : activeIndex + 1;
        setActiveIndex(newActiveIndex);

        setScrollOffset((prev) => (prev + 1) % industryData.length);
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsTransitioning(false);
        }, 500);

        return () => clearTimeout(timer);
    }, [activeIndex, scrollOffset]);

    const activeSection = industryData[activeIndex];

    return (
        <section className="3xl:h-[800px] relative h-[550px] w-full overflow-hidden md:h-[600px] lg:h-[600px]">
            {/* Background Image */}
            <div className="3xl:h-[800px] absolute inset-0 h-screen transition-all duration-800 ease-in-out md:h-[600px] lg:h-[600px]">
                <Image
                    src={activeSection.backgroundImage}
                    alt={activeSection.title}
                    fill
                    className="object-cover"
                    priority
                />
                <div className="absolute inset-0 bg-black/50" />
            </div>

            {/* Navigation Buttons */}
            <button
                onClick={handlePrevious}
                className="group absolute top-1/2 left-4 z-20 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 hover:bg-white/30"
                aria-label="Previous section"
            >
                <PrevButton />
            </button>

            <button
                onClick={handleNext}
                className="group absolute top-1/2 right-4 z-20 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 hover:bg-white/30"
                aria-label="Next section"
            >
                {' '}
                <NextButton />
            </button>

            {/* Main Content */}
            <div className="relative z-10 flex h-full flex-col">
                <div className="flex h-full w-full flex-col justify-end px-6 pb-8 text-white md:hidden lg:hidden">
                    <h1 className="mb-2 text-3xl font-semibold">{activeSection.title}</h1>
                    <p className="mb-6 line-clamp-2 text-base leading-snug">
                        {activeSection.description}
                    </p>

                    <Link href="/contact-us">
                        <button className="group mb-2 flex w-auto cursor-pointer items-center justify-center gap-3 self-start bg-white px-4 py-3 font-medium text-[var(--color-primary-500)] lg:pb-2">
                            {activeSection.ctaText}
                            <RightArrowIcon
                                width="25"
                                height="25"
                                fill="var(--color-primary-500)"
                                className="flex items-center justify-center pt-1.5"
                            />
                        </button>
                    </Link>
                </div>

                {/*  DESKTOP  */}
                <div className="relative hidden w-full bg-black/20 md:block lg:block">
                    {/* highlight box - moving to new active position */}
                    <div
                        className="absolute top-0 left-0 z-10 h-full border-r-2 border-l-2 border-white bg-black/20 transition-all duration-600 ease-in-out"
                        style={{
                            width: '25%',
                            transform: `translateX(${getVisibleSections().findIndex((s) => s.originalIndex === activeIndex) * 100}%)`,
                        }}
                    ></div>

                    {/* highlight box - moving from previous active position (criss-cross) */}
                    <div
                        className="absolute top-0 left-0 z-5 h-full border-r-2 border-l-2 border-white/50 bg-black/10 transition-all duration-600 ease-in-out"
                        style={{
                            width: '25%',
                            transform: `translateX(${getVisibleSections().findIndex((s) => s.originalIndex === activeIndex) * 100}%)`,
                            transitionDelay: '0ms',
                        }}
                    ></div>

                    {/* Second highlight box that starts from previous position */}
                    <div
                        className="absolute top-0 left-0 z-4 h-full border-r-2 border-l-2 border-white/30 bg-black/5 transition-all duration-600 ease-in-out"
                        style={{
                            width: '25%',
                            transform: `translateX(${getVisibleSections().findIndex((s) => s.originalIndex === previousActiveIndex) * 100}%)`,
                            transitionDelay: '100ms',
                        }}
                    ></div>

                    <div className="relative overflow-hidden">
                        {/* Sliding content container */}
                        <div
                            className="flex transition-all duration-600 ease-in-out"
                            style={{
                                transform: `translateX(-${getVisibleSections().findIndex((s) => s.originalIndex === activeIndex) * 25}%)`,
                            }}
                        >
                            {getVisibleSections().map((section, index) => (
                                <div
                                    key={`${section.id}-${scrollOffset}-${index}`}
                                    className={`relative z-20 flex-shrink-0 cursor-pointer border-r transition-all duration-600 ease-in-out ${
                                        activeIndex === section.originalIndex
                                            ? 'text-white hover:bg-black/5'
                                            : 'bg-white/10 text-white'
                                    }`}
                                    style={{ width: '25%', minWidth: '25%' }}
                                    onClick={() => {
                                        setPreviousActiveIndex(activeIndex);
                                        setActiveIndex(section.originalIndex);
                                    }}
                                    onMouseEnter={() => {
                                        if (window.innerWidth >= 1024) {
                                            setPreviousActiveIndex(activeIndex);
                                            setActiveIndex(section.originalIndex);
                                        }
                                    }}
                                >
                                    {activeIndex === section.originalIndex ? (
                                        <div className="3xl:h-[800px] flex h-[400px] flex-1 items-end pb-8 md:h-[600px] lg:h-[600px]">
                                            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                                                <div className="max-w-2xl">
                                                    <div className="transform transition-all duration-600 ease-in-out">
                                                        <h1 className="text-2xl font-semibold text-white md:mb-2 lg:mb-2 transition-all duration-600 ease-in-out">
                                                            {section.title}
                                                        </h1>
                                                        <p className="3xl:text-lg 3xl:w-sm mb-8 text-base leading-snug md:mb-4 md:line-clamp-4 md:text-sm lg:mb-6 transition-all duration-600 ease-in-out">
                                                            {section.description}
                                                        </p>
                                                        <Link href="/contact-us">
                                                            <button className="group flex cursor-pointer items-center justify-center rounded-sm bg-white px-8 py-3 font-medium text-[var(--color-primary-500)] md:px-2 md:py-2 lg:px-4 transition-all duration-600 ease-in-out">
                                                                {section.ctaText}
                                                                <RightArrowIcon
                                                                    width="23"
                                                                    height="12"
                                                                    fill="var(--color-primary-500)"
                                                                    className="mt-2 ml-2 h-5 w-7 transform text-[var(--color-primary-500)] transition-transform duration-300 group-hover:translate-x-1"
                                                                />
                                                            </button>
                                                        </Link>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex h-full items-end p-6">
                                            <h3 className="mb-4 text-2xl font-semibold text-white transition-all duration-600 ease-in-out">
                                                {section.title}
                                                <RightArrowIcon
                                                    width="25"
                                                    height="25"
                                                    fill="var(--color-white)"
                                                    className="flex items-center justify-center pt-1.5"
                                                />
                                            </h3>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default IndustryShowcase;
