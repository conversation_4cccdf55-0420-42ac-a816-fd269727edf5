import React from 'react';
import { getIconByKey } from '../icons';
import { renderRichText } from '../../utils';

const ServiceWhy = ({ data }) => {
    if (!data) return null;

    const { title = '', subtitle = '', services = [] } = data;

    return (
        <div className="bg-[var(--color-white)8f8] px-4 py-16">
            <div className="mx-auto max-w-6xl">
                <div className="mb-16 text-center">
                    <h2 className="mb-4 text-4xl leading-tight text-[var(--color-primary-900)] md:text-5xl">
                        {renderRichText(title)}
                    </h2>
                    {subtitle && (
                        <p className="mx-auto max-w-4xl text-lg leading-relaxed text-gray-600 md:text-xl">
                            {subtitle}
                        </p>
                    )}
                </div>

                <div className="grid grid-cols-1 justify-items-center gap-y-10 md:grid-cols-2 lg:grid-cols-4 lg:gap-12">
                    {services.map((service, idx) => {
                        const Icon = getIconByKey(service.iconKey);
                        return (
                            <div
                                className="flex max-w-2xs flex-col items-center justify-center rounded-2xl bg-[rgba(253,236,229,0.4)] px-6 py-8 text-center"
                                key={idx}
                            >
                                <div className="mb-6 flex">
                                    <div className="flex h-16 w-16">
                                        {Icon ? (
                                            <Icon
                                                width="24"
                                                height="24"
                                                fill="var(--color-black)"
                                                className="h-12 w-12 text-[var(--color-primary-900)]"
                                            />
                                        ) : (
                                            <div className="h-12 w-12 rounded bg-gray-200" />
                                        )}
                                    </div>
                                </div>
                                <h3 className="mb-3 text-xl font-bold text-[var(--color-primary-900)]">
                                    {service.title}
                                </h3>
                                <p className="pb-2 leading-relaxed text-gray-600">
                                    {service.description}
                                </p>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default ServiceWhy;
