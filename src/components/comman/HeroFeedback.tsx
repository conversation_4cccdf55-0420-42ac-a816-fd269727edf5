const HeroFeedback = () => {
    return (
        <div className="wrapper-gap lg:wrapper-gap m-auto flex justify-center bg-[var(--color-bg-tertiary)]">
            <div className="flex max-w-7xl flex-col rounded-sm px-6 md:flex-row lg:px-12">
                {/* Left Side: Title */}
                <div className="flex flex-col justify-start md:w-1/3">
                    <div>
                        <span className="block text-3xl font-semibold text-[var(--color-primary-900)] md:text-2xl">
                            Acelan Where
                        </span>
                        <span className="block text-4xl leading-tight font-bold text-[var(--color-primary-900)] md:text-3xl">
                            Ideas Take Flight!
                        </span>
                    </div>
                </div>

                {/* Right Side: Body & Signature */}
                <div className="mt-6 flex flex-1 flex-col md:mt-0 md:ml-8">
                    <p className="mb-4 text-lg leading-relaxed text-[#3B4255] md:text-sm">
                        Acelan was founded to bridge the gap between innovative ideas and practical
                        solutions. We combine cutting-edge technology with human-centric design to
                        help businesses thrive in a fast-paced market. Built on trust, excellence,
                        and continuous growth, our mission is to create value, solve challenges, and
                        open new doors of possibility for every client.
                    </p>
                    <div className="mb-5">
                        <section className="text-sm text-[var(--color-primary-900)]">
                            It’s about&nbsp;
                            <span className="font-bold">Solving, Evolving</span>,
                            <span>
                                <br></br>
                                and <span className="font-bold">Inspiring</span> a better tomorrow
                            </span>
                        </section>
                    </div>
                    <div className="mt-auto flex flex-col items-start">
                        <span className="font-signature text-lg leading-tight text-[var(--color-primary-900)] italic">
                            Satyam
                        </span>
                        <span className="mt-0.5 text-[10px] text-[#3B4255]">CEO, Acelan</span>
                    </div>
                </div>
            </div>
        </div>
    );
};
export default HeroFeedback;
