'use client';

import { JSX, useState } from 'react';

interface Technology {
    name: string;
    icon: JSX.Element;
    color: string;
}

interface TechnologyCategory {
    name: string;
    technologies: Technology[];
}

// Technology Icons Components
const HTMLIcon = () => (
    <svg viewBox="0 0 24 24" className="h-8 w-8" fill="currentColor">
        <path d="M1.5 0h21l-1.91 21.563L11.977 24l-8.564-2.438L1.5 0zm7.031 9.75l-.232-2.718 10.059.003.23-2.622L5.412 4.41l.698 8.01h9.126l-.326 3.426-2.91.804-2.955-.81-.188-2.11H6.248l.33 4.171L12 19.351l5.379-1.443.744-8.157H8.531z" />
    </svg>
);

const CSSIcon = () => (
    <svg viewBox="0 0 24 24" className="h-8 w-8" fill="currentColor">
        <path d="M1.5 0h21l-1.91 21.563L11.977 24l-8.565-2.438L1.5 0zm17.09 4.413L5.41 4.41l.213 2.622 10.125.002-.255 2.716h-6.64l.24 2.573h6.182l-.366 3.523-2.91.804-2.956-.81-.188-2.11h-2.61l.29 3.855L12 19.288l5.373-1.53L18.59 4.414z" />
    </svg>
);

const JSIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center rounded bg-yellow-400 text-sm font-bold text-black">
        JS
    </div>
);

const VueIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-green-400">
        V
    </div>
);

const ReactIcon = () => (
    <svg viewBox="0 0 24 24" className="h-8 w-8" fill="currentColor">
        <path d="M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.36-.034-.47 0-.92.014-1.36.034.44-.572.895-1.096 1.36-1.564zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.866.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.36.034.47 0 .92-.014 1.36-.034-.44.572-.895 1.095-1.36 1.56-.465-.467-.92-.992-1.36-1.56z" />
    </svg>
);

const AngularIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-red-500">A</div>
);

const D3Icon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-orange-500">
        D3
    </div>
);

const JQueryIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-blue-400">
        jQ
    </div>
);

const TypeScriptIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-sm font-bold text-white">
        TS
    </div>
);

const GraphQLIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-pink-500">
        GQL
    </div>
);

// Additional Icon Components
const SassIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-pink-500">
        SASS
    </div>
);
const BootstrapIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-purple-600">
        BS
    </div>
);
const TailwindIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-cyan-400">
        TW
    </div>
);
const WebpackIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-blue-500">
        WP
    </div>
);

const NodeIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-green-500">
        NODE
    </div>
);
const PythonIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-yellow-500">
        PY
    </div>
);
const PHPIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-indigo-500">
        PHP
    </div>
);
const JavaIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-red-600">
        JAVA
    </div>
);
const CSharpIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-green-600">
        C#
    </div>
);
const RubyIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-red-500">
        RUBY
    </div>
);

const FlutterIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-blue-400">
        F
    </div>
);
const SwiftIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-orange-500">
        S
    </div>
);
const KotlinIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-purple-500">
        K
    </div>
);
const IonicIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-blue-500">
        I
    </div>
);
const XamarinIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-xl font-bold text-blue-400">
        X
    </div>
);
const CordovaIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-gray-400">
        CD
    </div>
);
const UnityIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-gray-600">
        U3D
    </div>
);
const ReactNativeIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-cyan-400">
        RN
    </div>
);
const NativeScriptIcon = () => (
    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-green-400">
        NS
    </div>
);

const technologyData: TechnologyCategory[] = [
    {
        name: 'Front-End',
        technologies: [
            { name: 'HTML', icon: <HTMLIcon />, color: 'var(--color-html)' },
            { name: 'CSS', icon: <CSSIcon />, color: 'var(--color-css)' },
            { name: 'Javascript', icon: <JSIcon />, color: 'var(--color-javascript)' },
            { name: 'Vue JS', icon: <VueIcon />, color: 'var(--color-vue)' },
            { name: 'React JS', icon: <ReactIcon />, color: 'var(--color-react)' },
            { name: 'Angular JS', icon: <AngularIcon />, color: 'var(--color-angular)' },
            { name: 'SASS', icon: <SassIcon />, color: 'var(--color-sass)' },
            { name: 'Bootstrap', icon: <BootstrapIcon />, color: 'var(--color-bootstrap)' },
            { name: 'Tailwind', icon: <TailwindIcon />, color: 'var(--color-tailwind)' },
            { name: 'Webpack', icon: <WebpackIcon />, color: 'var(--color-webpack)' },
        ],
    },
    {
        name: 'Back-End',
        technologies: [
            { name: 'Node.js', icon: <NodeIcon />, color: 'var(--color-node)' },
            { name: 'Python', icon: <PythonIcon />, color: 'var(--color-python)' },
            { name: 'PHP', icon: <PHPIcon />, color: 'var(--color-php)' },
            { name: 'Java', icon: <JavaIcon />, color: 'var(--color-java)' },
            { name: 'C#', icon: <CSharpIcon />, color: 'var(--color-csharp)' },
            { name: 'Ruby', icon: <RubyIcon />, color: 'var(--color-ruby)' },
            { name: 'D3 JS', icon: <D3Icon />, color: 'var(--color-d3)' },
            { name: 'JQuery', icon: <JQueryIcon />, color: 'var(--color-jquery)' },
            { name: 'TypeScript', icon: <TypeScriptIcon />, color: 'var(--color-typescript)' },
            { name: 'GraphQL', icon: <GraphQLIcon />, color: 'var(--color-graphql)' },
        ],
    },
    {
        name: 'Mobile',
        technologies: [
            { name: 'React Native', icon: <ReactNativeIcon />, color: 'var(--color-react)' },
            { name: 'Flutter', icon: <FlutterIcon />, color: 'var(--color-flutter)' },
            { name: 'Swift', icon: <SwiftIcon />, color: 'var(--color-swift)' },
            { name: 'Kotlin', icon: <KotlinIcon />, color: 'var(--color-kotlin)' },
            { name: 'Ionic', icon: <IonicIcon />, color: 'var(--color-ionic)' },
            { name: 'Xamarin', icon: <XamarinIcon />, color: 'var(--color-xamarin)' },
            { name: 'Cordova', icon: <CordovaIcon />, color: 'var(--color-gray-300)' },
            { name: 'Unity', icon: <UnityIcon />, color: 'var(--color-unity)' },
            {
                name: 'NativeScript',
                icon: <NativeScriptIcon />,
                color: 'var(--color-nativescript)',
            },
            {
                name: 'PhoneGap',
                icon: (
                    <div className="flex h-8 w-8 items-center justify-center text-sm font-bold text-gray-500">
                        PG
                    </div>
                ),
                color: 'var(--color-phonegap)',
            },
        ],
    },
];

export default function TechnologyShowcase() {
    const [activeCategory, setActiveCategory] = useState('Front-End');

    const currentTechnologies =
        technologyData.find((category) => category.name === activeCategory)?.technologies || [];

    return (
        <div className="bg-gray-900 text-white">
            <div className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl px-6 lg:px-12">
                {/* Header Section */}
                <div className="mb-12 flex flex-col items-center justify-between md:items-start lg:flex-row lg:items-center">
                    <div className="font-outfit mb-6 flex flex-col items-center md:items-start lg:mb-0">
                        <h1 className="text-[40px] font-bold text-white md:mb-2 md:text-5xl">
                            We Used Advance
                        </h1>
                        <h1 className="text-4xl text-white lg:text-5xl">TECHNOLOGY</h1>
                    </div>

                    <div className="font-outfit max-w-md flex-col items-center text-center md:text-start">
                        <p className="text-base leading-relaxed text-gray-400">
                            Offer a wide range of services to help businesses establish and enhance
                            their online presence.
                        </p>
                    </div>
                </div>

                {/* Category Buttons - Horizontal with chat tails */}
                <div className="mb-8">
                    <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
                        {technologyData.map((category) => (
                            <div key={category.name} className="relative">
                                <button
                                    onClick={() => setActiveCategory(category.name)}
                                    className={`font-outfit relative cursor-pointer rounded-full px-3 py-2 text-xs font-medium transition-all duration-300 sm:px-4 sm:py-2 sm:text-sm ${activeCategory === category.name ? 'bg-blue-600 text-white shadow-lg' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`}
                                >
                                    {category.name}
                                    {/* Chat tail */}
                                    <div
                                        className={`absolute top-full left-1/2 h-0 w-0 -translate-x-1/2 transform border-t-[6px] border-r-[6px] border-l-[6px] border-r-transparent border-l-transparent transition-all duration-300 ${activeCategory === category.name ? 'border-t-blue-600' : 'border-t-gray-800'}`}
                                    ></div>
                                </button>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Combined Layout  */}
                <div className="space-y-6">
                    {/* Desktop Layout */}
                    <div className="mb-8 hidden grid-cols-5 items-end gap-6 md:grid">
                        {/* First Column - Raised HTML card */}
                        <div className="flex justify-center">
                            {currentTechnologies[0] && (
                                <div className="group flex min-h-[140px] w-full translate-y-[-20px] transform cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-6 transition-all duration-300 hover:scale-105 hover:bg-gray-700">
                                    <div className="mb-2 flex items-center justify-center">
                                        <div style={{ color: currentTechnologies[0].color }}>
                                            {currentTechnologies[0].icon}
                                        </div>
                                    </div>
                                    <span className="font-outfit text-center text-sm font-medium text-white transition-colors duration-300 group-hover:text-blue-400">
                                        {currentTechnologies[0].name}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Middle Three Columns  */}
                        <div></div>
                        <div></div>
                        <div></div>

                        {/* Last Column  */}
                        <div className="flex justify-center">
                            {currentTechnologies[1] && (
                                <div className="group flex min-h-[140px] w-full translate-y-[-20px] transform cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-6 transition-all duration-300 hover:scale-105 hover:bg-gray-700">
                                    <div className="mb-2 flex items-center justify-center">
                                        <div style={{ color: currentTechnologies[1].color }}>
                                            {currentTechnologies[1].icon}
                                        </div>
                                    </div>
                                    <span className="font-outfit text-center text-sm font-medium text-white transition-colors duration-300 group-hover:text-blue-400">
                                        {currentTechnologies[1].name}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Technology Grid */}
                    <div className="space-y-6">
                        {/* Mobile Layout - Custom grid */}
                        <div className="flxe flex-col md:hidden">
                            <div className="grid grid-cols-2 gap-4 sm:gap-6">
                                {/* First card - full width */}
                                {currentTechnologies[0] && (
                                    <div className="group col-span-2 flex min-h-[120px] cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-4 transition-all duration-300 hover:scale-105 hover:bg-gray-700 sm:min-h-[140px] sm:p-6">
                                        <div className="mb-2 flex items-center justify-center sm:mb-4">
                                            <div style={{ color: currentTechnologies[0].color }}>
                                                {currentTechnologies[0].icon}
                                            </div>
                                        </div>
                                        <h3 className="font-outfit text-center text-xs font-medium text-white transition-colors duration-300 group-hover:text-blue-400 sm:text-sm">
                                            {currentTechnologies[0].name}
                                        </h3>
                                    </div>
                                )}

                                {/* Second and Third cards side by side */}
                                {currentTechnologies.slice(1, 3).map((tech) => (
                                    <div
                                        key={tech.name}
                                        className="group flex min-h-[120px] flex-col items-center justify-center rounded-xl bg-gray-800 p-4 transition-all duration-300 hover:scale-105 hover:bg-gray-700 sm:min-h-[140px] sm:p-6"
                                    >
                                        <div className="mb-2 flex items-center justify-center sm:mb-4">
                                            <div style={{ color: tech.color }}>{tech.icon}</div>
                                        </div>
                                        <h3 className="font-outfit text-center text-xs font-medium text-white transition-colors duration-300 group-hover:text-blue-400 sm:text-sm">
                                            {tech.name}
                                        </h3>
                                    </div>
                                ))}

                                {/* Fourth card - full width */}
                                {currentTechnologies[3] && (
                                    <div className="group col-span-2 flex min-h-[120px] cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-4 transition-all duration-300 hover:scale-105 hover:bg-gray-700 sm:min-h-[140px] sm:p-6">
                                        <div className="mb-2 flex items-center justify-center sm:mb-4">
                                            <div style={{ color: currentTechnologies[3].color }}>
                                                {currentTechnologies[3].icon}
                                            </div>
                                        </div>
                                        <h3 className="font-outfit text-center text-xs font-medium text-white transition-colors duration-300 group-hover:text-blue-400 sm:text-sm">
                                            {currentTechnologies[3].name}
                                        </h3>
                                    </div>
                                )}

                                {/* Remaining cards */}
                                {currentTechnologies.slice(4).map((tech) => (
                                    <div
                                        key={tech.name}
                                        className="group flex min-h-[120px] cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-4 transition-all duration-300 hover:scale-105 hover:bg-gray-700 sm:min-h-[140px] sm:p-6"
                                    >
                                        <div className="mb-2 flex items-center justify-center sm:mb-4">
                                            <div style={{ color: tech.color }}>{tech.icon}</div>
                                        </div>
                                        <h3 className="font-outfit text-center text-xs font-medium text-white transition-colors duration-300 group-hover:text-blue-400 sm:text-sm">
                                            {tech.name}
                                        </h3>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Desktop Layout */}
                        <div className="hidden md:block">
                            {/* First Row */}
                            <div className="mb-6 grid grid-cols-5 gap-6">
                                {currentTechnologies.slice(2, 7).map((tech) => (
                                    <div
                                        key={tech.name}
                                        className="group flex min-h-[140px] cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-8 transition-all duration-300 hover:scale-105 hover:bg-gray-700"
                                    >
                                        <div className="mb-4 flex items-center justify-center">
                                            <div style={{ color: tech.color }}>{tech.icon}</div>
                                        </div>
                                        <h3 className="font-outfit text-center text-sm font-medium text-white transition-colors duration-300 group-hover:text-blue-400">
                                            {tech.name}
                                        </h3>
                                    </div>
                                ))}
                            </div>

                            {/* Second Row - 3 items centered with same total width as 5-item row */}
                            {currentTechnologies.length > 7 && (
                                <div className="grid grid-cols-5 gap-6">
                                    {/* Empty spacer - takes up first column */}
                                    <div></div>
                                    {currentTechnologies.slice(7, 10).map((tech) => (
                                        <div
                                            key={tech.name}
                                            className="group flex min-h-[140px] cursor-pointer flex-col items-center justify-center rounded-xl bg-gray-800 p-8 transition-all duration-300 hover:scale-105 hover:bg-gray-700"
                                        >
                                            <div className="mb-4 flex items-center justify-center">
                                                <div style={{ color: tech.color }}>{tech.icon}</div>
                                            </div>
                                            <h3 className="font-outfit text-center text-sm font-medium text-white transition-colors duration-300 group-hover:text-blue-400">
                                                {tech.name}
                                            </h3>
                                        </div>
                                    ))}
                                    {/* Empty spacer */}
                                    <div></div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
