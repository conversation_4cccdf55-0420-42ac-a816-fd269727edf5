import React from 'react';
import { getIconByKey } from '../icons';
import { renderRichText } from '../../utils';
import Image from 'next/image';
// import { log } from 'console';

const BestAtApp = ({ data }) => {
    if (!data) return null;

    const { title = '', subtitle = '', leftFeatures = [], rightFeatures = [] } = data;

    return (
        <div className="bg-[var(--color-white)] px-4 py-6 md:py-6">
            <div className="mx-auto max-w-6xl">
                <div className="text-center lg:mb-16">
                    <h2 className="mb-3 text-2xl leading-tight text-[var(--color-primary-900)] md:mb-6 md:text-5xl">
                        {renderRichText(title)}
                    </h2>
                    {subtitle && (
                        <p className="mx-auto max-w-4xl text-lg leading-relaxed text-gray-700 md:text-xl">
                            {subtitle}
                        </p>
                    )}
                </div>
                <div className="flex w-full flex-col items-center justify-center bg-white py-6 lg:py-16">
                    <div className="flex w-full max-w-7xl flex-col items-center justify-center gap-10 lg:flex-row">
                        {/* Left Features */}
                        <div className="flex flex-1 flex-col space-y-5 text-[var(--color-primary-900)] md:space-y-10">
                            {leftFeatures.map((feature, idx) => {
                                const LeftIcon = getIconByKey(feature.icon);
                                // console.log(feature.icon,"uuuuuuuuuuuuuuu");

                                return (
                                    <div
                                        key={idx}
                                        className="flex max-w-xs flex-col items-center space-x-4 md:flex-row"
                                    >
                                        {LeftIcon && (
                                            <LeftIcon
                                                width="24"
                                                height="24"
                                                fill="var(--color-black)"
                                                className="mb-4"
                                            />
                                        )}
                                        <div className="max-w-[250px]">
                                            <h3 className="pb-1 text-center text-2xl font-semibold text-[var(--color-primary-900)] md:text-start">
                                                {feature.title}
                                            </h3>
                                            <p className="text-center text-base font-normal md:text-start">
                                                {feature.description}
                                            </p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Center Image */}
                        <div className="relative flex flex-1 items-center justify-center text-[var(--color-primary-900)]">
                            <Image
                                src="/images/app_dev_center.png"
                                alt="Center"
                                width={300}
                                height={300}
                                className="w-72 md:w-96"
                            />
                        </div>

                        {/* Right Features */}
                        <div className="flex flex-1 flex-col space-y-5 text-[var(--color-primary-900)] md:space-y-10">
                            {rightFeatures.map((feature, idx) => {
                                const RightIcon = getIconByKey(feature.icon);
                                return (
                                    <div
                                        key={idx}
                                        className="flex max-w-xs flex-col items-center space-x-4 md:flex-row"
                                    >
                                        {RightIcon && (
                                            <RightIcon
                                                width="50"
                                                height="50"
                                                fill="var(--color-black)"
                                                className="mb-4"
                                            />
                                        )}
                                        <div className="max-w-[250px]">
                                            <h3 className="pb-1 text-center text-2xl font-semibold text-[var(--color-primary-900)] md:text-start">
                                                {feature.title}
                                            </h3>
                                            <p className="text-center text-base font-normal md:text-start">
                                                {feature.description}
                                            </p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BestAtApp;
