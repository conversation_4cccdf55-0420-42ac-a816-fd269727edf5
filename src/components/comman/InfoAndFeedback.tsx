import React from 'react';
import { getIconByKey } from '../icons';

const infoData = [
    {
        header: 'Outstanding Client Support',
        description:
            'We prioritize your satisfaction by delivering prompt, reliable, and personalized service, ensuring a smooth and seamless experience.',
        icon: 'clientSupportIcon',
    },
    {
        header: 'Knowledgeable Professionals',
        description:
            'Our team of industry experts is committed to offering insightful guidance and innovative solutions tailored to your unique needs.',
        icon: 'professionalsIcon',
    },
    {
        header: 'Superior Craftsmanship',
        description:
            'We take pride in delivering products that are meticulously finished to the highest standard of quality, designed to perform, last, and astound.',
        icon: 'superiorIcon',
    },
    {
        header: 'Innovative Solutions And Proven Methods',
        description:
            'Combining cutting-edge technology with time-tested methods, we create effective and efficient strategies that offer lasting solutions.',
        icon: 'innovativeIcon',
    },
];

type CardProps = {
    header: string;
    description: string;
    icon: string | React.ReactNode;
};

const Card: React.FC<CardProps> = ({ header, description, icon }) => {
    let IconEl: React.ReactNode;

    if (typeof icon === 'string') {
        const Icon = getIconByKey(icon);
        IconEl = Icon ? (
            <Icon
                width="24"
                height="24"
                fill="var(--color-black)"
                className="h-12 w-12 text-[var(--color-primary-900)]"
            />
        ) : (
            <div className="h-12 w-12 rounded bg-gray-200" />
        );
    } else {
        IconEl = icon;
    }

    return (
        <div className="h-full w-full bg-transparent p-6 text-left">
            <div className="mb-4">{IconEl}</div>
            <div className="mb-2 max-w-[12rem] font-semibold tracking-wide text-[var(--color-primary-900)]">
                {header}
            </div>
            <div className="max-w-[12rem] text-sm text-[#353751]">{description}</div>
        </div>
    );
};
const InfoAndFeedback = () => {
    return (
        <div className="wrapper-gap lg:wrapper-gap bg-[rgb(254,242,237)]">
            <div className="mx-auto max-w-7xl">
                {/* Header */}
                <section className="mb-8 flex flex-col justify-end md:flex-row md:items-end md:justify-between md:px-15 lg:px-15">
                    <span className="font-outfit mb-4 flex justify-center text-4xl text-[var(--color-primary-900)] md:mb-0 md:flex-col">
                        Acelan <span className="font-bold">Technologies</span>
                    </span>
                    <div className="text-xk px-2 text-center text-[#353751] md:max-w-xs md:px-0 md:text-left md:text-base lg:max-w-md">
                        <span>Acelan Technologies gives your teams the power to</span>
                        <span>build, ship, and manage sites collaboratively, at scale</span>
                    </div>
                </section>

                {/* Cards Grid */}
                <div className="grid grid-cols-1 gap-6 px-4 sm:grid-cols-2 md:px-12 lg:grid-cols-4">
                    {infoData.map((item) => (
                        <Card key={item.header} {...item} />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default InfoAndFeedback;
