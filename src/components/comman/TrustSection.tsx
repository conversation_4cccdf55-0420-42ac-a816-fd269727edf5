import trustSectionData from '@/data/TrustSectionData.json';
import { getIconByKey } from '../icons';

const TrustSection = () => {
    const { features } = trustSectionData;

    return (
        <section className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl bg-black bg-[linear-gradient(rgba(0,0,0,0.2),rgba(0,0,0,0.2)),url('/images/frame_bg_image_m.png')] bg-cover bg-center px-6 text-white md:bg-[url('/images/frame_bg_image_d.png')] lg:px-12">
            <div className="font-outfit mx-auto max-w-6xl">
                {/* Section Heading */}
                <h2 className="text-3xl md:text-4xl">
                    Why trust us, <span className="block font-bold">our results speaks</span>
                </h2>

                {/* Features Grid */}
                <div className="mt-12 grid grid-cols-1 gap-10 md:grid-cols-4">
                    {features.map((feature, index) => {
                        const Icon = getIconByKey(feature.icon);
                        return (
                            <div key={index} className="flex flex-col">
                                <div className="mb-4 flex h-18 w-18 items-center justify-center rounded-2xl bg-[rgba(229,229,229,0.1)]">
                                    {Icon && (
                                        <Icon width="20" height="20" fill="var(--color-black)" />
                                    )}
                                </div>
                                <h3 className="text-lg font-semibold">{feature.title}</h3>
                                <p className="mt-2 text-sm font-light">{feature.description}</p>
                            </div>
                        );
                    })}
                </div>
            </div>
        </section>
    );
};

export default TrustSection;
