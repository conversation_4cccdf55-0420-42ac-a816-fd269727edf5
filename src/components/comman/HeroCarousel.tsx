'use client';
import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import { renderRichText } from '@/utils';
import { getIconByKey } from '../icons';

// --- Canvas background animation logic ---
// Types for points and connections
interface Point {
    x: number;
    y: number;
    vx?: number;
    vy?: number;
    size?: number;
}
interface Connection {
    point: Point;
    distance: number;
    index: number;
}

const createRandomNetwork = (points: Point[]): [Point, Point][] => {
    const connections: [Point, Point][] = [];
    for (let i = 0; i < points.length; i++) {
        const point = points[i];
        const nearbyPoints: Connection[] = [];
        for (let j = 0; j < points.length; j++) {
            if (i !== j) {
                const other = points[j];
                const distance = Math.sqrt((point.x - other.x) ** 2 + (point.y - other.y) ** 2);
                if (distance < 150) {
                    nearbyPoints.push({ point: other, distance, index: j });
                }
            }
        }
        nearbyPoints.sort((a, b) => a.distance - b.distance);
        const numConnections = Math.min(2 + Math.floor(Math.random() * 4), nearbyPoints.length);
        for (let k = 0; k < numConnections; k++) {
            const randomIndex = Math.floor(Math.random() * Math.min(nearbyPoints.length, k + 3));
            const targetPoint = nearbyPoints[randomIndex];
            if (targetPoint && i < targetPoint.index) {
                connections.push([point, targetPoint.point]);
            }
        }
    }
    return connections;
};

const AnimatedBackground = ({
    mousePosition,
    isHovered,
}: {
    mousePosition: { x: number; y: number };
    isHovered: boolean;
}) => {
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const animationRef = useRef<number | null>(null);
    const dotsRef = useRef<Point[]>([]);
    const [isClient, setIsClient] = useState(false);

    useEffect(() => setIsClient(true), []);

    useEffect(() => {
        if (!isClient) return;
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const resizeCanvas = () => {
            const parent = canvas.parentElement;
            if (!parent) return;
            canvas.width = parent.offsetWidth;
            canvas.height = parent.offsetHeight;
        };

        const initDots = () => {
            if (canvas.width === 0 || canvas.height === 0) resizeCanvas();
            dotsRef.current = [];
            const numDots = 120;
            for (let i = 0; i < numDots; i++) {
                dotsRef.current.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.3,
                    vy: (Math.random() - 0.5) * 0.3,
                    size: 1 + Math.random() * 2,
                });
            }
        };

        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            dotsRef.current.forEach((dot) => {
                if (
                    typeof dot.x !== 'number' ||
                    typeof dot.y !== 'number' ||
                    typeof dot.vx !== 'number' ||
                    typeof dot.vy !== 'number'
                )
                    return;
                dot.x += dot.vx;
                dot.y += dot.vy;
                if (dot.x < 0) dot.x = canvas.width;
                if (dot.x > canvas.width) dot.x = 0;
                if (dot.y < 0) dot.y = canvas.height;
                if (dot.y > canvas.height) dot.y = 0;
            });

            const connections = createRandomNetwork(dotsRef.current);
            connections.forEach(([a, b], index) => {
                const dx = a.x - b.x;
                const dy = a.y - b.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const baseOpacity = 0.15 * (1 - distance / 150);
                const opacity = baseOpacity + Math.sin(index * 0.1) * 0.05;
                ctx.beginPath();
                ctx.moveTo(a.x, a.y);
                ctx.lineTo(b.x, b.y);
                ctx.strokeStyle = `rgba(255, 255, 255, ${Math.max(0.05, opacity)})`;
                ctx.lineWidth = 0.8;
                ctx.stroke();
            });

            dotsRef.current.forEach((dot, i) => {
                if (
                    typeof dot.x !== 'number' ||
                    typeof dot.y !== 'number' ||
                    typeof dot.size !== 'number'
                )
                    return;
                const time = performance.now() * 0.0005;
                const opacity = 0.6 + Math.sin(time + i * 0.2) * 0.2;
                ctx.beginPath();
                ctx.arc(dot.x, dot.y, dot.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
                ctx.fill();
            });

            animationRef.current = requestAnimationFrame(animate);
        };

        resizeCanvas();
        initDots();
        animate();
        window.addEventListener('resize', resizeCanvas);
        return () => {
            if (animationRef.current !== null) {
                cancelAnimationFrame(animationRef.current);
            }
            window.removeEventListener('resize', resizeCanvas);
        };
    }, [isClient]);

    return (
        <canvas
            ref={canvasRef}
            className="absolute inset-0"
            style={{
                zIndex: 10,
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                WebkitMaskImage: isHovered
                    ? `radial-gradient(circle 200px at ${mousePosition.x}px ${mousePosition.y}px, black 0%, black 60%, transparent 100%)`
                    : `radial-gradient(circle 0px at ${mousePosition.x}px ${mousePosition.y}px, black 0%, transparent 100%)`,
                maskImage: isHovered
                    ? `radial-gradient(circle 200px at ${mousePosition.x}px ${mousePosition.y}px, black 0%, black 60%, transparent 100%)`
                    : `radial-gradient(circle 0px at ${mousePosition.x}px ${mousePosition.y}px, black 0%, transparent 100%)`,
                WebkitMaskRepeat: 'no-repeat',
                maskRepeat: 'no-repeat',
                WebkitMaskSize: '100% 100%',
                maskSize: '100% 100%',
                transition: 'mask-image 0.3s ease-out, -webkit-mask-image 0.3s ease-out',
            }}
        />
    );
};

const slides = [
    {
        id: 1,
        tagline: 'AI & ML PIONEER',
        title: 'Innovative Software \n <span class="font-semibold">Solutions For The Future</span>',
        description:
            'Empowering businesses with cutting-edge software solutions designed for scalability, efficiency, and future growth.',
        image: '/images/hero-image-1.png',
        cta1: 'Contacts Us',
        cta2: 'Read More',
        navTitle: 'AI & ML Pioneer',
    },
    {
        id: 2,
        tagline: 'SOFTWARE SOLUTION',
        title: 'Custom Software \n <span class="font-semibold">Development</span>',
        description:
            'We build bespoke software tailored to your specific business needs, from enterprise applications to mobile solutions.',
        image: '/images/hero-image-2.png',
        cta1: 'Contacts Us',
        cta2: 'Read More',
        navTitle: 'Software Solution',
    },
    {
        id: 3,
        tagline: 'INFINITE POSSIBILITIES',
        title: 'Cloud & DevOps \n <span class="font-semibold">Expertise</span>',
        description:
            'Leverage the power of the cloud with our expert DevOps and cloud-native development services for ultimate scalability.',
        image: '/images/hero-image-3.png',
        cta1: 'Contacts Us',
        cta2: 'Read More',
        navTitle: 'Infinite Possibilities',
    },
    {
        id: 4,
        tagline: 'WELL RECOGNIZED',
        title: 'Award-Winning \n <span class="font-semibold">Design</span>',
        description:
            'Our focus on user-centric design has been recognized by industry awards, ensuring a great experience for your users.',
        image: '/images/hero-image-4.png',
        cta1: 'Contacts Us',
        cta2: 'Read More',
        navTitle: 'Well Recognized',
    },
    {
        id: 5,
        tagline: 'EMERGING LEADER',
        title: 'Leading the \n <span class="font-semibold">Charge in Tech</span>',
        description:
            'As an emerging leader, we are constantly exploring new technologies to give our clients a competitive edge.',
        image: '/images/hero-image-5.png',
        cta1: 'Contacts Us',
        cta2: 'Read More',
        navTitle: 'Emerging leader',
    },
];

const HeroCarousel = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);

    const [isVisible, setIsVisible] = useState(false);

    const slideRefs = useRef<(HTMLDivElement | null)[]>([]);
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
    const [isHovered, setIsHovered] = useState(false);
    const [logoOffset, setLogoOffset] = useState(0);

    const handleNext = () => {
        setActiveIndex((prev) => {
            const nextIndex = prev + 1;
            return nextIndex >= slides.length ? 0 : nextIndex;
        });
    };

    useEffect(() => {
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(handleNext, 5000);
        return () => {
            if (timeoutRef.current) clearTimeout(timeoutRef.current);
        };
    }, [activeIndex]);

    const [, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        if (!containerRef.current) return;

        const observer = new IntersectionObserver(
            ([entry]) => {
                setIsVisible(entry.isIntersecting);
            },
            { threshold: 0.1 } // 10% visible
        );

        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);

    useEffect(() => {
        if (!isVisible) return;
        if (window.innerWidth < 768) {
            const currentSlide = slideRefs.current[activeIndex];
            if (currentSlide && containerRef.current?.contains(currentSlide)) {
                currentSlide.scrollIntoView({
                    behavior: 'smooth',
                    inline: 'nearest',
                    block: 'nearest',
                });
            }
        }
    }, [activeIndex, isVisible]);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        if (!containerRef.current) return;
        const rect = containerRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        setMousePosition({ x: mouseX, y: mouseY });
        const containerCenterX = rect.width / 2;
        const distanceFromCenter = mouseX - containerCenterX;
        setLogoOffset(distanceFromCenter / 250);
    };

    const slide = slides[activeIndex];

    const AcelanLogo = getIconByKey('acelanLogo');

    return (
        <div
            ref={containerRef}
            className="relative h-[600px] overflow-hidden font-sans text-white md:h-[700px] lg:[height:calc(80vh)] xl:[height:calc(100vh-100px)] 2xl:[height:calc(90vh-150px)]"
            onMouseMove={handleMouseMove}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div
                className="absolute inset-0 z-0"
                style={{
                    backgroundImage: 'url("/images/hero_banner.png")',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                }}
            />
            <AnimatedBackground mousePosition={mousePosition} isHovered={isHovered} />
            <div className="3xl:px-60 relative z-20 flex h-full flex-col justify-center px-6 py-10 lg:relative lg:z-20 lg:flex lg:h-full lg:flex-col lg:justify-center lg:px-12">
                <div className="flex items-center justify-between">
                    <div className="max-w-3xl lg:max-w-[200rem]">
                        <p className="mb- text-outfit 3xl:md:text-[2rem] text-lg font-light tracking-widest text-gray-300 uppercase md:text-lg lg:mb-2 lg:text-sm lg:font-semibold lg:tracking-widest lg:text-gray-300 2xl:md:text-2xl">
                            {slide.tagline}
                        </p>
                        <h1 className="text-unbounded 3xl:text-7xl 3xl:text-[4rem] mb-4 text-2xl leading-tight font-normal md:text-3xl xl:text-5xl">
                            {renderRichText(slide.title)}
                        </h1>
                        <p className="text-outfit 3xl:max-w-5xl 3xl:md:text-[2rem] mb-8 line-clamp-3 text-xl text-gray-300 md:mb-6 md:max-w-md md:text-xl md:leading-tight lg:mb-8 lg:max-w-xl lg:text-xl 2xl:max-w-lg 2xl:text-2xl">
                            {slide.description}
                        </p>
                        <div className="3xl:gap-6 3xl:mt-20 flex space-x-4">
                            <Link href="/contact-us">
                                <button className="3xl:text-3xl 3xl:px-10 3xl:py-5 cursor-pointer rounded-md bg-white px-6 py-3 font-semibold text-blue-900 hover:bg-blue-900 hover:text-white md:py-2">
                                    Contact Us
                                </button>
                            </Link>
                            <button className="3xl:text-3xl 3xl:px-10 3xl:py-5 3xl:border-2 cursor-pointer rounded-md border border-white px-6 py-2 text-white hover:bg-blue-900 hover:text-white">
                                Read More
                            </button>
                        </div>
                    </div>
                    {/* <div
                        className="hidden items-center justify-center md:flex md:w-[17rem] lg:flex lg:w-xs  3xl:max-w-[300rem]"
                        style={{
                            transform: `translateX(${logoOffset}px)`,
                            transition: 'transform 0.1s ease-out',
                        }}
                    >
                        <AcelanLogo
                            width="800"
                            height="800"
                            fill="#000"
                            className="3xl:w-[300rem]"
                            style={{
                                maxWidth: '100%',
                                maxHeight: '80vh',
                                objectFit: 'contain',
                            }}
                        />
                    </div> */}

                    <div
                        className="3xl:w-[40rem] hidden items-center justify-center md:flex md:w-[17rem] lg:flex lg:w-xs"
                        style={{
                            transform: `translateX(${logoOffset}px)`,
                            transition: 'transform 0.1s ease-out',
                        }}
                    >
                        <AcelanLogo
                            width="800"
                            height="800"
                            fill="#000"
                            className="3xl:w-[300rem]"
                            style={{
                                maxWidth: '100%',
                                maxHeight: '80vh',
                                objectFit: 'contain',
                            }}
                        />
                    </div>
                </div>
            </div>

            <div className="absolute right-0 bottom-0 left-0 z-30 2xl:mb-10">
                <div
                    ref={containerRef}
                    className="scrollbar-hide 3xl:gap-60 flex w-full space-x-6 overflow-x-auto px-4 py-4 md:gap-6 lg:justify-center lg:gap-20 lg:overflow-x-visible xl:mb-5"
                >
                    {slides.map((s, i) => (
                        <div
                            key={s.id}
                            ref={(el: HTMLDivElement | null) => {
                                slideRefs.current[i] = el;
                            }}
                            className="flex flex-shrink-0 cursor-pointer flex-col items-center text-center"
                            onClick={() => setActiveIndex(i)}
                        >
                            {/* Progress bar container */}
                            <div className="mb-1 h-1 w-full overflow-hidden rounded bg-white/20">
                                {i === activeIndex && (
                                    <div className="animate-progress-bar h-full w-full bg-white" />
                                )}
                            </div>

                            {/* Slide navigation title */}
                            <span
                                className={`text-sm lg:text-lg xl:text-xl 2xl:text-4xl ${
                                    i === activeIndex
                                        ? 'text-white'
                                        : 'text-gray-400 hover:text-white'
                                }`}
                            >
                                {s.navTitle}
                            </span>
                        </div>
                    ))}
                </div>
            </div>

            {/* <div className="absolute right-0 bottom-0 left-0 z-30 md:mb-3 lg:mb-5 2xl:mb-10">
                <div
                    ref={containerRef}
                    className="scrollbar-hide flex space-x-6 overflow-x-auto px-4 py-4 md:gap-6 lg:justify-center lg:gap-10 lg:overflow-x-visible xl:gap-14"
                >
                    {slides.map((s, i) => (
                        <div
                            key={s.id}
                            ref={(el: HTMLDivElement | null) => {
                                slideRefs.current[i] = el;
                            }}
                            className="flex flex-shrink-0 cursor-pointer flex-col items-center text-center"
                            onClick={() => setActiveIndex(i)}
                        >
                            <div className="mb-1 h-1 w-full overflow-hidden rounded bg-white/20">
                                {i === activeIndex && (
                                    <div className="animate-progress-bar h-full w-full bg-white" />
                                )}
                            </div>

                            <span
                                className={`text-sm lg:text-xl xl:text-2xl ${
                                    i === activeIndex
                                        ? 'text-white'
                                        : 'text-gray-400 hover:text-white'
                                }`}
                            >
                                {s.navTitle}
                            </span>
                        </div>
                    ))}
                </div>
            </div> */}
        </div>
    );
};

export default HeroCarousel;
