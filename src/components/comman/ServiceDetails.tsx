import React from 'react';
import { getIconByKey } from '../icons';
import { renderRichText } from '../../utils';

const ServiceDetails = ({ data }) => {
    if (!data) return null;

    const { title = '', description = '', services = [] } = data;

    return (
        <div className="font-outfit bg-white">
            <div className="m-auto max-w-7xl px-6 py-10 lg:px-12">
                <section className="mx-auto flex flex-col md:flex-row md:justify-between">
                    <div className="max-w-xl py-4 text-center md:max-w-xs md:text-start lg:max-w-xl">
                        <span className="text-4xl">{renderRichText(title)}</span>
                        {/* <span className="block text-4xl font-bold">{subtitle}</span> */}
                    </div>
                    <div className="text-center md:max-w-xs md:text-start lg:max-w-lg">
                        <span className="px-0 text-xl">{description}</span>
                    </div>
                </section>

                {/* -------------------------------- */}
                <div className="flex justify-center px-0 py-10">
                    <div className="mx-auto max-w-7xl">
                        <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
                            {services.map((service, idx) => {
                                const Icon = getIconByKey(service.iconKey);
                                return (
                                    <div
                                        key={idx}
                                        className="flex h-80 w-full flex-col justify-center rounded border border-[var(--color-gray-200)] bg-white p-7 shadow-sm lg:justify-between"
                                    >
                                        <div>
                                            <div className="mb-4">
                                                {Icon ? (
                                                    <Icon
                                                        width="40"
                                                        height="40"
                                                        fill="var(--color-black)"
                                                        className="mb-4"
                                                    />
                                                ) : (
                                                    <div className="mb-4 text-red-500">
                                                        Missing icon
                                                    </div>
                                                )}
                                            </div>
                                            <div
                                                className="mb-2 max-w-50 text-lg font-semibold text-[var(--color-black-100)]"
                                                style={{ lineHeight: '1.4' }}
                                            >
                                                {service.title}
                                            </div>
                                            <div className="mt-4 text-lg text-[var(--color-gray-600)]">
                                                {service.description}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ServiceDetails;
