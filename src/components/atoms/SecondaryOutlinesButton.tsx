'use client';

import { SecondaryButtonProps } from '@/types/button';
import Link from 'next/link';

const SecondaryOutlinesButton = ({
    children,
    secondaryButtonLink,
    serviceReference,
    className = '',
}: SecondaryButtonProps) => {
    return (
        <Link
            href={{
                pathname: String(secondaryButtonLink), // ensures it's a string
                ...(serviceReference && { query: { service: serviceReference } }),
            }}
        >
            <button
                type="button"
                className={`cursor-pointer rounded-md border border-white bg-transparent px-6 py-4 text-white hover:bg-white/10 ${className}`}
            >
                {children}
            </button>
        </Link>
    );
};

export default SecondaryOutlinesButton;
