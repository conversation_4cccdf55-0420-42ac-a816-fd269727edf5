'use client';

import { PrimaryBlueButtonProps } from '@/types/button';
import Link from 'next/link';

const PrimaryBlueButton = ({
    children, // Button text or elements
    primaryButtonLink,
    serviceReference,
    className = '',
    ...props // Other props (like aria-label)
}: PrimaryBlueButtonProps) => {
    return (
        <Link
            href={{
                pathname: String(primaryButtonLink), // ensures it's a string
                ...(serviceReference && { query: { service: serviceReference } }),
            }}
        >
            <button
                className={`cursor-pointer rounded-md bg-[var(--color-primary-600)] px-6 py-4 text-white shadow-[inset_4px_4px_17.4px_var(--color-shadow-light)] hover:bg-[var(--color-primary-600)]/70 ${className}`}
                {...props}
            >
                {children}
            </button>
        </Link>
    );
};

export default PrimaryBlueButton;
