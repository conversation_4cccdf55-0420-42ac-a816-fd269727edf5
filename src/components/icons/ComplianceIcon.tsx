import React from 'react';

const ComplianceIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.42188 5.86429C6.65039 6.09867 5.86914 6.75296 5.48828 7.47562L5.22461 7.97367L5.25391 14.6631C5.26367 18.3448 5.32227 21.5869 5.37109 21.8701C5.54688 22.8565 6.08398 23.5987 6.95312 24.0576C7.54883 24.3604 8.56445 24.3799 13.1348 24.1944C17.5195 23.9991 19.9121 23.833 21.6406 23.5889C22.2949 23.501 23.0371 22.7491 23.2422 21.9873C23.4668 21.1377 23.8965 9.10648 23.7305 8.2471C23.6133 7.58304 23.418 7.22171 22.9492 6.75296C22.1777 5.98148 22.0215 5.94242 19.3848 5.86429C16.2109 5.7764 7.72461 5.7764 7.42188 5.86429ZM21.2891 8.00296C21.8848 8.29593 21.9336 8.52054 22.002 11.0694C22.041 12.3194 22.1387 15.2686 22.2363 17.6221L22.4023 21.9092L22.168 22.3584C22.002 22.6612 21.7676 22.9053 21.4551 23.1006L20.9961 23.3838L20.0391 23.2764C18.4375 23.0811 16.6211 22.9737 12.4512 22.8174C10.2539 22.7393 8.31055 22.6514 8.14453 22.6221C7.8125 22.5635 7.38281 22.1729 7.24609 21.792C7.19727 21.6455 7.1875 20.0147 7.23633 17.7881C7.27539 15.7178 7.31445 12.8467 7.31445 11.4112C7.32422 8.60843 7.36328 8.37406 7.8418 8.10062C8.00781 8.00296 9.375 7.9639 13.7207 7.92484C16.8359 7.89554 19.7363 7.86624 20.166 7.84671C20.752 7.83695 21.0352 7.86624 21.2891 8.00296Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M33.2416 6.02014C31.5912 6.44006 30.2728 7.17249 29.0424 8.33459C27.265 10.024 26.3666 11.9869 26.2103 14.5162C26.1127 16.1373 26.4349 17.6412 27.2162 19.1744C28.2513 21.1959 30.1459 22.7877 32.3236 23.4713C33.0072 23.6764 33.2123 23.6959 35.3998 23.6959C37.5873 23.6959 37.7924 23.6764 38.476 23.4713C41.4252 22.5436 43.6127 20.2389 44.3939 17.2408C44.7357 15.9225 44.7553 14.1451 44.4428 12.8658C43.6127 9.44788 41.0638 6.88928 37.6166 6.01038C36.3275 5.67834 34.5209 5.68811 33.2416 6.02014ZM36.3861 8.03186C40.39 8.5885 43.3197 12.3678 42.7924 16.3033C42.558 18.0319 41.8939 19.4186 40.683 20.6881C39.5209 21.9283 38.5248 22.4948 36.6205 23.0514L35.39 23.4127L34.4135 23.1393C31.7377 22.3678 29.9603 20.9615 28.8178 18.6764C28.3197 17.6901 28.1049 16.7428 28.1049 15.444C28.1049 14.067 28.3197 13.1686 28.9447 11.9479C29.9799 9.96545 32.0013 8.49084 34.2474 8.08069C35.2045 7.90491 35.4584 7.89514 36.3861 8.03186Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M7.32453 26.7327C6.3382 27.094 5.62531 27.8069 5.31281 28.7444C5.16633 29.2034 5.15656 29.7893 5.21516 35.6975C5.25422 39.2718 5.33234 42.4163 5.38117 42.7386C5.55695 43.7542 6.17219 44.5452 7.11945 44.9651C7.51984 45.1409 7.74445 45.1604 9.03352 45.1507C12.1292 45.1116 18.0277 44.8186 19.6292 44.6331C20.0882 44.5745 20.7425 44.5061 21.0941 44.4768C21.4456 44.4475 21.8851 44.3401 22.0804 44.2425C22.598 43.9886 23.1155 43.344 23.2327 42.8069C23.4769 41.7229 23.9066 29.8577 23.7308 29.0667C23.5745 28.3831 23.3011 27.9241 22.764 27.4358C21.9144 26.6643 22.139 26.6936 14.5023 26.6253C8.39875 26.5764 7.72492 26.5862 7.32453 26.7327ZM21.3773 28.8714C21.8948 29.135 21.9437 29.4085 22.0218 32.2893C22.0511 33.7346 22.1488 36.3518 22.2269 38.0999C22.4222 42.5628 22.4124 42.8753 22.139 43.2854C21.8655 43.6858 21.1917 44.1741 20.889 44.1839C20.762 44.1839 20.264 44.135 19.7757 44.0764C18.5257 43.9202 16.1429 43.7835 11.846 43.6175C7.82258 43.471 7.72492 43.4514 7.36359 42.9143C7.14875 42.5823 7.15852 43.0315 7.26594 35.512C7.35383 29.2718 7.36359 29.1643 7.78352 28.9593C8.06672 28.8225 10.4105 28.7639 15.9476 28.7444C20.2933 28.7249 21.1429 28.7444 21.3773 28.8714Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M28.1551 26.7525C27.4519 27.0064 26.9051 27.4068 26.5633 27.9244C25.9578 28.8424 25.9578 28.7935 26.0262 34.8775C26.0652 37.9146 26.1238 41.0103 26.1629 41.772C26.2312 43.022 26.2605 43.1881 26.5144 43.6568C26.8562 44.272 27.2078 44.5845 27.9305 44.9263C28.4383 45.1607 28.5457 45.1705 29.8836 45.151C33.1062 45.1021 39.0535 44.8092 40.5281 44.6334C40.9871 44.5748 41.6805 44.4967 42.0711 44.4576C43.3308 44.3111 43.9851 43.6275 44.1512 42.2701C44.2586 41.2935 44.4051 37.9927 44.5418 33.3736C44.6297 30.3853 44.6297 29.3697 44.5418 28.9693C44.3172 27.9537 43.5066 27.0943 42.5301 26.8209C42.1394 26.7135 40.6551 26.6744 35.3523 26.6256C28.9851 26.567 28.6433 26.5767 28.1551 26.7525ZM42.2664 28.8814C42.7254 29.2037 42.7449 29.3697 42.8621 33.022C42.9305 34.9263 43.0379 37.8756 43.116 39.565C43.243 42.4947 43.243 42.651 43.0769 43.0318C42.9793 43.2467 42.7742 43.5396 42.618 43.6861C42.3152 43.9693 41.6414 44.2916 41.5144 44.2135C41.2312 44.0377 37.989 43.8228 32.618 43.6177C28.5457 43.4713 28.6531 43.481 28.2039 42.8756C28.0379 42.651 28.0281 42.3287 28.0965 36.0299L28.1746 29.4088L28.448 29.1451C28.7117 28.8814 28.741 28.8717 30.157 28.8131C30.948 28.7838 33.9461 28.7545 36.8172 28.7447C41.4168 28.7252 42.0711 28.7447 42.2664 28.8814Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default ComplianceIcon;
