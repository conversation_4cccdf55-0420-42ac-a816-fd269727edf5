import React from 'react';

const DetailscountershapeIcon = () => {
    return (
        <svg
            height="86"
            width="68"
            fill="none"
            viewBox="0 0 68 86"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 0H51.94C60.7765 0 67.94 7.16344 67.94 16V86H44C19.6995 86 0 66.3005 0 42V0Z"
                fill="url(#paint0_linear_1961_343250)"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_1961_343250"
                    gradientUnits="userSpaceOnUse"
                    x1="0"
                    x2="67.94"
                    y1="43"
                    y2="43"
                >
                    <stop stopColor="#082251" />
                    <stop offset="1" stopColor="#020E24" />
                </linearGradient>
            </defs>
        </svg>
    );
};

export default DetailscountershapeIcon;
