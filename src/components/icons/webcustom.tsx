import React from 'react';

const webcustom = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M13.2519 3.23244C10.5371 3.31057 7.46089 3.47658 6.20113 3.6133C5.05855 3.74025 3.88667 4.209 3.32027 4.78518C2.81246 5.28322 2.33394 6.19143 2.0898 7.0801C1.87496 7.91018 1.7773 11.4746 1.67964 22.0215C1.59175 30.9766 1.62105 32.4121 1.90425 33.5059C2.10933 34.2871 2.26558 34.5899 2.7441 35.1074C3.52535 35.9571 4.89253 36.3086 8.30074 36.543C11.2109 36.7383 25.6543 36.709 30.664 36.4942C32.7832 36.4063 34.8339 36.3281 35.2148 36.3281H35.9082L35.9765 38.4961C36.1132 43.291 36.1328 43.5449 36.3964 43.8574C36.5722 44.0625 36.7285 44.1406 36.9531 44.1406C37.2265 44.1406 37.4023 43.9942 38.4668 42.8223C39.1308 42.0899 39.6972 41.5235 39.7265 41.5528C39.7558 41.5821 40.4003 42.6953 41.1621 44.0235C42.4121 46.2305 42.5586 46.4551 42.9199 46.6309C43.5156 46.9238 44.5312 46.8848 45.1757 46.5528C46.0937 46.084 46.7285 45.1367 46.5332 44.5313C46.4941 44.4141 46.0742 43.6524 45.6054 42.8321C45.1269 42.0215 44.4628 40.8496 44.1211 40.2344L43.4961 39.1113L43.9843 39.043C44.2578 39.0039 45.205 38.9063 46.0937 38.8184C46.9824 38.7403 47.8027 38.6426 47.9199 38.6231C48.2031 38.5547 48.4668 38.1543 48.3886 37.9004C48.3007 37.627 45.3515 35.2246 42.9589 33.4863C42.3242 33.0274 42.2265 32.9199 42.2949 32.7442C42.4707 32.2656 42.832 25.6055 42.9785 20.1074C43.0566 16.8946 43.0566 14.8242 42.9785 11.709C42.8418 6.82619 42.7734 5.94728 42.4804 5.36135C42.08 4.57033 41.3281 3.94533 40.4785 3.70119C40.1074 3.59377 33.6523 3.34963 27.539 3.22268C22.4218 3.11525 17.1191 3.11525 13.2519 3.23244ZM33.7402 4.48244C39.5312 4.69728 40.166 4.74611 40.4785 4.90236C40.8593 5.09768 41.289 5.52736 41.4746 5.89846C41.621 6.19143 41.7382 7.72463 41.8261 10.4199L41.8554 11.6211H22.9296H4.00386L4.05269 10.6153C4.2773 6.38674 4.28706 6.25978 4.48238 5.86916C4.74605 5.35158 5.30269 4.91213 5.84956 4.79494C6.32808 4.68752 8.71089 4.53127 11.7675 4.39455C14.2968 4.28713 30.1171 4.35549 33.7402 4.48244ZM41.8945 15.9863C41.8945 21.3672 41.4648 31.6602 41.2207 32.0899C41.1816 32.1582 40.3906 31.6895 39.1308 30.8496C37.9589 30.0684 36.9921 29.4922 36.8652 29.4922C36.6015 29.4922 36.3281 29.7266 36.3281 29.961C36.3281 30.0488 36.2207 30.3028 36.0839 30.5176C35.8593 30.8887 35.8496 30.9863 35.8398 32.4317V33.9649L34.0136 34.043C32.998 34.0918 27.6562 34.1211 22.1191 34.1211C11.582 34.1211 6.123 34.0039 5.53706 33.75C5.13667 33.584 4.59956 33.0567 4.43355 32.6563C4.28706 32.3047 4.16011 30.8789 3.9941 27.5879C3.85738 25.1172 3.82808 15.2539 3.94527 13.7403L4.02339 12.793H22.9589H41.8945V15.9863ZM38.0664 31.5039C39.9121 32.6856 43.6132 35.3613 45.4589 36.8555L46.4355 37.6465L45.7031 37.7149C43.916 37.8906 42.373 38.1153 42.2265 38.2324C41.8359 38.5059 42.0898 39.0625 44.4433 43.1348C45.4003 44.7852 45.4199 44.8242 45.2636 45.0586C45.0781 45.3321 44.3261 45.7324 44.082 45.6836C43.9843 45.6641 43.2617 44.4531 42.2656 42.6074C41.2011 40.6641 40.5371 39.5313 40.4199 39.502C40.0781 39.3946 39.8828 39.541 38.7109 40.8399C37.871 41.7676 37.539 42.0703 37.5293 41.9336C37.4902 41.6016 37.4414 31.1524 37.4804 31.1524C37.4902 31.1524 37.7636 31.3086 38.0664 31.5039Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M28.6135 7.34379C22.7443 7.42191 22.0119 7.44145 21.8557 7.58793C21.6506 7.76371 21.6213 8.20316 21.7971 8.37895C21.9338 8.51566 25.1662 8.5352 32.1584 8.42777C37.0314 8.35941 36.9142 8.37895 36.9142 7.81254C36.9142 7.38285 36.6017 7.2266 35.84 7.24613C35.4885 7.2559 32.2365 7.29496 28.6135 7.34379Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M10.0882 7.7539C9.7757 8.0371 9.72687 8.13476 9.72687 8.55468C9.7171 9.15038 9.94171 9.53124 10.4202 9.73632C10.8792 9.92187 11.348 9.83398 11.7484 9.47265C12.0316 9.21874 12.0609 9.14062 12.0609 8.64257C12.0609 8.12499 12.0316 8.0664 11.6995 7.7539C11.3968 7.48046 11.2601 7.42187 10.889 7.42187C10.5276 7.42187 10.3812 7.48046 10.0882 7.7539Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M6.03582 7.71481C5.45965 8.16403 5.42059 9.12106 5.9577 9.58981C6.57293 10.1465 7.68621 9.86325 7.92059 9.09176C8.06707 8.59372 7.96942 8.24215 7.56903 7.84176C7.29559 7.56833 7.1784 7.5195 6.75848 7.5195C6.42645 7.5195 6.20184 7.57809 6.03582 7.71481Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M14.5891 7.70512C14.0715 8.02738 13.9153 8.7891 14.2571 9.35551C14.7161 10.1075 15.8489 10.0977 16.3079 9.34574C16.6399 8.79887 16.5032 8.15433 15.9563 7.72465C15.6243 7.46097 14.9993 7.45121 14.5891 7.70512Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M7.08008 16.3477C6.51367 16.6113 6.2207 16.9043 6.00586 17.4023C5.87891 17.7051 5.85938 18.3398 5.85938 21.3867C5.85938 25.3711 5.85938 25.3809 6.45508 25.9473C7.12891 26.582 7.10937 26.582 12.0312 26.6406C17.002 26.6894 17.0117 26.6894 17.6758 26.0449C18.2227 25.5176 18.2617 25.3027 18.3594 22.6758C18.5352 18.0469 18.5254 17.6855 18.2617 17.207C18.0078 16.7578 17.5 16.3574 16.9824 16.2109C16.7871 16.1523 14.7754 16.1133 12.1094 16.1133H7.56836L7.08008 16.3477ZM16.8652 17.4121C17.041 17.5 17.2266 17.666 17.2852 17.7832C17.4219 18.0273 17.2266 24.5215 17.0703 24.9219C17.0215 25.0684 16.8555 25.2637 16.709 25.3613C16.4746 25.5273 16.1621 25.5371 12.1582 25.498C8.50586 25.4785 7.8125 25.4492 7.5293 25.3125C6.94336 25.0391 6.92383 24.8926 6.95312 21.1914L6.98242 17.8906L7.2168 17.6367C7.35352 17.4902 7.59766 17.334 7.75391 17.2852C7.91992 17.2363 9.9707 17.207 12.3047 17.2168C16.1523 17.2363 16.582 17.2559 16.8652 17.4121Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M23.7015 17.8418C23.3109 17.9395 23.3011 18.7402 23.682 18.8867C23.7699 18.916 26.5824 18.9453 29.9222 18.9453C35.6937 18.9453 36.016 18.9355 36.1625 18.7695C36.3968 18.5156 36.3675 18.1055 36.1136 17.9297C35.9183 17.793 35.1761 17.7734 29.8832 17.7832C26.5824 17.793 23.7992 17.8125 23.7015 17.8418Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.8593 23.7891C20.6835 23.9648 20.6639 24.248 20.8007 24.5215C20.8983 24.707 21.0936 24.707 28.4178 24.707C35.7421 24.707 35.9374 24.707 36.035 24.5215C36.1815 24.2383 36.1522 23.9746 35.9569 23.7988C35.7909 23.6426 35.1268 23.6328 28.3885 23.6328C21.8065 23.6328 20.996 23.6523 20.8593 23.7891Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.0918 28.9844C23.8965 29.1992 23.8769 29.5996 24.043 29.7656C24.1309 29.8535 25.1562 29.8828 28.0371 29.8828C31.4355 29.8828 31.9433 29.8633 32.0703 29.7266C32.2656 29.5312 32.2656 29.3066 32.0703 29.0234L31.9238 28.8086H28.0859C24.4922 28.8086 24.2383 28.8184 24.0918 28.9844Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default webcustom;
