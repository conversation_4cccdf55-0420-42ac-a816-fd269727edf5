import React from 'react';

const scalable = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
        >
            <path
                d="M7.27389 7.12023C5.9067 7.51398 5.07545 9.6796 5.68795 11.2874C6.10358 12.414 7.16452 13.2452 8.3567 13.4093C8.63014 13.453 8.83795 13.5187 8.80514 13.5624C8.7067 13.7155 8.43327 17.0405 8.32389 19.3046C8.14889 22.9468 8.07233 32.0359 8.15983 39.2327C8.22545 45.2265 8.25827 45.8718 8.42233 46.0796C8.60827 46.3093 8.60827 46.3093 15.7614 46.2765C31.7301 46.2109 38.5005 46.0577 43.0176 45.664L43.7505 45.5984V46.0249C43.7505 46.6155 44.3192 47.7093 44.8661 48.1796C45.5661 48.7702 46.3755 49.0327 47.3598 48.978C48.6833 48.9015 49.613 48.289 50.2036 47.1187C50.663 46.189 50.6739 45.1827 50.2145 44.2421C49.6895 43.1265 49.0223 42.7218 47.8411 42.7874C47.3926 42.8093 47.0973 42.7655 47.0098 42.678C46.8458 42.514 46.1348 42.503 45.577 42.6671C44.8223 42.8749 44.0567 43.7937 43.8161 44.7671L43.7395 45.0734L41.3114 45.0187C37.4942 44.9202 20.3661 44.7452 14.8536 44.7343H9.8442L9.85514 43.0062L9.86608 41.289L10.8067 40.4249C11.3208 39.9437 12.9942 38.2812 14.5145 36.739L17.2817 33.928L17.6208 34.3327C17.8176 34.5515 18.4848 35.2515 19.1083 35.8968C20.5739 37.4062 20.6723 37.428 21.7114 36.3234C22.1051 35.9077 22.6739 35.2624 22.9801 34.8796C23.2755 34.4968 23.7567 33.8843 24.0411 33.5343C24.3255 33.1843 24.588 32.8452 24.6208 32.7796C24.6645 32.7249 25.0255 32.878 25.4411 33.1296C26.4911 33.764 27.4098 34.0374 28.6895 34.103C30.0676 34.1687 30.888 34.0046 32.0473 33.4359C33.7208 32.6265 35.077 30.9859 35.613 29.1484C35.8426 28.3499 35.8645 26.6655 35.6567 25.8124C35.4598 24.9702 35.0005 23.9968 34.4755 23.3077L34.0161 22.7062L34.7489 22.0499C35.1426 21.678 35.8208 21.0218 36.2364 20.5843C36.663 20.1468 37.0348 19.8077 37.0786 19.8187C37.1114 19.8405 37.6692 20.4421 38.3036 21.164C39.7911 22.8812 40.0645 23.0999 40.5567 23.0562C40.9067 23.0234 41.1364 22.8155 42.788 21.0546C43.8051 19.9718 44.9755 18.6265 45.402 18.0577L46.1786 17.0405L46.4192 17.7296C46.7036 18.5718 46.9114 18.7249 47.327 18.4187C47.863 18.0249 47.9176 17.7952 47.8848 16.253C47.8301 14.2734 47.9067 14.3171 45.0958 14.6124C44.3848 14.689 43.8051 15.0499 43.8051 15.4218C43.8051 15.6734 43.8708 15.7062 44.7348 15.8702C45.4567 16.0015 45.6098 16.0562 45.413 16.1218C45.0739 16.2312 43.0505 18.189 41.6505 19.764C41.038 20.4421 40.513 20.9999 40.4583 20.9999C40.4145 20.9999 39.6926 20.3109 38.8614 19.4687C37.4067 18.014 37.1223 17.8062 36.7942 18.0249C36.7286 18.0687 36.2364 18.5937 35.7114 19.1952C34.902 20.114 34.1692 21.1202 33.6333 22.039C33.5348 22.214 33.4583 22.1921 32.7583 21.8312C32.3317 21.6124 31.6973 21.3609 31.3364 21.2734C30.6145 21.0874 29.313 21.0546 29.313 21.2187C29.313 21.2734 29.1051 21.2187 28.8536 21.1093C28.3176 20.8687 27.8145 20.8359 26.9942 21.0109C24.6317 21.514 22.8926 23.4062 22.3567 26.064C21.9739 27.9343 22.4989 30.1109 23.6911 31.5655L24.052 32.0249L23.4286 32.4296C23.0895 32.6593 22.313 33.3265 21.7114 33.9171L20.6067 34.989L19.0864 33.4687C17.2161 31.5984 17.2051 31.5874 16.2317 32.6265C14.0114 34.9999 10.5442 38.9702 10.1614 39.5718L9.8442 40.0749L9.82233 35.0437C9.81139 32.2874 9.80045 29.8046 9.80045 29.5312C9.8442 27.2234 9.46139 14.1312 9.34108 13.7046C9.28639 13.5077 9.33014 13.464 9.64733 13.3984C10.6536 13.1796 11.7583 12.228 12.0755 11.2984C12.2723 10.7187 12.3051 9.65773 12.1411 9.05617C11.9661 8.45461 11.3864 7.74367 10.8286 7.45929C10.4567 7.27335 10.1833 7.21867 9.68014 7.22961C9.3192 7.24054 8.91452 7.19679 8.78327 7.13117C8.50983 6.97804 7.75514 6.97804 7.27389 7.12023ZM10.1067 8.25773C10.4786 8.49836 10.938 9.41711 10.938 9.92023C10.938 10.6421 10.5551 11.2327 9.82233 11.6046C8.49889 12.2827 6.92389 11.1671 7.15358 9.72335C7.29577 8.85929 7.71139 8.26867 8.50983 7.82023L8.90358 7.60148L9.35202 7.82023C9.59264 7.95148 9.9317 8.14835 10.1067 8.25773ZM29.7505 21.6562C30.0786 21.7218 30.4505 21.8202 30.5708 21.8749C30.6911 21.9296 30.9098 22.0062 31.063 22.0499C31.4895 22.1593 32.6489 23.0343 33.0645 23.5484C33.6223 24.2265 34.0926 25.1234 34.2895 25.8671C35.1645 29.2359 32.5286 32.5499 28.9848 32.5062C25.977 32.4843 23.5051 29.5859 23.9426 26.5999C24.0848 25.6593 24.4458 24.7296 24.9926 23.9093C25.452 23.2202 25.9114 22.7827 26.6551 22.3562C27.3442 21.9405 29.0176 21.3171 29.0942 21.4374C29.127 21.4921 29.4223 21.5905 29.7505 21.6562ZM47.863 43.4874C48.3551 43.6952 48.9567 44.439 49.0989 45.0187C49.4051 46.2218 48.4973 47.3484 47.1958 47.3812C46.3208 47.3921 45.5223 46.6702 45.413 45.7734C45.3145 44.8218 45.8286 43.8812 46.7036 43.4218C47.0864 43.214 47.2176 43.2249 47.863 43.4874Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M28.4922 23.1219C28.3391 23.2313 28.3062 23.3516 28.3609 23.6688C28.4047 23.9313 28.3828 24.0625 28.2953 24.0625C28.2188 24.0625 27.9125 24.1828 27.5953 24.325C26.8297 24.686 26.4688 25.2219 26.4688 26.0313C26.4688 27.2125 27.3219 27.9453 28.8641 28.0985C29.3891 28.1532 29.8703 28.2735 30.0453 28.3828C30.3297 28.5688 30.3297 28.5797 30.1219 28.7328C29.75 29.0063 28.8422 29.2032 28.0328 29.1813C27.5953 29.1703 27.2125 29.1922 27.1687 29.225C27.0375 29.3672 27.4203 29.9141 27.8687 30.2203C28.2734 30.4938 28.3281 30.5813 28.3281 30.9422C28.3281 31.6422 28.8094 31.8828 29.6297 31.5985C29.9688 31.4782 29.9906 31.4453 29.9469 31.0516C29.9141 30.636 29.9141 30.625 30.4391 30.45C31.4234 30.1219 31.9375 29.4875 31.9375 28.6016C31.9375 28.0219 31.7625 27.6391 31.3141 27.2125C30.9094 26.8297 30.3187 26.6219 29.3125 26.5235C28.4594 26.436 27.8906 26.2282 27.8906 25.9985C27.9016 25.55 28.6016 25.2766 29.9797 25.1891C31.0297 25.1235 31.3906 24.9922 31.2266 24.7297C31.1609 24.6094 30.625 24.3469 30.2203 24.2266C30.0125 24.1719 29.9688 24.0844 29.9688 23.7125C29.9688 23.4172 29.9031 23.2203 29.7828 23.1219C29.5312 22.936 28.7547 22.925 28.4922 23.1219Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.0321 26.0422C39.704 26.6328 40.1524 26.9937 41.0493 26.8625C41.5962 26.775 41.6509 26.6766 41.443 26.2062C41.2352 25.7578 40.2399 25.6375 40.0321 26.0422Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.0536 29.2249C39.8676 29.4108 39.8895 29.9358 40.0973 30.0124C40.4473 30.1437 41.4536 30.089 41.5192 29.9249C41.5848 29.7499 41.3879 29.3124 41.1911 29.1812C40.9942 29.0499 40.1958 29.0827 40.0536 29.2249Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.1085 32.4516C39.8788 32.7141 39.8679 33.2062 40.0976 33.2937C40.196 33.3266 40.4804 33.3594 40.7429 33.3594C41.3554 33.3594 41.5632 33.2719 41.5632 33.0312C41.5632 32.4625 40.4913 32.0359 40.1085 32.4516Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M30.0349 35.7874C29.849 36.0608 29.8927 36.3999 30.1224 36.553C30.4396 36.7718 31.6099 36.564 31.6099 36.2905C31.599 35.9515 31.2052 35.6343 30.713 35.5796C30.2646 35.5249 30.188 35.5468 30.0349 35.7874Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.0422 35.7436C39.7141 36.3671 40.0641 36.7061 40.9391 36.6077C41.5844 36.5311 41.6063 36.5092 41.5188 36.1811C41.4094 35.7436 41.2891 35.6561 40.7204 35.5796C40.2282 35.5249 40.1516 35.5467 40.0422 35.7436Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M20.0814 39.003C19.8518 39.2218 19.8518 39.6593 20.0814 39.7468C20.4096 39.8671 21.5252 39.8233 21.5799 39.6702C21.6455 39.4843 21.3721 38.9811 21.1643 38.8936C20.8361 38.7733 20.2564 38.828 20.0814 39.003Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M30.0242 39.0467C29.8602 39.2326 29.8492 39.3092 29.9477 39.5389C30.068 39.8123 30.1227 39.8232 30.8117 39.7904C31.6649 39.7576 31.7852 39.5935 31.3477 39.1232C30.9977 38.7514 30.3305 38.7076 30.0242 39.0467Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.0425 38.9811C39.966 39.0686 39.9222 39.2764 39.9441 39.4514C39.9769 39.7467 39.9988 39.7576 40.7207 39.7905C41.2894 39.8123 41.4754 39.7905 41.5191 39.6592C41.5847 39.4951 41.3879 39.0467 41.191 38.9155C40.9832 38.7733 40.1738 38.817 40.0425 38.9811Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M20.0162 42.3172C19.8521 42.6344 19.8849 42.9516 20.0818 43.0281C20.1802 43.0609 20.5084 43.0938 20.8365 43.0938C21.5912 43.0938 21.7662 42.9078 21.4381 42.4375C21.2302 42.1531 21.1427 42.1094 20.6724 42.1094C20.2459 42.1094 20.1037 42.1531 20.0162 42.3172Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M29.9688 42.3172C29.7063 42.8094 30.0016 43.0938 30.7673 43.0938C31.5876 43.0938 31.7079 42.9844 31.4673 42.5031C31.2923 42.1203 31.2594 42.1094 30.6907 42.1094C30.2094 42.1094 30.0673 42.1531 29.9688 42.3172Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M40.0531 42.2405C39.9766 42.317 39.9219 42.5139 39.9219 42.678C39.9219 43.0389 40.1516 43.1264 40.9719 43.0717C41.5844 43.0389 41.7047 42.8311 41.4094 42.3827C41.2563 42.153 41.1359 42.1092 40.7094 42.1092C40.4141 42.1092 40.1297 42.1639 40.0531 42.2405Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default scalable;
