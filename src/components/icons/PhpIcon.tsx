import React from 'react';

const PhpIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131078)"
                transform="translate(-0.00500488 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131078"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131078" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131078"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default PhpIcon;
