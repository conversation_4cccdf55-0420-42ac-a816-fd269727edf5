import React from 'react';

const HtmlIcone = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131090)"
                transform="translate(-0.00500488 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131090"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131090" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131090"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default HtmlIcone;
