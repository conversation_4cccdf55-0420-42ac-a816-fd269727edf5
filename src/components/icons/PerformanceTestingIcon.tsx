import React from 'react';

const PerformanceTestingIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M37.2558 1.62111C35.7128 1.95314 34.4042 2.70509 33.1152 4.01369L32.2753 4.85353L25.1953 4.72658C21.2988 4.65822 15.7421 4.57033 12.8417 4.53127C7.57806 4.47267 7.5683 4.47267 6.9433 4.67775C6.18158 4.94142 5.664 5.28322 5.0683 5.92775C4.57025 6.46486 4.37494 6.78712 4.07221 7.60744C3.87689 8.13478 3.87689 8.43752 3.789 17.9199C3.74994 23.291 3.72064 31.5039 3.73041 36.1817C3.75971 44.6192 3.75971 44.6777 3.97455 45.2344C4.45306 46.4942 5.30267 47.4121 6.52338 47.9981C7.539 48.4766 7.04096 48.4668 22.1581 48.3496C30.4101 48.2813 36.5429 48.1934 36.9042 48.1348C37.8027 47.9981 38.5253 47.6074 39.2675 46.875C39.9511 46.2012 40.3124 45.5469 40.5273 44.6289C40.664 44.0625 40.6249 39.2383 40.4296 32.7637C40.332 29.4434 40.0488 19.3067 40.0097 17.666L39.9902 16.6309L40.5273 16.5137C40.8203 16.4453 41.4746 16.1914 41.9726 15.957C44.0624 14.9512 45.7226 12.6758 46.0937 10.3223C46.5624 7.42189 45.4199 4.4922 43.2128 2.91017C41.455 1.65041 39.3066 1.19142 37.2558 1.62111ZM39.7167 3.2715C41.3867 3.70119 42.998 4.94142 43.6816 6.34767C44.1601 7.31447 44.3359 8.06643 44.3554 9.13088C44.3847 10.8496 43.8769 12.0899 42.6367 13.3301C41.455 14.5117 40.2441 15.0781 38.5839 15.2051C36.5038 15.3613 34.2187 14.1406 33.1249 12.2852C32.0507 10.4688 31.9335 8.10548 32.8222 6.31837C34.0917 3.78908 36.8749 2.55861 39.7167 3.2715ZM31.9238 5.92775C31.3476 7.08986 31.2011 7.75393 31.2011 9.22853C31.2011 10.2832 31.2402 10.625 31.4355 11.2305C32.0996 13.3692 33.5546 15.0586 35.6152 16.0938C36.6113 16.6016 37.4902 16.7969 38.7402 16.7969H39.8437L39.8535 17.3047C39.8632 17.5879 39.8632 17.9785 39.8535 18.1641C39.8437 18.3496 39.8046 20.1074 39.746 22.0703C39.6093 27.2363 39.4628 33.5156 39.3554 39.1895C39.2382 44.7168 39.2382 44.7461 38.5351 45.5664C38.1445 46.0254 37.2167 46.5527 36.6601 46.6309C36.455 46.6602 29.9121 46.6504 22.1191 46.6016L7.95892 46.5332L7.45111 46.2988C6.87494 46.0449 6.19135 45.3613 5.83978 44.7168L5.61517 44.2871L5.55658 32.1777C5.51752 25.5176 5.45892 17.4317 5.41986 14.209C5.34174 8.78908 5.3515 8.31056 5.50775 7.84181C5.74213 7.12892 6.39642 6.4258 7.20697 6.03517L7.86127 5.71291L12.7441 5.65431C16.7382 5.60548 26.2011 5.36134 29.1503 5.24416C29.5312 5.22462 30.3906 5.20509 31.0644 5.19533L32.2949 5.1758L31.9238 5.92775Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M41.7965 6.23082C40.7808 6.55309 38.6031 7.9984 37.3726 9.17028L36.9137 9.60973L35.9859 8.69176C35.4781 8.18395 35.0387 7.80309 35.0094 7.83239C34.8629 7.96911 36.1812 10.8695 36.4937 11.1039C36.816 11.348 37.1187 11.2308 38.0367 10.5472C39.0328 9.80504 40.566 8.25231 41.4449 7.09997C41.9918 6.37731 42.0992 6.13317 41.7965 6.23082Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M13.7598 9.87347C13.1445 9.91254 12.4805 9.97113 12.2949 10.0102C11.4844 10.1664 10.4492 10.4789 10.4492 10.5571C10.4492 10.6547 11.5137 10.977 12.4023 11.143C13.2812 11.309 16.6406 11.309 17.5781 11.143C18.4863 10.977 19.5312 10.6645 19.5312 10.5571C19.5312 10.4496 18.2031 10.0883 17.334 9.97113C16.3965 9.83441 15.1074 9.80511 13.7598 9.87347Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M14.2083 13.8769C12.5189 14.0038 10.3997 14.3945 10.4974 14.5507C10.5658 14.6581 12.5482 15.0195 13.7591 15.1367C14.3158 15.1855 15.8099 15.2343 17.0892 15.2343C18.3685 15.2343 19.8626 15.1855 20.4193 15.1367C21.6302 15.0195 23.6126 14.6581 23.681 14.5507C23.7786 14.3847 21.5423 14.0038 19.7259 13.8671C18.2318 13.7597 15.6536 13.7597 14.2083 13.8769Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.7141 17.9785C28.1066 18.4667 25.0109 19.414 25.4406 19.5898C25.6848 19.6972 28.507 19.6972 29.9719 19.5995L31.0852 19.5214L31.0168 19.9413C30.8117 21.3085 30.841 25.1953 31.0559 25.1953C31.1633 25.1953 31.5832 23.9941 31.9836 22.5097C32.2961 21.3769 32.8137 18.8476 32.8137 18.4863C32.8137 18.4081 32.6867 18.2128 32.5305 18.0566C32.1984 17.7245 32.0813 17.7148 30.7141 17.9785Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M29.0914 21.4646C28.1442 22.4705 26.7672 23.9646 26.025 24.7751C25.2829 25.5955 24.6383 26.2693 24.5895 26.2693C24.5407 26.2693 23.6129 25.7712 22.529 25.156C19.2672 23.3298 19.1012 23.2419 18.7204 23.2419C18.3981 23.2419 18.2809 23.3396 17.3043 24.3845C15.2731 26.5623 12.4704 29.99 12.6168 30.1267C12.6461 30.156 13.3395 29.6287 14.1696 28.9646C15.5172 27.8904 16.2008 27.2947 18.32 25.3611L18.9157 24.8142L20.4196 25.6443C22.6754 26.8943 24.5797 27.8318 24.8434 27.8318C25.1559 27.8318 25.1852 27.8025 26.6012 25.9373C28.0661 24.0134 29.2282 22.3826 30.2145 20.8884C31.3864 19.0915 31.2789 19.1501 29.0914 21.4646Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.9574 27.7543C30.2543 31.0551 30.1762 35.8012 30.7719 39.2972C30.8793 39.932 30.9574 40.4594 30.9379 40.4691C30.9184 40.4789 30.186 40.4594 29.2973 40.4203C28.4086 40.3812 27.1195 40.3422 26.4262 40.3422C25.2738 40.3324 25.1762 40.3226 25.2445 40.1566C25.7035 38.9457 25.7817 35.8207 25.3813 34.473C25.1664 33.7601 24.9809 33.3012 24.9028 33.3012C24.7367 33.3012 24.3266 34.8246 24.2192 35.85C24.0824 37.0414 24.1606 38.516 24.4145 39.5023C24.5121 39.8734 24.5805 40.1957 24.5707 40.2054C24.5707 40.2054 23.3012 40.2347 21.7582 40.2543C19.2973 40.2836 18.9555 40.264 18.9945 40.1371C19.307 39.0824 19.3852 38.1156 19.3852 35.4008C19.3852 32.4711 19.3168 31.5922 18.936 29.9515C18.7309 29.0922 18.6332 28.9652 18.5258 29.4633C18.4867 29.6195 18.3988 30.0492 18.311 30.4301C18.1156 31.3578 17.8715 33.975 17.8715 35.1469C17.8715 36.2992 18.0278 37.9984 18.2524 39.3265C18.3988 40.225 18.3988 40.3324 18.2621 40.3324C18.0082 40.3324 14.3461 40.5179 13.4672 40.5765L12.6274 40.6351L12.8715 39.8246C13.1059 39.0824 13.1254 38.8676 13.1254 37.1097C13.1352 35.391 13.1156 35.1273 12.9106 34.4047C12.7836 33.9652 12.6176 33.5258 12.5492 33.4281C12.4223 33.2621 12.4028 33.2816 12.2758 33.5941C11.8559 34.5804 11.7778 35.1664 11.7778 37.1097C11.7778 38.8676 11.7973 39.0726 12.0219 39.8148L12.2758 40.6058L11.5824 40.6742C10.3715 40.7816 9.22892 40.9379 9.10197 41.0062C9.02385 41.0453 9.40471 41.1332 9.93205 41.1918C14.2778 41.7191 22.1098 41.934 27.7836 41.6996C31.309 41.5531 35.6938 41.1527 35.4399 41.016C35.3227 40.9476 31.9926 40.6254 31.4653 40.6254C31.2797 40.6254 31.2797 40.5961 31.4555 39.5804C32.061 36.0551 32.0512 31.7875 31.436 28.4379C31.3188 27.8324 31.1918 27.3246 31.1528 27.2953C31.1039 27.266 31.0258 27.4808 30.9574 27.7543Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default PerformanceTestingIcon;
