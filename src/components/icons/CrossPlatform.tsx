import React from 'react';

const CrossPlatform = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M25.6284 5.05927C21.8194 5.90145 19.044 9.17446 18.8239 13.0982C18.6516 16.0363 19.9341 19.1849 22.585 22.3431C23.4942 23.4341 26.3461 26.1903 26.7672 26.3913C27.1883 26.5922 27.8008 26.5922 28.2219 26.3913C28.6238 26.1999 31.4375 23.4819 32.3562 22.4101C35.0168 19.2615 36.3375 16.0554 36.1652 13.1365C35.9451 9.14575 33.1506 5.86317 29.2938 5.05927C28.4707 4.88701 26.4227 4.88701 25.6284 5.05927Z"
                fill="#EBEBEB"
            />
            <path
                d="M10.4385 20.8115C6.77307 21.3474 3.88286 24.0367 3.13638 27.5968C2.95454 28.4677 2.95454 30.353 3.13638 31.2143C3.94028 35.0424 7.12717 37.7891 11.1275 38.0857C13.6637 38.2771 16.5443 37.2531 19.4058 35.1381C20.6212 34.2385 24.1909 30.7262 24.4589 30.1616C24.6599 29.7309 24.6694 29.1089 24.4685 28.6878C24.2771 28.2858 21.5591 25.4722 20.4872 24.5535C17.0611 21.6633 13.5584 20.3521 10.4385 20.8115Z"
                fill="#091542"
            />
            <path
                d="M41.7323 20.8501C39.4259 21.2233 36.9089 22.5153 34.4972 24.5538C33.4253 25.4725 30.7074 28.2862 30.516 28.6881C30.315 29.1092 30.315 29.7217 30.516 30.1428C30.7074 30.5447 33.4253 33.3584 34.4972 34.2771C38.2679 37.464 41.9907 38.6698 45.4647 37.8372C47.9817 37.2343 50.2307 35.3585 51.293 32.9756C52.1734 30.9945 52.2404 28.3532 51.4652 26.2956C50.4986 23.7212 48.1539 21.6349 45.4838 20.9937C44.412 20.7353 42.8042 20.6683 41.7323 20.8501Z"
                fill="#091542"
            />
            <path
                d="M26.7477 32.4492C26.3458 32.6406 23.5226 35.3777 22.6325 36.4208C18.5173 41.292 17.6751 46.2685 20.3165 50.1923C21.1396 51.4077 22.5847 52.6136 23.9341 53.2165C26.0587 54.1544 28.9297 54.1544 31.0543 53.2165C33.4373 52.1542 35.3035 49.9148 35.916 47.3883C36.7582 43.9334 35.5428 40.1915 32.3559 36.4208C31.4371 35.3489 28.6235 32.631 28.2215 32.4396C27.81 32.2482 27.1497 32.2482 26.7477 32.4492Z"
                fill="#D7D7D7"
            />
        </svg>
    );
};

export default CrossPlatform;
