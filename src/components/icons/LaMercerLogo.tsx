import React from 'react';

const LaMercerLogo = ({ className = '' }) => {
    return (
        <svg
            height="80"
            width="458"
            fill="none"
            viewBox="0 0 458 118"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect height="117.436" width="458" fill="url(#pattern0_1564_779921)" />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_1564_779921"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.00320513 0.0125)" xlinkHref="#image0_1564_779921" />
                </pattern>
                <image
                    height="80"
                    id="image0_1564_779921"
                    width="312"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default LaMercerLogo;
