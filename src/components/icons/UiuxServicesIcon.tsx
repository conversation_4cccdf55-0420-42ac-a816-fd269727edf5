import React from 'react';

const UiuxServicesIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_446_138364)">
                <path
                    d="M7.05469 1.06524C6.19063 1.28399 5.34844 2.06055 5.12969 2.84805C4.95469 3.5043 5.075 4.3793 5.41407 4.96992C6.44219 6.71992 9.22032 6.53399 10.0078 4.65273L10.1719 4.24805H15.3453C20.4203 4.25899 20.4969 4.25899 20.0156 4.45586C13.9891 6.85117 9.28594 12.2105 7.64532 18.5215C7.29532 19.8996 7 21.7699 7 22.6668V23.2793H6.23438C5.02032 23.2793 5.03125 23.2574 5.03125 25.8824C5.03125 27.8512 5.05313 28.0699 5.22813 28.234C5.40313 28.398 5.74219 28.4199 7.67813 28.4199C9.63594 28.4199 9.94219 28.398 10.0734 28.234C10.1938 28.1027 10.2266 27.5559 10.2266 25.8277C10.2266 23.2027 10.2594 23.2793 9.05625 23.2793H8.32344L8.37813 22.5355C8.87032 16.4105 12.0313 10.9746 17.0625 7.6168C19.4141 6.05273 21.9844 5.04648 24.9703 4.53242L25.375 4.45586V5.12305C25.375 6.2168 25.3859 6.2168 28 6.2168C30.6141 6.2168 30.625 6.2168 30.625 5.12305V4.45586L31.0406 4.52148C32.0906 4.69649 34.1906 5.27617 35.2188 5.66992C40.3703 7.67148 44.4609 11.8496 46.4188 17.0668C47.0531 18.7621 47.4141 20.4574 47.6328 22.6449L47.6875 23.2793H46.9438C45.7406 23.2793 45.7734 23.1918 45.7734 25.8277C45.7734 27.5559 45.8063 28.1027 45.9266 28.234C46.0578 28.398 46.3641 28.4199 48.3438 28.4199C50.4 28.4199 50.6188 28.398 50.7828 28.223C50.9469 28.048 50.9688 27.7199 50.9688 25.8715C50.9688 23.2684 50.9797 23.2793 49.7875 23.2793H49.0328L48.9672 22.1965C48.825 19.9434 48.0813 17.1324 47.0422 14.9121C44.8109 10.198 40.7422 6.34805 35.9844 4.45586C35.5031 4.25899 35.5797 4.25899 40.6547 4.24805H45.8281L45.9922 4.65273C46.7797 6.53399 49.5578 6.71992 50.5859 4.96992C51.1547 3.99648 51.0563 2.72774 50.3453 1.94024C49.0656 0.51836 46.8453 0.780859 46.0469 2.44336L45.8063 2.93555H38.2156H30.625V2.22461C30.625 1.60117 30.5922 1.46992 30.3516 1.24023C30.0891 0.966797 30.0672 0.966797 28 0.966797C25.9328 0.966797 25.9109 0.966797 25.6484 1.24023C25.4078 1.46992 25.375 1.60117 25.375 2.22461V2.93555H17.7844H10.1938L9.95313 2.44336C9.64688 1.81992 9.06719 1.33867 8.37813 1.13086C7.79844 0.95586 7.56875 0.944921 7.05469 1.06524Z"
                    fill="var(--color-primary-900)"
                />
                <path
                    d="M26.3812 8.69961C26.1734 8.77617 14 33.1777 14 33.5059C14 33.659 15.05 35.8684 16.3187 38.4277L18.6484 43.0762H28H37.3516L39.6812 38.4277C40.95 35.8684 42 33.659 42 33.5059C42 33.3527 39.2437 27.7199 35.875 20.9824C30.6359 10.5043 29.7172 8.73242 29.4875 8.6668C29.1047 8.5793 28.8203 8.69961 28.6781 9.0168C28.5797 9.21367 28.5469 12.0902 28.5469 19.7355V30.1809L28.8094 30.4215C28.9516 30.5527 29.2688 30.7934 29.4984 30.9465C30.3844 31.5371 30.7234 32.9809 30.2203 33.9434C29.0062 36.2402 25.5719 35.4746 25.55 32.9043C25.5391 32.1059 25.9109 31.3402 26.5016 30.9465C26.7312 30.7934 27.0484 30.5527 27.1906 30.4215L27.4531 30.1809V19.7246C27.4531 9.20274 27.4422 8.95117 27.0813 8.71055C26.95 8.61211 26.6 8.61211 26.3812 8.69961Z"
                    fill="var(--color-primary-900)"
                />
                <path
                    d="M16.7016 44.4762C16.5375 44.6512 16.5156 44.9793 16.5156 46.8387C16.5156 48.1184 16.5594 49.0809 16.625 49.2121C16.7344 49.4199 16.9859 49.4199 28 49.4199C39.0141 49.4199 39.2656 49.4199 39.375 49.2121C39.4406 49.0809 39.4844 48.1074 39.4844 46.8168C39.4844 44.848 39.4625 44.6293 39.2875 44.4652C39.1016 44.2902 38.1609 44.2793 27.9781 44.2793C17.3578 44.2793 16.8656 44.2902 16.7016 44.4762Z"
                    fill="#BCB8B8"
                />
                <path
                    d="M18.375 52.9746C18.375 55.3371 18.375 55.3371 18.6484 55.5996L18.9109 55.873H28H37.0891L37.3516 55.5996C37.625 55.3371 37.625 55.3371 37.625 52.9746V50.623H28H18.375V52.9746Z"
                    fill="#BCB8B8"
                />
            </g>
            <defs>
                <clipPath id="clip0_446_138364">
                    <rect height="56" width="56" fill="white" transform="translate(0 0.419922)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default UiuxServicesIcon;
