import React from 'react';

const apppwa = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M7.42188 5.86368C6.65039 6.09805 5.86914 6.75235 5.48828 7.47501L5.22461 7.97305L5.25391 14.6625C5.26367 18.3441 5.32227 21.5863 5.37109 21.8695C5.54688 22.8559 6.08398 23.5981 6.95312 24.057C7.54883 24.3598 8.56445 24.3793 13.1348 24.1938C17.5195 23.9984 19.9121 23.8324 21.6406 23.5883C22.2949 23.5004 23.0371 22.7484 23.2422 21.9867C23.4668 21.1371 23.8965 9.10587 23.7305 8.24649C23.6133 7.58243 23.418 7.2211 22.9492 6.75235C22.1777 5.98087 22.0215 5.9418 19.3848 5.86368C16.2109 5.77579 7.72461 5.77579 7.42188 5.86368ZM21.2891 8.00235C21.8848 8.29532 21.9336 8.51993 22.002 11.0688C22.041 12.3188 22.1387 15.268 22.2363 17.6215L22.4023 21.9086L22.168 22.3578C22.002 22.6606 21.7676 22.9047 21.4551 23.1L20.9961 23.3832L20.0391 23.2758C18.4375 23.0805 16.6211 22.9731 12.4512 22.8168C10.2539 22.7387 8.31055 22.6508 8.14453 22.6215C7.8125 22.5629 7.38281 22.1723 7.24609 21.7914C7.19727 21.6449 7.1875 20.0141 7.23633 17.7875C7.27539 15.7172 7.31445 12.8461 7.31445 11.4106C7.32422 8.60782 7.36328 8.37345 7.8418 8.10001C8.00781 8.00235 9.375 7.96329 13.7207 7.92423C16.8359 7.89493 19.7363 7.86563 20.166 7.8461C20.752 7.83634 21.0352 7.86563 21.2891 8.00235Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M33.2416 6.0199C31.5912 6.43982 30.2728 7.17224 29.0424 8.33435C27.265 10.0238 26.3666 11.9867 26.2103 14.516C26.1127 16.1371 26.4349 17.641 27.2162 19.1742C28.2513 21.1957 30.1459 22.7875 32.3236 23.4711C33.0072 23.6761 33.2123 23.6957 35.3998 23.6957C37.5873 23.6957 37.7924 23.6761 38.476 23.4711C41.4252 22.5433 43.6127 20.2386 44.3939 17.2406C44.7357 15.9222 44.7553 14.1449 44.4428 12.8656C43.6127 9.44763 41.0638 6.88904 37.6166 6.01013C36.3275 5.6781 34.5209 5.68787 33.2416 6.0199ZM36.3861 8.03162C40.39 8.58826 43.3197 12.3676 42.7924 16.3031C42.558 18.0316 41.8939 19.4183 40.683 20.6879C39.5209 21.9281 38.5248 22.4945 36.6205 23.0511L35.39 23.4125L34.4135 23.139C31.7377 22.3676 29.9603 20.9613 28.8178 18.6761C28.3197 17.6898 28.1049 16.7426 28.1049 15.4437C28.1049 14.0668 28.3197 13.1683 28.9447 11.9476C29.9799 9.96521 32.0013 8.4906 34.2474 8.08044C35.2045 7.90466 35.4584 7.8949 36.3861 8.03162Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M7.32453 26.7327C6.3382 27.094 5.62531 27.8069 5.31281 28.7444C5.16633 29.2034 5.15656 29.7893 5.21516 35.6975C5.25422 39.2718 5.33234 42.4163 5.38117 42.7386C5.55695 43.7542 6.17219 44.5452 7.11945 44.9651C7.51984 45.1409 7.74445 45.1604 9.03352 45.1507C12.1292 45.1116 18.0277 44.8186 19.6292 44.6331C20.0882 44.5745 20.7425 44.5061 21.0941 44.4768C21.4456 44.4475 21.8851 44.3401 22.0804 44.2425C22.598 43.9886 23.1155 43.344 23.2327 42.8069C23.4769 41.7229 23.9066 29.8577 23.7308 29.0667C23.5745 28.3831 23.3011 27.9241 22.764 27.4358C21.9144 26.6643 22.139 26.6936 14.5023 26.6253C8.39875 26.5764 7.72492 26.5862 7.32453 26.7327ZM21.3773 28.8714C21.8948 29.135 21.9437 29.4085 22.0218 32.2893C22.0511 33.7346 22.1488 36.3518 22.2269 38.0999C22.4222 42.5628 22.4124 42.8753 22.139 43.2854C21.8655 43.6858 21.1917 44.1741 20.889 44.1839C20.762 44.1839 20.264 44.135 19.7757 44.0764C18.5257 43.9202 16.1429 43.7835 11.846 43.6175C7.82258 43.471 7.72492 43.4514 7.36359 42.9143C7.14875 42.5823 7.15852 43.0315 7.26594 35.512C7.35383 29.2718 7.36359 29.1643 7.78352 28.9593C8.06672 28.8225 10.4105 28.7639 15.9476 28.7444C20.2933 28.7249 21.1429 28.7444 21.3773 28.8714Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M28.1551 26.7523C27.4519 27.0062 26.9051 27.4066 26.5633 27.9241C25.9578 28.8421 25.9578 28.7933 26.0262 34.8773C26.0652 37.9144 26.1238 41.0101 26.1629 41.7718C26.2312 43.0218 26.2605 43.1878 26.5144 43.6566C26.8562 44.2718 27.2078 44.5843 27.9305 44.9261C28.4383 45.1605 28.5457 45.1702 29.8836 45.1507C33.1062 45.1019 39.0535 44.8089 40.5281 44.6331C40.9871 44.5745 41.6805 44.4964 42.0711 44.4573C43.3308 44.3109 43.9851 43.6273 44.1512 42.2698C44.2586 41.2933 44.4051 37.9925 44.5418 33.3734C44.6297 30.3851 44.6297 29.3695 44.5418 28.9691C44.3172 27.9534 43.5066 27.0941 42.5301 26.8206C42.1394 26.7132 40.6551 26.6741 35.3523 26.6253C28.9851 26.5667 28.6433 26.5765 28.1551 26.7523ZM42.2664 28.8812C42.7254 29.2034 42.7449 29.3695 42.8621 33.0218C42.9305 34.9261 43.0379 37.8753 43.116 39.5648C43.243 42.4945 43.243 42.6507 43.0769 43.0316C42.9793 43.2464 42.7742 43.5394 42.618 43.6859C42.3152 43.9691 41.6414 44.2913 41.5144 44.2132C41.2312 44.0374 37.989 43.8226 32.618 43.6175C28.5457 43.471 28.6531 43.4808 28.2039 42.8753C28.0379 42.6507 28.0281 42.3284 28.0965 36.0296L28.1746 29.4085L28.448 29.1448C28.7117 28.8812 28.741 28.8714 30.157 28.8128C30.948 28.7835 33.9461 28.7542 36.8172 28.7445C41.4168 28.7249 42.0711 28.7445 42.2664 28.8812Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default apppwa;
