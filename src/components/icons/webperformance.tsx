import React from 'react';

const webperformance = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M36.3083 3.6468C36.2204 3.71516 35.8102 4.35969 35.4001 5.07258C34.7849 6.1175 34.6677 6.40071 34.7751 6.46907C34.8923 6.53743 34.8825 6.55696 34.7556 6.55696C34.6677 6.55696 34.4235 6.68391 34.2087 6.84016C33.4372 7.3968 33.2517 8.24641 33.0661 11.9769L32.9587 14.3206L32.031 14.8089C30.4392 15.6488 29.5993 16.2152 29.5212 16.5081C29.4821 16.6448 29.4431 18.0316 29.4431 19.5745V22.3968L29.7165 22.6312C29.863 22.7581 30.0876 22.8656 30.2048 22.8656C30.5173 22.8656 31.8356 22.0159 32.7048 21.264L33.447 20.6195L34.7458 20.5706L36.0446 20.5218L35.986 22.6409C35.9567 23.8128 35.8884 26.8988 35.8395 29.5062C35.7907 32.1136 35.7028 34.5648 35.6442 34.9652C35.3708 36.7718 34.4333 38.9886 33.2907 40.5413C32.1286 42.1331 30.1852 43.8031 28.5642 44.6234C25.3415 46.2347 21.2985 46.4202 18.0661 45.1019C14.9802 43.8421 12.1677 41.3128 10.8395 38.598C10.488 37.8948 9.93134 36.6937 9.7165 36.1956L9.55048 35.805L9.45283 36.098C9.16962 37.0062 9.87275 39.4866 10.8786 41.1761C12.6364 44.1156 16.2106 46.596 19.7263 47.3187C23.5153 48.0902 27.3337 47.3675 30.6149 45.2484C33.7302 43.2464 36.1227 39.7113 36.8845 35.971C37.0407 35.2191 37.0602 34.5745 37.0505 30.4827C37.0407 27.0648 36.9919 25.346 36.8747 23.9398C36.7868 22.8948 36.6794 21.6937 36.6501 21.2738L36.5915 20.5218H38.0563H39.531L39.7849 20.8538C40.029 21.1761 41.6794 22.4066 42.2946 22.7191C42.8806 23.0316 43.4958 22.6605 43.4958 22.0062C43.5056 20.8831 43.32 16.5374 43.2614 16.3812C43.1735 16.137 41.0251 14.8968 40.361 14.7015L39.8435 14.555V11.6546C39.8435 8.55891 39.7653 7.76789 39.4138 6.99641C39.0915 6.29329 36.8161 3.52961 36.5622 3.52961C36.5036 3.52961 36.3962 3.58821 36.3083 3.6468ZM37.2067 5.98079C38.0368 7.18196 38.2907 7.61165 38.3884 7.96321C38.4665 8.24641 38.4763 9.90657 38.4274 13.6566L38.3493 18.9593H36.5134H34.6677L34.6579 18.6859C34.6579 18.5394 34.6091 16.7718 34.5602 14.7601C34.5114 12.7484 34.4431 10.4046 34.4138 9.56477C34.3649 8.30501 34.3845 7.91438 34.5212 7.34797C34.6286 6.88899 34.7263 6.68391 34.8044 6.71321C34.8727 6.7425 35.0974 6.57649 35.3024 6.34211C35.4977 6.10774 35.7614 5.81477 35.8786 5.67805C36.0056 5.54133 36.1813 5.30696 36.279 5.16047L36.4548 4.88704L36.7087 5.25813C36.8454 5.46321 37.07 5.78547 37.2067 5.98079ZM33.0075 17.6409V19.7308L32.5974 19.8382C32.363 19.9066 31.9431 20.0823 31.6501 20.2386C31.3571 20.3948 31.0935 20.5023 31.0739 20.473C31.0446 20.4437 30.986 19.6917 30.9372 18.8031C30.8786 17.6409 30.8786 17.1429 30.9567 17.0648C31.0544 16.9671 32.9196 15.5706 32.988 15.5511C32.9977 15.5413 33.0075 16.4886 33.0075 17.6409ZM40.3317 15.9417C40.361 15.971 40.7126 16.2249 41.1227 16.4984L41.8649 17.0159V18.7542L41.8747 20.5023L41.4157 20.307C41.1716 20.1995 40.7321 20.0042 40.4587 19.8773L39.9411 19.6429V18.4906C39.9411 17.846 39.9118 16.9183 39.8825 16.4105L39.8142 15.4925L40.0485 15.6878C40.1755 15.7952 40.3024 15.9124 40.3317 15.9417Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M19.4922 6.76205C18.9649 6.889 18.5157 7.25033 18.2715 7.75814C18.086 8.14877 18.0664 8.34408 18.0664 10.3851V12.5824L17.5098 12.5628C17.1973 12.5531 16.2793 12.5042 15.459 12.4652C13.7989 12.3773 13.3594 12.4652 12.8614 12.9827C12.3731 13.4906 12.3145 13.8031 12.2461 16.1663C12.2071 17.3773 12.1485 18.3734 12.1192 18.3734C12.0899 18.3734 11.7969 18.305 11.4746 18.2269C11.1524 18.1488 10.2735 18.0609 9.52152 18.0218L8.15433 17.9632L7.61722 18.2464C6.87504 18.637 6.52347 19.2132 6.50394 20.0531C6.47465 21.7132 6.66996 29.1156 6.75785 29.4574C6.89457 30.014 7.28519 30.473 7.80277 30.7074C8.20316 30.8831 8.36918 30.8929 9.668 30.8343C10.4493 30.805 11.3965 30.7171 11.7676 30.639C12.1485 30.5609 12.461 30.512 12.4707 30.5218C12.4903 30.5316 12.4317 30.9417 12.3438 31.4202C12.1192 32.68 12.2071 34.6136 12.5391 35.8343C13.0176 37.5628 13.8379 39.0179 15.0489 40.2972C16.5039 41.8304 17.8907 42.641 20.1172 43.2757C21.2305 43.598 23.5547 43.6566 24.8145 43.3929C27.9786 42.7386 30.7715 40.5706 32.1778 37.6703C34.0332 33.8714 33.252 29.0179 30.3125 26.0492C28.9356 24.6527 26.8848 23.637 24.9707 23.4027C24.668 23.3636 24.4141 23.3245 24.4141 23.305C24.4141 23.2952 24.4727 23.1585 24.5508 23.0023C24.7168 22.6702 24.9707 21.0394 25.1075 19.4964C25.1563 18.9007 25.1953 16.1468 25.1953 13.3734C25.2051 7.8558 25.2051 7.92416 24.6289 7.29916C24.0918 6.72298 23.7793 6.65462 21.7286 6.66439C20.7129 6.67416 19.6973 6.71322 19.4922 6.76205ZM23.3985 8.35384C23.5743 8.49056 23.584 8.69564 23.6426 13.5101C23.7012 18.305 23.8282 21.3617 24.0235 22.6214C24.0821 22.9437 24.1211 23.2269 24.1211 23.2562C24.1211 23.2855 23.9258 23.3148 23.6817 23.3245C23.4375 23.3343 23.1836 23.3441 23.125 23.3441C23.0567 23.3441 22.5879 23.2952 22.0899 23.2367C21.2305 23.1292 19.9024 23.2171 19.3946 23.4124C19.1895 23.4906 19.17 23.305 19.2481 21.9867C19.2871 21.3128 19.3653 18.0218 19.4239 14.6624C19.5313 8.62728 19.5313 8.55892 19.7364 8.38314C19.9121 8.23666 20.1661 8.21712 21.5723 8.21712C22.8321 8.21712 23.252 8.24642 23.3985 8.35384ZM18.1543 17.641C18.1641 18.8812 18.2422 20.18 18.3985 21.5472C18.5254 22.6702 18.6231 23.6175 18.6036 23.637C18.584 23.6566 18.2422 23.8128 17.8516 23.9788C16.5332 24.555 15.293 25.5413 14.375 26.762L13.7989 27.5238L13.8086 24.4378C13.8184 22.7386 13.7793 19.721 13.7305 17.7386L13.6524 14.1253L13.877 13.9202C14.0723 13.7445 14.2871 13.6956 15.2344 13.6468C15.8496 13.6077 16.7286 13.5003 17.1875 13.4027L18.0176 13.2367L18.0762 14.516C18.1153 15.2093 18.1446 16.6156 18.1543 17.641ZM12.2071 22.6898C12.2071 25.0628 12.2559 26.6839 12.3633 27.8753C12.4512 28.8324 12.4903 29.6429 12.461 29.682C12.3145 29.8285 8.61332 29.6234 8.33988 29.4574C8.1348 29.3304 8.1348 29.2913 8.12504 24.6234C8.11527 20.2191 8.12504 19.9163 8.29105 19.7503C8.418 19.6234 8.70121 19.555 9.43363 19.5062C9.96097 19.4574 10.7129 19.3988 11.084 19.3597C11.4649 19.3206 11.8653 19.2718 11.9922 19.2718L12.2071 19.2523V22.6898ZM22.002 24.3499C21.9532 25.0238 21.9629 25.0726 22.1973 25.2972C22.4121 25.4925 22.5196 25.5218 22.9004 25.4827C23.6621 25.3949 23.7305 25.2093 23.4766 24.096L23.4082 23.8128L23.9942 23.8812C24.6778 23.9593 26.0157 24.389 26.8262 24.7992C27.959 25.3753 29.0625 26.3128 29.8828 27.4066C30.8985 28.7445 31.5528 30.2874 31.7774 31.8499L31.836 32.2894L31.2891 32.2992C30.3223 32.3285 30.0098 32.68 30.2832 33.4515C30.4102 33.8128 30.459 33.8519 30.7813 33.8812C30.9864 33.9007 31.2989 33.8714 31.4942 33.8128L31.8457 33.7152L31.7774 34.1742C31.25 37.8558 28.6817 40.7367 25.0782 41.7035C24.6875 41.8109 24.1895 41.9085 23.9746 41.9281L23.584 41.9574L23.4668 41.3812C23.3496 40.7757 23.2325 40.639 22.7246 40.5316C22.3243 40.4339 22.0899 40.7269 22.0118 41.4398L21.9434 42.0257L21.4942 41.9574C18.3301 41.5081 15.3907 39.0863 14.336 36.0492C14.1504 35.5218 13.8672 34.3011 13.8672 34.0472C13.8672 33.9691 14.1016 33.8812 14.5215 33.8031C15.2637 33.6566 15.625 33.3734 15.625 32.9046C15.625 32.6702 15.5664 32.5921 15.2637 32.4652C15.0586 32.3773 14.6582 32.3089 14.375 32.2992L13.8477 32.2894L13.9161 31.7523C13.9942 31.0785 14.4532 29.7015 14.8536 28.9007C15.8106 27.0062 17.1973 25.5999 18.9942 24.7015C19.502 24.4476 21.0352 23.9007 21.6602 23.7542C21.8555 23.7054 22.0313 23.6663 22.0411 23.6566C22.0508 23.6468 22.0313 23.9691 22.002 24.3499Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M22.4219 27.2504C22.2656 27.4945 22.0703 29.4379 22.0117 31.3031L21.9434 33.4418L23.1543 34.6039C24.4141 35.8051 25.6641 36.7328 26.0352 36.7328C26.4941 36.7328 26.416 35.9613 25.8594 35.1312C25.6934 34.8676 25.0977 34.2133 24.5508 33.6664L23.5547 32.6801L23.4961 31.6547C23.4668 31.0883 23.418 30.1508 23.3789 29.5648C23.3301 28.598 23.1348 27.7777 22.8711 27.3969C22.7734 27.2504 22.4805 27.1527 22.4219 27.2504Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default webperformance;
