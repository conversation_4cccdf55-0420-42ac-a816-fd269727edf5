import React from 'react';

const ecom = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M24.9512 6.97265C24.5215 7.17773 23.8672 7.89062 23.6133 8.44726C23.3887 8.93554 23.3887 8.96484 23.3594 13.6328L23.3301 18.3301L20.6739 18.3984C18.2325 18.457 13.6426 18.6621 11.2696 18.8086L10.293 18.8769L9.58011 16.8848C9.18948 15.791 8.80862 14.8437 8.74026 14.7851C8.54495 14.6387 3.08597 14.5117 2.82229 14.6484C2.54886 14.8047 2.56839 15.3418 2.87112 15.5957C2.99807 15.7129 3.17386 16.0254 3.27151 16.2988C3.40823 16.709 3.46682 16.7969 3.61331 16.748C3.97464 16.6309 5.74222 16.5332 6.65042 16.582L7.58792 16.6211L10.4004 24.4043C11.9434 28.6816 13.3789 32.6172 13.584 33.1445C14.2969 34.9414 15.0782 35.9375 16.2598 36.5137C16.8457 36.8066 17.9395 37.0996 18.4278 37.1094C18.6328 37.1094 18.6231 37.1387 18.2617 37.6172C17.7149 38.3496 17.5293 38.9355 17.5293 39.9902C17.5293 40.7519 17.5684 40.9375 17.8028 41.4258C18.3692 42.5684 19.3457 43.125 20.8008 43.1055C22.1192 43.0957 23.3008 42.5293 24.043 41.543C24.5215 40.9277 24.7266 40.293 24.7266 39.4726C24.7168 38.7012 24.5215 38.0859 24.1114 37.5684L23.8184 37.207H27.4805C29.4922 37.207 31.2207 37.1777 31.3184 37.1387C31.4258 37.0996 31.377 37.1973 31.1719 37.4121C30.5274 38.0859 30.0879 39.4043 30.2246 40.3223C30.459 42.0605 31.416 42.9394 33.1446 43.0469C34.9805 43.1543 36.4942 42.334 37.1875 40.8398C37.4024 40.3809 37.4512 40.1465 37.4512 39.5019C37.4512 38.5644 37.2071 37.959 36.6114 37.3437L36.2207 36.9434L36.7871 36.7871C37.5586 36.5723 38.1934 36.1328 38.6035 35.5176C39.4043 34.3555 41.2403 26.7871 41.9824 21.6016C42.2364 19.8633 42.2364 19.5898 41.9922 19.1894C41.6407 18.623 41.4551 18.584 38.1348 18.4668C36.4746 18.4082 34.4043 18.3594 33.5352 18.3594C32.666 18.3594 31.6211 18.3301 31.2012 18.291L30.4395 18.2324L30.5078 13.0469C30.5762 7.96874 30.5762 7.85155 30.3809 7.54882C30.0391 7.00195 29.8438 6.95312 27.4414 6.89452C25.6543 6.8457 25.1856 6.85546 24.9512 6.97265ZM29.375 8.12499C29.4434 8.21288 29.4434 9.72655 29.3848 12.7441C29.2578 19.7461 29.2578 21.875 29.3946 22.0117C29.4629 22.0801 30.1758 22.1387 31.4551 22.168L33.3985 22.2168L30.3418 25.2734L27.295 28.3203L25.7813 26.9433C24.0235 25.3418 22.1094 23.4766 21.2793 22.5391L20.6836 21.8848L22.7344 21.9434L24.7852 22.0019L24.9317 21.7676C25.0489 21.5723 25.0879 20.498 25.1758 14.8633C25.2344 11.1914 25.3125 8.15429 25.3418 8.10546C25.4395 7.94921 29.2481 7.97851 29.375 8.12499ZM23.3399 20.1269V20.8301L21.2891 20.7812C19.8242 20.7422 19.1992 20.7617 19.0821 20.8398C18.9746 20.918 18.916 21.1719 18.8867 21.6894L18.8379 22.4316L22.2852 25.9375C24.1797 27.8613 25.8301 29.5215 25.9571 29.6289C26.3379 29.9316 26.9727 29.9512 27.461 29.6582C27.6953 29.5215 29.5606 27.7246 31.6211 25.6641C35.586 21.6797 35.6446 21.6016 35.1367 21.25C34.9512 21.123 34.5703 21.0937 32.9492 21.0937C31.8653 21.0937 30.8399 21.0644 30.6739 21.0351L30.3711 20.9668V20.1855V19.4043L33.7696 19.4726C38.0274 19.5605 40.918 19.6875 41.0157 19.7949C41.1328 19.9121 40.7324 22.5586 40.127 25.6836C39.5117 28.8184 38.1739 33.9648 37.7637 34.7168C37.4805 35.2344 36.8946 35.625 36.2305 35.7324C35.4883 35.8594 32.1387 36.0254 28.711 36.0937C24.5117 36.1816 18.2715 35.918 17.5489 35.6055C16.7188 35.2637 16.416 34.668 14.0137 28.6621C12.9102 25.9277 10.7422 20.1953 10.7422 20.0391C10.7422 19.9316 17.1485 19.5996 21.0938 19.5019C21.6602 19.4824 22.3926 19.4629 22.7344 19.4531L23.3399 19.4336V20.1269ZM22.041 37.4121C22.8809 37.666 23.6328 38.6523 23.6328 39.4922C23.6328 40.8008 22.2266 41.9922 20.7032 41.9922C20 41.9922 19.6778 41.875 19.2578 41.4746C18.8184 41.0547 18.6524 40.625 18.6524 39.9023C18.6524 39.1504 18.9551 38.4668 19.5215 37.959C20.2735 37.2754 21.0352 37.1191 22.041 37.4121ZM34.7559 37.4512C36.0157 37.8906 36.6602 39.2578 36.1426 40.3809C35.9082 40.918 35.1563 41.5527 34.541 41.7578C33.9551 41.9531 33.0176 41.9824 32.5098 41.8164C31.0254 41.3184 30.9473 38.9258 32.3828 37.832C33.0567 37.3144 33.9649 37.168 34.7559 37.4512Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M20.2347 38.916C19.8733 39.2187 19.7268 39.6582 19.8343 40.1172C19.9319 40.5469 20.1663 40.8008 20.6058 40.9277C21.3968 41.1621 22.0706 40.625 22.0706 39.7754C22.0706 39.1601 21.68 38.7597 21.0354 38.7012C20.6155 38.6523 20.5081 38.6816 20.2347 38.916Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M33.1445 38.5644C32.8516 38.75 32.6172 39.1894 32.6172 39.5703C32.6172 39.6777 32.6953 39.9219 32.7832 40.1074C33.1543 40.8984 34.4141 40.9179 34.8145 40.1367C35.3809 39.0332 34.1992 37.9199 33.1445 38.5644Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M39.6095 13.7304C39.5216 13.8085 39.4532 13.9062 39.4532 13.9453C39.4532 13.9843 39.3458 14.5898 39.2188 15.3027C38.9356 16.914 39.0431 17.2363 39.795 17.0507C40.0684 16.9824 40.0782 16.9531 40.3321 15.5761C40.5567 14.3359 40.5763 13.8574 40.4102 13.6913C40.2442 13.5253 39.7852 13.5449 39.6095 13.7304Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M43.75 14.4824C43.3691 14.6679 41.5137 17.0996 41.5039 17.4023C41.5039 17.8418 41.9922 18.125 42.3438 17.8906C42.6074 17.7246 44.5312 15.166 44.5312 14.9804C44.5312 14.5605 44.1211 14.2968 43.75 14.4824Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M45.0197 18.9844C42.842 19.4043 42.881 19.3945 42.8127 19.6875C42.7052 20.0977 42.9103 20.4102 43.2814 20.4102C43.8088 20.4102 46.9045 19.8145 47.1486 19.668C47.5685 19.4043 47.3146 18.6328 46.8166 18.6621C46.7189 18.6719 45.9084 18.8184 45.0197 18.9844Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ecom;
