import React from 'react';

const DeploymentMvpDevIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_1961_348586)">
                <path
                    d="M24.121 1.31287C23.164 1.57654 22.5781 2.16248 21.2402 4.16443C18.9746 7.54333 17.7148 11.6937 17.7832 15.5804L17.8027 16.6644L17.2753 16.9769C16.1914 17.6117 15.0488 18.764 14.375 19.8773C13.6718 21.0297 13.0859 22.7972 13.0859 23.7445C13.0859 24.4183 13.6035 24.9164 14.2871 24.9164C14.5214 24.9164 14.7949 24.7894 15.1953 24.516C17.1484 23.1586 17.9785 22.7191 18.7695 22.6117L19.1894 22.5531L19.4042 23.0707C19.5312 23.3539 19.6093 23.598 19.5996 23.6176C19.58 23.6273 17.246 24.3597 14.4042 25.2289L9.248 26.8207L8.10542 28.1879C7.48042 28.9398 6.51362 30.0336 5.96675 30.6293C4.89253 31.7914 4.71675 32.1234 4.82417 32.8461C4.94136 33.6078 5.25386 33.8715 7.11909 34.7308L8.77925 35.5121L8.82808 35.9027C8.85737 36.1176 8.9062 37.5433 8.9355 39.0765C9.00386 42.1625 9.04292 42.3969 9.66792 42.9242C10.5761 43.6859 18.9355 47.5726 23.7402 49.4769L25.166 50.0336L26.4746 49.516C29.7753 48.2269 33.1933 46.684 37.4121 44.5844C40.6445 42.973 40.8789 42.807 41.1035 41.9672C41.1621 41.7523 41.2402 40.1898 41.2793 38.5101L41.3574 35.4535L42.9199 34.7211C44.6972 33.9008 45.0585 33.598 45.1757 32.8558C45.2832 32.1136 45.1074 31.7719 44.0234 30.6195C43.0859 29.6136 41.3476 27.5824 40.996 27.0746C40.8203 26.8207 40.4785 26.7133 33.8281 24.7211L30.1074 23.6078L30.3222 23.1195C30.5078 22.6996 30.5761 22.6312 30.7714 22.6605C31.3378 22.7386 32.1484 23.139 33.2226 23.8812C34.7363 24.9066 34.7558 24.9164 35.1562 24.9164C35.6054 24.9164 36.0644 24.6234 36.2304 24.2328C36.5039 23.5785 35.957 21.5082 35.1464 20.0629C34.6386 19.184 33.3789 17.807 32.539 17.2504L31.9628 16.8597L31.9042 14.809C31.8652 13.1097 31.8066 12.5531 31.5917 11.5375C31.1132 9.24255 30.205 6.9281 28.9941 4.92615C28.4179 3.96912 27.207 2.32849 26.7968 1.94763C26.1621 1.36169 24.9707 1.07849 24.121 1.31287ZM25.3906 2.41638C25.5273 2.44568 25.7519 2.56287 25.8984 2.67029C26.2011 2.87537 27.2949 4.32068 27.9101 5.30701L28.3203 5.97107H24.9023C23.0273 5.97107 21.4843 5.94177 21.4843 5.91248C21.4843 5.80505 22.705 3.99841 23.1835 3.38318C23.6816 2.75818 24.0527 2.46521 24.541 2.35779C24.8535 2.27966 24.9511 2.28943 25.3906 2.41638ZM29.1992 7.69958C30.2148 9.93591 30.7421 12.2113 30.83 14.7113C30.9277 17.7582 30.1953 21.0883 28.8476 23.6566C28.2714 24.7504 28.3789 24.7211 24.873 24.7211C22.539 24.7211 21.8945 24.6918 21.621 24.5746C21.3183 24.4476 21.2109 24.3011 20.7519 23.3344C18.3984 18.4418 18.2714 13.139 20.3808 8.09021L20.8203 7.04529H24.8632H28.9062L29.1992 7.69958ZM18.3691 19.8871C18.5253 20.5316 18.6718 21.1566 18.7109 21.264C18.7597 21.4496 18.7109 21.4886 18.2421 21.5961C17.2656 21.8304 15.9179 22.5629 14.6191 23.5785C14.414 23.7347 14.2285 23.8422 14.1894 23.8129C14.1015 23.7152 14.4628 22.2601 14.7656 21.5277C15.2636 20.3168 16.4648 18.8422 17.5292 18.1293L17.9199 17.8656L17.998 18.2953C18.0468 18.5199 18.2128 19.2426 18.3691 19.8871ZM33.0078 19.1449C33.6621 19.7992 33.9453 20.1801 34.2968 20.8636C34.8632 21.9672 35.371 23.6566 35.1757 23.7836C35.1367 23.8031 34.8632 23.6469 34.5703 23.432C33.5937 22.6801 32.4023 21.9867 31.6992 21.7719C31.3183 21.6547 30.996 21.5472 30.9765 21.5375C30.9667 21.5277 31.0644 21.1371 31.2011 20.6683C31.3378 20.1996 31.5136 19.4281 31.6015 18.9496C31.7578 18.1195 31.7675 18.0902 31.9726 18.1976C32.08 18.2562 32.5488 18.6859 33.0078 19.1449ZM26.914 26.2445C28.0566 28.1976 27.9492 30.5804 26.6406 32.0062C26.2207 32.4652 25.4296 33.0316 25.039 33.1586C24.8535 33.2172 24.8242 33.1781 24.7851 32.7386C24.7167 32.0746 24.3554 31.3812 23.5253 30.3363C22.6464 29.223 22.3535 28.6273 22.3437 27.8656C22.3339 27.2015 22.4804 26.6742 22.8222 26.1566L23.0761 25.7953H24.8632H26.6503L26.914 26.2445ZM41.7382 29.7504C42.0703 30.1215 42.6757 30.8051 43.1054 31.264C44.0625 32.2992 44.1699 32.4652 44.0918 32.7386C44.0136 32.9926 41.4941 34.2328 39.7949 34.8578C35.7324 36.3519 34.3261 36.8988 32.9687 37.5043C32.1093 37.8851 31.289 38.1976 31.1425 38.1976C30.957 38.1976 30.7128 38.0707 30.4492 37.8363C29.7949 37.2797 27.1777 34.682 26.7578 34.184L26.3769 33.7347L26.9824 33.2465C27.6367 32.7191 27.1191 32.9242 38.0371 28.8226L40.2734 27.973L40.7128 28.5199C40.957 28.8226 41.416 29.3695 41.7382 29.7504ZM14.0722 29.6429C16.3476 30.5219 19.4433 31.7133 20.9472 32.2894C22.4511 32.8656 23.7011 33.3539 23.7109 33.3734C23.9062 33.5297 21.2695 36.391 19.7949 37.6215C18.9941 38.2953 18.7792 38.2855 16.9238 37.4554C16.1328 37.1039 14.3554 36.4008 12.9882 35.8929C11.621 35.3851 10.1953 34.8578 9.8144 34.7113C9.44331 34.5746 8.41792 34.1156 7.55855 33.7054C5.87886 32.9047 5.69331 32.7386 5.98628 32.309C6.0644 32.1918 6.53315 31.6547 7.0312 31.1176C7.52925 30.5902 8.33003 29.6722 8.80855 29.0961C9.29683 28.5199 9.74605 28.0414 9.8144 28.0414C9.873 28.0414 11.7968 28.764 14.0722 29.6429ZM24.5507 41.6351L24.6191 48.6371L22.9785 47.973C19.3847 46.4984 16.6113 45.2386 12.7539 43.3148C10.9765 42.4261 10.3808 42.0844 10.2539 41.8793C10.1074 41.6449 10.0683 41.2054 10.0097 38.9398C9.97065 37.4847 9.95112 36.2152 9.97065 36.1273C9.99995 35.9906 10.3222 36.0785 12.0605 36.723C14.6484 37.6801 15.1757 37.8949 16.7968 38.6176C17.9687 39.1449 18.1835 39.2133 18.75 39.2133C19.2089 39.2133 19.5019 39.1547 19.7949 38.9984C20.2636 38.7543 22.0117 37.1429 23.4375 35.6293C23.9941 35.0531 24.4531 34.5941 24.4628 34.6039C24.4726 34.6234 24.5117 37.7875 24.5507 41.6351ZM27.2558 36.3617C28.0468 37.2113 29.9316 38.9008 30.2832 39.0765C30.9375 39.4183 31.6015 39.35 32.7539 38.8226C34.5898 37.973 39.8144 35.9515 40.1562 35.9515C40.3027 35.9515 40.1464 41.3812 39.9902 41.7719C39.8828 42.0258 39.6191 42.2015 38.2714 42.9047C35 44.5941 31.1425 46.391 27.6855 47.8265L25.7324 48.6371L25.6738 44.516C25.6445 42.2504 25.6054 39.0863 25.5957 37.4847L25.5859 34.5844L26.25 35.2875C26.621 35.6781 27.0703 36.1566 27.2558 36.3617Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M24.0133 9.20349C23.4273 9.36951 22.734 9.96521 22.4117 10.6097C22.148 11.1273 22.1188 11.264 22.1285 12.0648C22.1383 13.1879 22.3824 13.8617 23.0563 14.6039C23.7008 15.307 24.2672 15.5707 25.1461 15.5707C26.3766 15.5707 27.3824 14.9261 27.9195 13.7933C28.1344 13.3246 28.1734 13.1195 28.1734 12.3187C28.1734 11.264 28.027 10.8734 27.3336 10.1215C26.6109 9.33044 25.068 8.90076 24.0133 9.20349ZM25.7613 10.5804C25.9469 10.7367 25.898 11.1273 25.693 11.2445C25.5855 11.2933 25.3316 11.3422 25.1168 11.3422C24.5309 11.3422 24.277 11.5961 24.2574 12.2113C24.2379 12.807 24.15 12.934 23.7691 12.8851C23.1734 12.8168 23.232 11.6742 23.857 10.9906C24.3648 10.434 25.3316 10.2289 25.7613 10.5804Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M11.9822 40.8148C11.7674 41.0492 11.7674 41.3519 11.992 41.5375C12.2459 41.7719 18.5838 44.4476 18.867 44.4476C19.199 44.4476 19.3552 44.2426 19.3162 43.8422L19.2869 43.5199L15.9177 42.0844C14.0623 41.2933 12.4607 40.6488 12.3435 40.639C12.2361 40.639 12.0701 40.7172 11.9822 40.8148Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M20.3131 44.4378C20.1275 44.7894 20.2349 45.0628 20.6256 45.2777C20.9674 45.4534 21.0162 45.4632 21.2603 45.3265C21.5728 45.1507 21.6607 44.7894 21.4556 44.5062C21.3775 44.3987 21.1529 44.3109 20.8795 44.2816C20.4888 44.2327 20.4107 44.262 20.3131 44.4378Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M36.4957 41.9089C36.3395 41.9577 36.1539 42.0456 36.0758 42.114C35.8902 42.2605 35.9 42.7292 36.0855 42.8855C36.2906 43.0515 36.8863 43.0222 37.1695 42.8366C37.482 42.6413 37.482 42.1726 37.1891 41.9675C36.9449 41.7917 36.8961 41.7917 36.4957 41.9089Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M33.9829 42.7782C32.7036 43.2274 30.3599 44.2723 29.6665 44.6825C29.1294 45.0047 28.9536 45.3758 29.1978 45.6688C29.4224 45.9422 29.7056 45.8934 30.7701 45.3661C32.1079 44.7118 33.3677 44.1746 34.5201 43.7645C35.4868 43.4325 35.7408 43.2567 35.7408 42.9344C35.7408 42.6805 35.4576 42.3973 35.1939 42.4071C35.0962 42.4071 34.5493 42.5731 33.9829 42.7782Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M30.9175 5.14118C30.6928 5.36579 30.7221 5.71735 30.9956 6.14704C31.7182 7.29939 32.2456 8.69587 32.4702 10.1021C32.5483 10.5806 32.6655 11.0299 32.7241 11.108C32.8999 11.3228 33.3686 11.274 33.5346 11.0201C33.9253 10.4244 33.0073 7.19196 32.0014 5.67829C31.562 4.9947 31.23 4.82868 30.9175 5.14118Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M32.8725 12.1136C32.7261 12.2699 32.7065 12.4066 32.7651 12.807C32.8433 13.3539 33.0093 13.5883 33.3413 13.5883C33.7417 13.5883 33.9272 13.266 33.8589 12.6996C33.8198 12.4261 33.7417 12.1429 33.6831 12.0648C33.5171 11.8695 33.0679 11.8988 32.8725 12.1136Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M42.8613 35.9316L42.6465 36.1172L42.8027 37.875C42.8906 38.8418 42.9883 39.7109 43.0273 39.7988C43.1641 40.1601 43.9062 40.1015 44.0039 39.7207C44.0332 39.5937 43.9844 38.7343 43.8965 37.7968C43.7598 36.3125 43.7207 36.0781 43.5352 35.9218C43.2812 35.707 43.1445 35.707 42.8613 35.9316Z"
                    fill="var(--color-black-300)"
                />
            </g>
            <defs>
                <clipPath id="clip0_1961_348586">
                    <rect height="50" width="50" fill="white" transform="translate(0 0.599976)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default DeploymentMvpDevIcon;
