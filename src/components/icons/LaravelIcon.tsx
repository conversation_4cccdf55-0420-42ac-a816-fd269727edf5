import React from 'react';

const LaravelIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131086)"
                transform="translate(-0.00500488 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131086"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131086" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131086"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default LaravelIcon;
