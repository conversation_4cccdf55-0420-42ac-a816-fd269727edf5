import React from 'react';

const AutomationTestingIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M26.4244 6.78137C23.4166 7.13137 20.7588 8.01731 18.7573 9.35168C17.6416 10.0954 16.176 11.5939 16.2635 11.922C16.3401 12.2064 16.4057 12.1954 16.8869 11.8673C17.1057 11.7142 17.3135 11.5939 17.3463 11.5939C17.3791 11.5939 17.5651 11.4736 17.751 11.3314C17.9479 11.1892 18.5932 10.8282 19.1948 10.522C22.3666 8.947 24.7619 8.36731 28.1088 8.37825C30.1213 8.37825 31.1385 8.49856 32.7573 8.91418C34.0151 9.24231 36.9135 10.3579 37.8432 10.8611C38.576 11.2657 39.4838 11.6267 39.5604 11.5501C39.6479 11.4626 39.2651 10.697 38.926 10.2704C37.9416 9.02356 34.8354 7.51418 32.2104 7.00012C30.8323 6.73762 27.8463 6.61731 26.4244 6.78137Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M46.1547 14.6228C44.0765 14.8306 42.5234 15.0822 42.6109 15.2134C42.6875 15.3447 42.6328 15.3337 42.2281 15.1369C41.4953 14.765 40.5547 15.3228 40.2594 16.3072C40.0515 16.9962 39.9422 18.6587 39.9094 21.4697C39.8875 23.8103 39.8875 23.9306 40.1281 24.4228C40.2703 24.7072 40.5547 25.079 40.7625 25.254C41.4515 25.8228 41.7578 25.8556 45.8047 25.7681C49.9172 25.6806 50.0594 25.6478 50.7156 24.904C51.2953 24.2478 51.3172 24.029 51.2734 20.015C51.2187 16.0228 51.1969 15.8259 50.5844 15.1806C50.0594 14.6228 49.7203 14.5462 48.0469 14.5681C47.2156 14.5681 46.3625 14.6009 46.1547 14.6228ZM43.1031 15.5853C43.4859 15.6181 45.0062 15.6947 46.4828 15.7603C47.9594 15.8259 49.25 15.9244 49.3594 15.99C49.7859 16.2087 49.8187 16.6353 49.764 20.1462L49.7094 23.4931L49.4469 23.7775L49.1844 24.0619H45.5969H42.0094L41.7906 23.7775C41.5828 23.515 41.5609 23.34 41.5609 22.104C41.5609 21.3494 41.5062 19.8181 41.4297 18.7025C41.364 17.5869 41.3422 16.5369 41.3859 16.3619C41.4844 15.9572 42.2062 15.2244 42.3156 15.3994C42.3594 15.4759 42.7094 15.5634 43.1031 15.5853Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M27.1239 14.8091C24.2911 15.4107 22.5192 18.5716 23.5145 21.2404C23.777 21.9294 24.3895 22.7607 24.9364 23.1326C25.4395 23.4826 25.4723 23.581 25.1223 23.6685C24.6083 23.7888 23.2192 24.806 22.5192 25.5716C21.1848 27.0154 20.5833 28.7544 20.7255 30.7341C20.813 31.9919 20.9989 33.0419 21.1192 32.9654C21.1739 32.9326 21.2177 32.9873 21.2177 33.0748C21.2177 33.1623 21.1739 33.2716 21.1083 33.3044C21.0536 33.3373 20.9989 33.5998 20.9989 33.8732C20.9989 34.2888 21.0536 34.4201 21.3598 34.7263C21.5567 34.9232 21.9177 35.1419 22.1583 35.2076C23.1208 35.4591 25.527 35.5794 29.8583 35.5904L34.3973 35.6013L34.9223 35.3388C35.2505 35.1748 35.5567 34.9123 35.7427 34.6388C36.027 34.2013 36.038 34.1576 36.0708 32.2326C36.1145 29.8154 35.9614 28.9294 35.2723 27.5076C34.4739 25.8779 33.1723 24.5654 31.6192 23.8216L30.8755 23.4607L31.302 23.1654C33.4786 21.5904 33.4677 17.7623 31.2911 15.8482C30.6348 15.2685 30.1098 15.006 29.213 14.8091C28.3598 14.6341 27.9333 14.6341 27.1239 14.8091ZM29.5848 16.6685C30.5692 17.1169 31.302 18.0685 31.5098 19.1841C31.7177 20.3216 31.1927 21.656 30.1317 22.6294L29.6614 23.0669H28.3161H26.9817L26.227 22.3123C25.0786 21.1748 24.7286 20.1138 25.0895 18.8998C25.702 16.7888 27.6927 15.8044 29.5848 16.6685ZM30.0223 24.8826C31.9911 25.5388 33.4348 27.0154 34.0802 29.0279C34.3208 29.7716 34.3755 30.1982 34.4192 31.6748C34.4848 33.3373 34.4739 33.4466 34.2661 33.6544C34.0583 33.8623 33.8505 33.8732 30.3395 33.9388C28.3052 33.9716 25.6692 34.0591 24.488 34.1357C22.5083 34.2451 22.3114 34.2451 21.9395 34.0701C21.4036 33.8185 21.2833 33.5779 21.4364 33.0529C21.4911 32.8341 21.5458 32.156 21.5458 31.5435C21.5458 30.3185 21.7973 29.1263 22.2348 28.2513C23.7661 25.2435 26.9708 23.8544 30.0223 24.8826Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M8.3225 15.0498C7.01 15.3998 5.99281 16.253 5.3475 17.5217C4.21 19.7748 4.60375 22.2905 6.36468 24.0405C9.87562 27.5514 15.9787 25.1123 15.9678 20.1905C15.9569 17.2045 13.9662 15.0936 11.1553 15.0936C10.8491 15.0936 10.6084 15.1483 10.6084 15.203C10.6084 15.2686 10.4225 15.2139 10.2037 15.0936C9.7225 14.842 9.15375 14.8202 8.3225 15.0498ZM11.91 16.0014C12.7412 16.3186 13.6819 17.2264 14.1412 18.1452C14.4694 18.8233 14.4912 18.9436 14.4912 20.0155C14.4912 21.0108 14.4584 21.2295 14.2287 21.6561C13.8131 22.4327 13.5506 22.7389 12.9053 23.1983C9.62406 25.5608 5.46781 22.553 6.67093 18.692C7.05375 17.4452 7.91781 16.4389 8.96781 16.0014C9.17562 15.9139 9.50375 15.7827 9.67875 15.7061C10.3131 15.4217 10.3678 15.4327 11.91 16.0014Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M9.723 30.3188C9.2855 31.3907 9.82143 33.5453 11.0574 35.6782C12.7199 38.5547 16.1542 41.8141 18.3964 42.6344C19.4683 43.0282 20.5292 43.05 20.2996 42.6672C20.2558 42.6016 19.8621 42.2953 19.4355 42C17.2808 40.5016 16.2636 39.6047 14.9183 38.0407C13.4199 36.2797 12.698 35.1532 11.5933 32.8563C10.3246 30.2094 10.2261 30.0344 10.0292 29.9907C9.94175 29.9688 9.8105 30.1 9.723 30.3188Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M45.9582 30.1105C45.9035 30.1871 45.7722 30.4714 45.6738 30.7558C45.4988 31.2808 45.4004 31.5105 44.6566 33.1402C43.8254 34.9339 42.4254 36.9464 40.8285 38.6199C40.391 39.0792 39.33 40.0089 38.4769 40.698C36.3004 42.448 35.8847 42.8417 36.0925 42.9839C36.3879 43.1808 37.4269 43.1042 38.0613 42.8417C40.6535 41.7699 43.6832 38.5324 45.3457 35.0433C46.0347 33.5996 46.3519 32.5277 46.4504 31.3027C46.505 30.4824 46.4941 30.3839 46.2863 30.1871C46.1222 30.023 46.0347 30.0121 45.9582 30.1105Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M27.3531 40.0967C27.014 40.4576 26.4344 41.1358 26.0734 41.617C25.4609 42.4264 23.4594 45.653 22.4969 47.3701C22.0265 48.2123 21.9719 48.7483 22.3219 49.0655C22.5406 49.2514 22.7265 49.2733 24.5422 49.2623C29.5734 49.2186 33.6859 49.0873 33.8609 48.9451C34.2656 48.6498 34.0797 48.2014 32.4172 45.467C31.5312 44.0123 30.6015 42.5248 30.339 42.1639C29.6062 41.1467 28.2937 39.5608 28.1297 39.5061C28.0422 39.4842 27.7031 39.7467 27.3531 40.0967ZM28.3812 40.4358C28.8297 41.628 30.2078 44.4608 31.039 45.9264C31.5312 46.7905 31.925 47.5233 31.9031 47.5451C31.8812 47.567 30.1859 47.567 28.1406 47.5561L24.4109 47.5233L24.9687 46.5936C25.8 45.1826 26.6969 43.3233 27.4078 41.5405C27.7687 40.6545 28.0859 39.9217 28.1187 39.9217C28.1625 39.9217 28.2719 40.1514 28.3812 40.4358Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default AutomationTestingIcon;
