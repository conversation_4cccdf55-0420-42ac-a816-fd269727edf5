import React from 'react';

const AagileServicesIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_2644_340171)">
                <path
                    d="M28.0859 6.73198C27.0031 8.26323 26.1063 9.57573 26.0953 9.66323C26.0734 9.73979 26.9703 11.0632 28.0641 12.5945C29.5078 14.607 30.1203 15.3835 30.2734 15.3835C30.4813 15.3835 30.4922 15.296 30.525 14.1476L30.5578 12.9007L31.0172 12.9773C31.925 13.1304 33.1391 13.4914 34.1125 13.907C38.7719 15.8429 42.1625 19.9554 43.2563 24.9867C43.6063 26.5945 43.6063 29.4492 43.2453 31.0789C42.5891 34.1304 41.2438 36.5804 39.0344 38.7898C36.8031 41.021 34.4516 42.2898 31.2578 43.0117L30.1641 43.2632L16.0875 43.296L4 43.3398V46.3367V49.3445H16.4156C28.4141 49.3445 31.0063 49.3117 31.9359 49.1804C39.2969 48.1304 45.5641 42.9242 48.2984 35.5632C50.5734 29.4492 49.9063 22.4164 46.5156 16.871C45.6844 15.5039 44.8422 14.421 43.5734 13.1195C40.3797 9.79448 36.3328 7.68354 31.6953 6.91792L30.6016 6.74292L30.5469 5.37573C30.4703 3.36323 30.4813 3.35229 28.0859 6.73198Z"
                    fill="var(--color-primary-900)"
                />
                <path
                    d="M22.0136 7.83599C13.2965 9.02818 9.08865 14.6891 7.17459 23.3079C6.13553 28.0329 6.70428 33.0532 8.79334 37.3516C9.47146 38.7297 9.8824 38.9063 10.6699 39.7594L11.1949 40.3391L16.3355 40.3063L21.4761 40.2735L20.5136 39.8141C13.9402 36.6532 11.484 31.4672 13.1793 24.4344C14.6012 18.5063 16.5012 14.9235 22.4949 13.9391C23.1183 13.8407 23.6543 13.7422 23.6761 13.7204C23.709 13.6985 23.6324 13.5454 23.5012 13.3813C23.3808 13.2172 22.9543 12.5938 22.5496 12.0032C21.9043 11.0625 21.8168 10.8657 21.8605 10.5594C21.9152 10.2751 22.8449 8.78755 23.6105 7.80317C23.7418 7.63911 23.4683 7.63911 22.0136 7.83599Z"
                    fill="var(--color-primary-900)"
                />
                <path
                    d="M26.9066 19.25C26.3378 19.3813 26.1847 19.6656 26.0972 20.6609C25.955 22.2359 25.4081 22.4984 24.2597 21.5469C23.5706 20.9672 23.2425 20.825 22.8269 20.9344C22.4878 21.0219 21.8206 21.5906 21.3175 22.225C20.705 23.0016 20.7378 23.3078 21.4925 24.2266C22.4222 25.3641 22.28 25.9547 21.0331 26.075C19.3487 26.25 19.1956 26.425 19.2066 28.0328C19.2175 29.6516 19.3378 29.8156 20.7378 29.9359C22.2034 30.0563 22.4878 30.6031 21.6456 31.6641C20.9894 32.4844 20.8581 32.7688 20.9456 33.1297C21.0222 33.5016 21.9519 34.5625 22.4659 34.8797C23.1222 35.2844 23.4612 35.1969 24.5222 34.3109C25.08 33.8516 25.4847 33.7969 25.8019 34.1141C26.0206 34.3328 26.0862 34.5734 26.1847 35.6562C26.2284 36.0938 26.3159 36.3234 26.5237 36.5312C26.7862 36.7938 26.8628 36.8047 28.055 36.8047C29.2581 36.8047 29.3237 36.7938 29.5862 36.5203C29.8378 36.2797 29.8925 36.1047 29.9581 35.2734C30.0347 34.4641 30.0894 34.2672 30.2972 34.1031C30.7128 33.775 31.0519 33.8625 31.8612 34.5078C32.9331 35.3719 33.2503 35.3172 34.4316 34.0922C35.0441 33.4469 35.1097 33.3375 35.1097 32.9109C35.1097 32.5391 35.0331 32.3531 34.705 31.9594C33.8191 30.8875 33.7534 30.7016 34.1581 30.2422C34.355 30.0125 34.53 29.9578 35.1097 29.9031C36.6628 29.7609 36.8597 29.5531 36.8597 28.0109C36.8597 26.425 36.6628 26.1844 35.2737 26.0641C34.5737 26.0094 34.355 25.9438 34.1581 25.7469C33.7862 25.375 33.8409 25.025 34.3878 24.3578C35.0003 23.6031 35.1097 23.3953 35.1097 22.9578C35.1097 22.6625 34.9675 22.4656 34.2566 21.7438C33.4691 20.9563 33.3597 20.8906 32.955 20.8906C32.5722 20.8906 32.3972 20.9781 31.7737 21.4922C30.9534 22.1594 30.6472 22.2359 30.2534 21.8422C30.0456 21.6344 29.98 21.4047 29.9144 20.7703C29.8159 19.7203 29.6409 19.4031 29.1159 19.2609C28.6456 19.1187 27.4644 19.1187 26.9066 19.25ZM29.5316 24.4563C29.8597 24.6094 30.2862 24.85 30.4941 25.0031C31.0409 25.4297 31.5878 26.3266 31.7847 27.1578C32.3097 29.2797 30.8769 31.4016 28.6237 31.8172C27.4206 32.0359 26.1081 31.5766 25.2222 30.625C24.4237 29.7828 24.2269 29.2469 24.2269 28C24.2269 26.7531 24.4237 26.2172 25.2222 25.375C26.3597 24.15 28.0331 23.8 29.5316 24.4563Z"
                    fill="url(#paint0_linear_2644_340171)"
                />
                <path
                    d="M51.9297 41.7094C51.8969 41.7859 51.8641 42.3875 51.8531 43.0547L51.8203 44.2578L49.1516 44.2906L46.4719 44.3125L45.3781 45.3844C44.1531 46.5656 43.4203 47.1562 42.0203 48.0641C41.0141 48.7312 39.2094 49.6391 38.1484 50.0328L37.4922 50.2734L44.6562 50.3281C48.5938 50.3609 51.8313 50.3937 51.8531 50.4156C51.8641 50.4266 51.875 51.0281 51.875 51.75C51.875 52.9641 51.8859 53.0625 52.0828 53.0625C52.2359 53.0625 52.8703 52.2531 54.2703 50.2734C55.3641 48.7422 56.25 47.4187 56.25 47.3312C56.25 47.1016 52.3344 41.6437 52.1484 41.6C52.0609 41.5781 51.9625 41.6219 51.9297 41.7094Z"
                    fill="var(--color-primary-900)"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_2644_340171"
                    gradientUnits="userSpaceOnUse"
                    x1="28.0329"
                    x2="28.0329"
                    y1="19.1529"
                    y2="36.8047"
                >
                    <stop stopColor="white" />
                    <stop offset="1" stopColor="var(--color-phonegap)" />
                </linearGradient>
                <clipPath id="clip0_2644_340171">
                    <rect height="56" width="56" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default AagileServicesIcon;
