import React from 'react';

const PrototypeIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.428 5.20459C8.35965 5.26318 7.14871 5.30224 6.54324 5.4292C4.56082 5.84912 3.22293 7.12841 2.50027 9.29638C1.40652 12.5874 0.69363 22.1382 0.957302 29.9312C1.08425 33.4565 1.20144 35.146 1.37722 36.0054C1.43582 36.269 1.52371 37.3726 1.56277 38.4468C1.65066 40.5659 1.73855 41.0347 2.27566 42.0405C2.85183 43.1343 4.01394 43.8569 5.88894 44.3062C9.06277 45.0581 14.9221 45.8003 20.9475 46.23C23.3011 46.396 31.553 46.396 33.6917 46.23C38.1643 45.8784 43.0179 45.1069 44.7464 44.4526C47.9886 43.2417 49.385 40.5659 49.1897 35.9565C49.1507 35.2339 49.0628 33.2124 48.9846 31.4644C48.8284 28.1245 48.6331 22.7241 48.3889 15.3999C48.2229 10.3901 48.1741 10.0288 47.3929 8.41748C46.8264 7.23584 46.055 6.44482 45.0003 5.94677C43.7405 5.3413 43.2522 5.28271 39.0139 5.19482C34.5999 5.09716 22.2952 5.09716 13.428 5.20459ZM43.1253 6.98193C45.6839 7.54834 46.7874 9.4331 46.9436 13.5347C47.0022 14.9409 47.0022 14.98 46.8167 14.8823C46.4749 14.6968 45.1175 14.5698 42.5393 14.4722C37.803 14.2964 25.9866 14.1597 14.4925 14.1499L3.06668 14.1304L3.34011 12.5288C3.73074 10.2339 4.1409 9.02295 4.80496 8.11474C5.08816 7.73388 5.80105 7.2456 6.3284 7.06982C7.10965 6.81591 9.31668 6.78662 26.1721 6.80615C40.5667 6.82568 42.51 6.84521 43.1253 6.98193ZM6.05496 14.814C9.43386 15.0874 15.1663 15.3901 22.0218 15.644C23.4768 15.7026 27.6077 15.7905 31.2014 15.8394C39.5413 15.9663 45.3616 15.8296 46.719 15.4878L46.9729 15.4292V16.7769C46.9729 18.6616 47.1292 22.519 47.4124 27.8022C47.7932 34.98 47.8714 36.9136 47.803 37.8022C47.5491 40.9956 46.4065 42.6362 43.8479 43.4663C42.5003 43.896 38.7405 44.5796 36.0354 44.8921C30.4495 45.5171 23.555 45.5854 16.9925 45.0776C13.5647 44.814 7.56863 44.0718 5.9573 43.7104C3.77957 43.2222 2.66629 42.2651 2.19754 40.4976C1.97293 39.6089 1.73855 36.6694 1.85574 36.103C1.91433 35.8296 1.99246 33.3296 2.05105 30.5366C2.14871 24.9312 2.2073 23.0464 2.35379 20.5757C2.45144 18.8569 2.78347 15.4487 2.8909 14.8726L2.95925 14.5405L3.65261 14.6089C4.03347 14.6479 5.11746 14.7358 6.05496 14.814Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M8.67218 8.23217C7.51984 8.50561 6.69952 9.71654 6.87531 10.8787C6.9632 11.4646 7.37335 11.9333 7.79327 11.9333C7.95929 11.9333 8.10577 11.9919 8.13507 12.0798C8.24249 12.3435 9.00421 12.656 9.57062 12.656C10.2933 12.6658 10.8109 12.3923 11.1722 11.7966C11.4163 11.406 11.4554 11.24 11.4651 10.6345C11.4651 10.029 11.4261 9.85326 11.1819 9.40404C10.8694 8.82787 10.3226 8.36889 9.85382 8.281C9.21906 8.15404 9.01398 8.14428 8.67218 8.23217ZM9.58038 9.88256C9.98077 10.1072 10.1859 10.9177 9.95148 11.3669C9.78546 11.6697 9.24835 11.8552 8.71124 11.7771L8.24249 11.7185L8.25226 11.1619C8.25226 10.156 8.90656 9.531 9.58038 9.88256Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M19.5898 8.23166C18.5645 8.46603 17.7734 9.46213 17.7734 10.5168C17.7734 11.3078 18.1836 11.9328 18.7109 11.9328C18.8281 11.9328 19.0625 12.0598 19.2285 12.216C19.8242 12.7727 20.8496 12.8215 21.6406 12.3332C22.5391 11.7766 22.6367 10.009 21.8262 8.94455C21.3965 8.38791 20.3613 8.05588 19.5898 8.23166ZM20.5664 9.96018C20.9766 10.2922 21.123 10.927 20.8887 11.3664C20.7227 11.6692 20.1855 11.8547 19.6484 11.7766L19.1895 11.718L19.1992 11.2199C19.209 10.4387 19.2188 10.3996 19.541 10.0871C19.8828 9.74533 20.2441 9.6965 20.5664 9.96018Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M13.7127 8.39806C12.7752 8.83752 12.2478 9.73595 12.326 10.7711C12.365 11.318 12.4041 11.4059 12.7361 11.7086C12.9705 11.9235 13.1658 12.0309 13.283 12.0016C13.3904 11.9723 13.5369 12.0406 13.6443 12.1481C14.1131 12.6852 15.158 12.861 15.8807 12.5289C16.6424 12.1871 16.9451 11.5328 16.8768 10.4195C16.8182 9.55041 16.4959 8.92541 15.8904 8.54455C15.3436 8.19299 14.3084 8.12463 13.7127 8.39806ZM15.0213 9.94103C15.5193 10.2828 15.6756 11.0445 15.3338 11.4742C15.0994 11.7672 14.6893 11.8746 14.1228 11.7965L13.7029 11.7281L13.7127 11.0934C13.7225 10.4781 13.742 10.4293 14.0936 10.1168C14.4939 9.74572 14.699 9.70666 15.0213 9.94103Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.4089 18.2998C30.1647 18.6416 29.4226 19.8135 28.7683 20.917C23.8464 29.1592 17.8405 41.3564 17.987 42.8213C18.0163 43.0947 18.0554 43.1338 18.3093 43.1338C18.5339 43.1338 18.6608 43.0361 18.9538 42.6553C19.9206 41.376 29.1101 22.7236 30.7214 18.7783C31.239 17.499 31.1315 17.3232 30.4089 18.2998Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M15.859 19.2478C14.6579 19.7946 7.48014 25.6735 6.68912 26.7575C6.10318 27.5681 6.00553 28.6423 6.44498 29.5114C7.11881 30.8396 14.2672 38.5446 15.3219 39.0818C16.279 39.5603 17.4411 39.3747 17.8708 38.6716C18.0856 38.32 18.1247 37.8122 17.9684 37.5095C17.9001 37.3825 17.9001 37.3239 17.9684 37.3239C18.1344 37.3239 18.0758 36.7575 17.8219 35.9958C17.5387 35.156 16.904 33.8571 15.195 30.7028L13.9743 28.4177L15.2145 27.1384C16.777 25.5173 17.7731 24.3845 18.2028 23.7302C19.3551 22.0017 19.1501 19.9607 17.7536 19.2478C17.2751 19.0036 16.3962 19.0036 15.859 19.2478ZM17.07 20.7517C17.5583 21.2693 17.402 22.1091 16.6501 23.115C16.4059 23.4372 15.4977 24.4626 14.6286 25.3903C13.7497 26.3278 12.9391 27.2263 12.8122 27.4118C12.5192 27.822 12.402 28.3396 12.4997 28.779C12.6266 29.3552 17.2067 37.0407 17.6071 37.3435C17.8122 37.4997 17.8122 37.5193 17.6754 37.9294C17.5094 38.3982 17.0212 38.7888 16.6012 38.7888C16.0934 38.7888 15.3805 38.32 14.6188 37.4802C12.1286 34.7458 7.74381 29.3747 7.5192 28.7888C7.19693 27.9392 7.55826 27.529 11.279 24.531C13.4567 22.7829 16.5524 20.5271 16.7868 20.5271C16.8258 20.5271 16.9528 20.6247 17.07 20.7517Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M32.9004 19.6964C32.0215 19.9698 31.3477 20.9756 31.3477 21.9815C31.3477 22.8213 31.8262 23.8663 32.7637 25.0674C32.998 25.3799 33.8672 26.337 34.6875 27.1866C35.5078 28.046 36.2109 28.7979 36.25 28.8565C36.3281 28.9835 36.1914 29.2471 34.5996 32.2159C33.9355 33.4561 33.1641 34.9991 32.8809 35.6338C32.373 36.8057 32.2949 37.2452 32.3047 38.71C32.3145 39.794 33.7109 40.2823 35.0195 39.667C35.5371 39.4229 37.373 37.6163 40.4297 34.3448C42.6367 31.9815 43.3984 31.0635 43.8184 30.2237C44.1113 29.628 44.1699 29.4229 44.1602 28.9151C44.1504 28.1827 43.8281 27.46 43.2324 26.8546C42.3438 25.9366 38.877 23.046 36.5234 21.2588C34.4629 19.6963 33.8086 19.4131 32.9004 19.6964ZM35.1172 22.1866C37.6758 24.0225 42.1289 27.6065 42.6074 28.2217C42.9883 28.71 42.9688 29.2178 42.5391 29.9307C42.1094 30.6436 41.543 31.3272 38.1348 35.3213C35.2539 38.6905 34.7266 39.1983 34.0234 39.335C33.6035 39.4131 33.0566 39.2276 32.8125 38.9151C32.5781 38.6124 32.5586 38.085 32.7734 37.9092C32.8516 37.8311 33.4863 36.8741 34.1699 35.7706C36.8555 31.4346 37.8906 29.5596 37.8906 28.9835C37.8906 28.4366 37.4902 27.8604 36.0645 26.3663C34.248 24.462 33.6523 23.7491 33.3008 23.0264C32.8418 22.1182 32.959 21.21 33.5352 21.21C33.6621 21.21 34.3359 21.6202 35.1172 22.1866Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default PrototypeIcon;
