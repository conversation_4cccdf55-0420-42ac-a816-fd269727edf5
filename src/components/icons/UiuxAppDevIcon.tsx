import React from 'react';

const UiuxAppDevIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.3349 3.02722C11.8173 3.08582 9.60052 3.13464 9.27825 3.22253C7.93059 3.58386 6.97356 4.54089 6.53411 5.95691C6.18255 7.08972 6.15325 7.9198 5.75286 28.7108C5.63567 34.5409 5.56731 39.8534 5.59661 40.5272C5.72356 43.203 6.41692 44.4725 8.24309 45.3612C9.91302 46.162 11.3095 46.621 12.9111 46.8846C13.3993 46.9628 15.8212 47.0116 20.8017 47.0409C30.2743 47.0995 33.1845 46.9725 36.2802 46.3378C37.8818 46.0057 38.1357 45.9276 38.4775 45.6346C38.8779 45.3026 38.8583 44.8046 38.4482 44.4921C38.1943 44.3065 38.038 44.287 36.7392 44.287C35.9482 44.287 35.04 44.3261 34.7079 44.3651L34.1122 44.4432L34.3954 43.8768C34.747 43.1639 34.874 42.3632 34.8056 41.3573C34.7568 40.6053 34.5712 29.2675 34.6103 29.2284C34.62 29.2186 35.1669 30.1268 35.831 31.2596C37.1884 33.5643 37.4033 33.7987 38.2236 33.8671C38.8779 33.9159 39.2099 33.7694 40.1279 32.9979C40.9677 32.2948 41.1435 31.9725 41.1435 31.2206C41.1337 30.6835 41.0849 30.5761 39.8642 28.4862L38.6044 26.3182L40.9091 26.2694C42.8036 26.2303 43.2626 26.1913 43.5068 26.0643C44.249 25.6542 44.4345 25.1561 44.4345 23.6132C44.4345 22.1971 44.2685 21.7382 43.624 21.3085C43.2333 21.0448 43.2236 21.0448 40.9579 20.996L38.6825 20.9471L39.8935 19.0038C41.124 17.0507 41.1728 16.9237 41.1142 16.1132C41.0849 15.6151 40.8896 15.3026 40.2743 14.7753C39.3368 13.9647 38.9853 13.7694 38.4677 13.7694C37.9208 13.7694 37.54 13.8964 37.2372 14.1796C37.12 14.2968 36.4755 15.244 35.8212 16.2987L34.62 18.203L34.5615 17.6757C34.5224 17.3827 34.4638 14.9218 34.4345 12.2069C34.3564 6.31824 34.3075 5.61511 33.9658 4.87292C33.6533 4.1991 33.1259 3.59363 32.5302 3.24207L32.081 2.97839L28.5165 2.96863C26.5536 2.95886 21.5243 2.98816 17.3349 3.02722ZM32.0712 4.28699C32.4814 4.55066 32.9599 5.27332 33.0966 5.85925C33.1552 6.10339 33.2138 6.90418 33.2236 7.64637L33.2529 8.98426H20.87H8.47747L8.51653 7.19715C8.54583 5.43933 8.55559 5.40027 8.8095 5.00964C8.96575 4.77527 9.23919 4.53113 9.49309 4.41394C9.90325 4.21863 10.1083 4.20886 20.2158 4.12097C25.8798 4.06238 30.7919 4.03308 31.124 4.05261C31.5341 4.07214 31.8368 4.1405 32.0712 4.28699ZM33.3505 13.496C33.3896 15.3905 33.3993 17.1483 33.3798 17.4022L33.3505 17.8514L32.2372 16.2401C31.622 15.3612 30.9872 14.5409 30.8212 14.4237C30.6357 14.287 30.3329 14.2089 29.9716 14.1796C29.3173 14.1307 29.0048 14.2772 27.9306 15.1561C26.8466 16.0546 26.4853 17.1386 26.954 18.1053C27.0615 18.33 27.5888 19.0624 28.1259 19.746L29.1025 20.996H27.4716C25.5966 20.996 25.2255 21.0741 24.7568 21.5917C24.3075 22.0897 24.1904 22.5878 24.2392 23.8768C24.2685 24.8436 24.2978 24.9999 24.5419 25.41C24.913 26.0448 25.245 26.328 25.8798 26.5819C26.3486 26.7675 26.5927 26.7968 27.8134 26.7772L29.2197 26.7675L29.0732 26.9823C27.8915 28.8085 26.915 30.4491 26.8466 30.7421C26.749 31.1522 26.8173 31.66 27.0126 32.0507C27.2568 32.5194 28.8486 33.8475 29.3466 33.9843C29.9814 34.16 30.6845 34.0721 31.1044 33.7499C31.5634 33.3983 32.4033 32.2753 33.0087 31.1913L33.497 30.3221V31.6503C33.497 32.3827 33.5263 34.2968 33.5654 35.9178L33.624 38.8671H21.0165H8.39934V31.953C8.39934 28.1444 8.42864 21.66 8.4677 17.5487L8.5263 10.0585H20.8993H33.2822L33.3505 13.496ZM39.2001 15.3905C39.8642 15.9471 40.04 16.1718 40.04 16.455C40.04 16.5428 39.3564 17.6952 38.5263 19.0136C37.6962 20.3417 37.0126 21.494 37.0126 21.6014C37.0126 21.6991 37.1005 21.8651 37.2079 21.9725C37.3935 22.1581 37.5302 22.1678 40.1669 22.1678C43.5458 22.1678 43.3115 22.0604 43.3115 23.6327C43.3115 25.205 43.5361 25.0975 40.245 25.0975C38.2724 25.0975 37.4814 25.1366 37.3056 25.2245C36.8173 25.4686 36.9052 25.703 38.5165 28.4667C39.3564 29.8925 40.04 31.162 40.04 31.2792C40.0302 31.5428 39.9228 31.6796 39.3075 32.1874C38.5751 32.7831 38.4091 32.8514 38.1454 32.6757C38.0185 32.5975 37.1591 31.2303 36.2216 29.6288C35.1279 27.7636 34.4443 26.6991 34.3075 26.6307C34.1513 26.5624 34.0146 26.5624 33.8681 26.6307C33.7314 26.6991 32.9599 27.8221 31.9345 29.453C30.0595 32.4315 29.8544 32.7147 29.5908 32.7147C29.4052 32.7147 28.4286 31.9921 28.1064 31.621C28.0087 31.5136 27.9306 31.3182 27.9306 31.1913C27.9306 31.0448 28.6044 29.8827 29.5419 28.3983C30.4306 26.9921 31.1533 25.7714 31.1533 25.6835C31.1533 25.5956 31.0947 25.4393 31.0165 25.3319C30.8896 25.1561 30.704 25.1464 28.2724 25.0975C25.8408 25.0487 25.6552 25.0389 25.5283 24.8632C25.3329 24.5897 25.3329 22.6757 25.5283 22.4022C25.6552 22.2264 25.8408 22.2167 28.3603 22.1678C30.2841 22.1288 31.0751 22.0897 31.163 22.0018C31.4658 21.6991 31.29 21.3475 29.7861 19.2382C28.9658 18.0858 28.2724 17.0604 28.2431 16.9725C28.1454 16.6014 28.3408 16.3085 29.0243 15.7811C29.4052 15.4784 29.7568 15.2343 29.8056 15.2343C30.079 15.2343 30.4208 15.6542 32.0224 17.9589C33.0575 19.4432 33.8681 20.5175 33.9853 20.5565C34.1025 20.5956 34.288 20.5565 34.415 20.4686C34.5419 20.3905 35.3818 19.16 36.2802 17.7245C37.8232 15.2831 38.1552 14.8436 38.4482 14.8436C38.5068 14.8436 38.8486 15.0878 39.2001 15.3905ZM33.6923 41.2401C33.6923 42.6073 33.5556 43.2128 33.1064 43.828C32.7743 44.287 32.2275 44.6874 31.7294 44.8241C30.2548 45.2538 17.0322 45.4393 12.7158 45.0878C9.33684 44.8143 9.14153 44.7557 8.64349 43.9061C8.40911 43.5057 8.39934 43.4276 8.39934 41.7675V40.0389H21.0458H33.6923V41.2401Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M16.3086 6.44531C16.2012 6.55273 16.1133 6.72852 16.1133 6.83594C16.1133 6.94336 16.2012 7.11914 16.3086 7.22656C16.4941 7.41211 16.6309 7.42188 19.1895 7.42188C21.748 7.42188 21.8848 7.41211 22.0703 7.22656C22.1777 7.11914 22.2656 6.94336 22.2656 6.83594C22.2656 6.72852 22.1777 6.55273 22.0703 6.44531C21.8848 6.25977 21.748 6.25 19.1895 6.25C16.6309 6.25 16.4941 6.25977 16.3086 6.44531Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M23.0103 6.40625C22.6587 6.74805 22.9224 7.32422 23.4302 7.32422C23.772 7.32422 23.9283 7.1582 23.9283 6.78711C23.9283 6.62109 23.8794 6.43555 23.8111 6.36719C23.6451 6.20117 23.1861 6.2207 23.0103 6.40625Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.8468 18.9065C11.7003 19.1409 11.7003 19.1897 11.8273 19.4241L11.9738 19.678L18.0675 19.7073C23.6242 19.7269 24.171 19.7171 24.337 19.5706C24.5617 19.3558 24.5617 19.0237 24.337 18.8186C24.171 18.6722 23.5949 18.6526 18.087 18.6526H12.0128L11.8468 18.9065Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.1324 25.0684C10.732 25.2148 10.6344 25.6738 10.9371 25.9766C11.1227 26.1621 11.2594 26.1719 15.4098 26.1719C19.1207 26.1719 19.7066 26.1523 19.902 26.0156C20.1754 25.8301 20.1852 25.459 19.9215 25.1953C19.7359 25.0098 19.5992 25 15.5074 25.0098C13.1832 25.0098 11.2106 25.0391 11.1324 25.0684Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.8468 30.625C11.7003 30.8594 11.7003 30.9082 11.8273 31.1426L11.9738 31.3965H17.1886H22.4035L22.5499 31.1426C22.6769 30.9082 22.6769 30.8594 22.5304 30.625L22.3644 30.3711H17.1886H12.0128L11.8468 30.625Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M19.6003 41.2499C18.5847 42.2557 19.1218 44.4335 20.3815 44.4335C21.1042 44.4335 21.6804 43.662 21.6804 42.6757C21.6804 41.6405 21.1335 40.9178 20.3522 40.9178C20.0007 40.9178 19.8737 40.9764 19.6003 41.2499Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default UiuxAppDevIcon;
