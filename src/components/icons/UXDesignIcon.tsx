import React from 'react';

const UXDesignIcon = ({ fill = 'white' }) => {
    return (
        <svg
            height="21"
            width="20"
            fill="none"
            viewBox="0 0 20 21"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_964_647370)">
                <path
                    d="M12.49 11.1048C12.6217 12.5881 11.4567 13.8331 10 13.8331H6.685C6.10917 13.8331 5.70083 13.2781 5.8725 12.729C6.29083 11.3881 7.48583 9.25814 9.40583 8.88897C10.8683 8.60731 12.3583 9.62147 12.49 11.1048ZM19.1667 7.99981C18.7058 7.99981 18.3333 8.37231 18.3333 8.83314V12.9998C18.3333 14.3781 17.2117 15.4998 15.8333 15.4998H4.16667C2.78833 15.4998 1.66667 14.3781 1.66667 12.9998V6.33314C1.66667 4.95481 2.78833 3.83314 4.16667 3.83314H10.8333C11.2942 3.83314 11.6667 3.46064 11.6667 2.99981C11.6667 2.53898 11.2942 2.16648 10.8333 2.16648H4.16667C1.86917 2.16648 0 4.03564 0 6.33314V12.9998C0 15.2973 1.86917 17.1665 4.16667 17.1665H9.16667V18.8331H6.66667C6.20667 18.8331 5.83333 19.2056 5.83333 19.6665C5.83333 20.1273 6.20667 20.4998 6.66667 20.4998H13.3333C13.7942 20.4998 14.1667 20.1273 14.1667 19.6665C14.1667 19.2056 13.7942 18.8331 13.3333 18.8331H10.8333V17.1665H15.8333C18.1308 17.1665 20 15.2973 20 12.9998V8.83314C20 8.37231 19.6275 7.99981 19.1667 7.99981ZM11.3217 7.73731C11.3275 7.73981 11.3342 7.74231 11.34 7.74564C11.66 7.88564 12.0317 7.79481 12.2642 7.53397L16.965 2.42564C17.255 2.10148 17.7533 2.07398 18.0767 2.36397C18.0875 2.37397 18.0975 2.38314 18.1083 2.39398C18.4158 2.70064 18.4158 3.19814 18.1092 3.50564C18.1092 3.50564 18.1092 3.50564 18.1083 3.50647L13.2383 8.24481C12.9625 8.52064 12.9233 8.94814 13.1358 9.27564C13.1392 9.28064 13.1425 9.28564 13.145 9.28981C13.4275 9.73064 14.0433 9.79481 14.4133 9.42481L19.2833 4.68731C20.24 3.72897 20.24 2.17648 19.2833 1.21814C18.3258 0.259809 16.7733 0.258975 15.815 1.21648C15.7825 1.24898 11.0142 6.42981 11.0142 6.42981C10.635 6.85564 10.7983 7.51147 11.3217 7.73731Z"
                    fill={fill}
                />
            </g>
            <defs>
                <clipPath id="clip0_964_647370">
                    <rect height="20" width="20" fill={fill} transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default UXDesignIcon;
