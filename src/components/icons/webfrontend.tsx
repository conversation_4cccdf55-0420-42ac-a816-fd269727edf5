import React from 'react';

const webfrontend = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M16.1132 5.25295C14.3652 5.31154 11.1816 5.3799 9.02339 5.3799C5.36128 5.38967 5.09761 5.39943 4.95112 5.56545C4.51167 6.05373 4.97065 14.4717 5.4687 14.9893C5.63472 15.1651 5.64448 15.1553 5.70308 14.6475C5.83003 13.5733 5.95698 10.7315 5.95698 8.71975V6.63967L15.4589 6.70803C25.1367 6.77639 27.9003 6.72756 31.7382 6.41506C34.375 6.19045 34.7851 6.15139 34.6972 6.07326C34.4726 5.86818 31.0156 5.3799 28.496 5.19436C26.6406 5.0674 20.1562 5.0967 16.1132 5.25295Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.707 9.20797C18.3398 9.2568 12.5684 9.32516 11.8652 9.36422C10.7227 9.44235 10.5371 9.48141 10.0684 9.73532C9.42383 10.0966 8.95508 10.6338 8.68164 11.3369C8.38867 12.0888 8.33008 13.8369 8.53516 15.8779C8.62305 16.7373 8.69141 17.5381 8.69141 17.665C8.69141 17.8896 8.68164 17.8896 7.45117 17.8896C6.75781 17.8896 6.00586 17.9384 5.76172 17.997C5.14648 18.1338 4.62891 18.5048 4.375 18.9931L4.15039 19.4033V29.999V40.5947L4.41406 41.0341C4.57031 41.3076 4.83398 41.5713 5.09766 41.7177C5.50781 41.9521 5.56641 41.9619 7.32422 41.9521C9.26758 41.9326 13.1055 41.7568 13.4277 41.6689C13.6133 41.6103 13.6523 41.6982 13.8574 42.4013C14.707 45.3115 17.2852 46.7275 21.1523 46.3955C23.3398 46.2099 24.5801 45.7412 25.6055 44.7158C26.377 43.9443 26.7969 43.2412 27.207 42.0302C27.6367 40.7705 27.832 39.6181 27.9004 37.9384C27.9297 37.1767 27.9883 36.5029 28.0273 36.4443C28.0859 36.3564 28.2715 36.3564 28.7695 36.4248C29.1406 36.4834 29.8438 36.5517 30.3223 36.581C30.8105 36.6201 32.3438 36.708 33.7402 36.7959C36.4844 36.9716 38.4766 36.9326 39.3359 36.6982C40.5078 36.3759 41.3086 35.7314 41.7676 34.7451L42.041 34.1494V23.1631C42.041 12.8408 42.0312 12.1474 41.8652 11.6689C41.6113 10.956 41.0059 10.1552 40.4102 9.75485C39.6094 9.2275 39.0332 9.10055 37.5586 9.12008C36.8555 9.12985 31.0742 9.16891 24.707 9.20797ZM30.7129 10.5556C39.4238 10.6435 39.1406 10.624 39.834 11.2197C40.5566 11.8349 40.5176 11.083 40.5469 22.9775L40.5762 33.6611L40.3027 34.2275C40.127 34.5791 39.8926 34.8818 39.668 35.0381C38.9648 35.5166 38.6133 35.5556 33.9844 35.6631C31.6016 35.7216 29.2773 35.8095 28.8379 35.8486L28.0273 35.917V34.9795C28.0273 33.124 27.7539 32.4013 26.8848 31.8838C26.4941 31.6591 26.2988 31.6103 25.7031 31.6006C25.0195 31.5908 24.9805 31.5713 24.8145 31.3076C24.5605 30.8974 23.9941 30.4287 23.5742 30.292C23.3691 30.2236 22.9785 30.1943 22.666 30.2236L22.1191 30.2724L21.8555 29.8427C21.7188 29.6181 21.4062 29.3056 21.1816 29.1494C20.8203 28.915 20.6738 28.8759 20.0586 28.8759H19.3457L19.3164 25.7509L19.2871 22.6259L19.0137 22.1279C18.6719 21.5224 18.3105 21.2392 17.6855 21.083L17.207 20.9756L17.168 20.1357C17.1191 19.0517 16.9141 18.6904 16.1426 18.3193C15.625 18.0654 15.498 18.0459 13.6328 17.9775C12.5488 17.9384 11.0938 17.8994 10.4004 17.8994L9.13086 17.8896L9.16016 15.3213C9.18945 12.5966 9.26758 12.0791 9.6875 11.4443C10 10.9853 10.6055 10.5752 11.1133 10.4873C11.582 10.4091 18.5547 10.4384 30.7129 10.5556ZM15.9961 19.1201C16.1133 19.1982 16.2793 19.4033 16.3574 19.5595C16.5527 19.9404 16.709 21.1123 16.5625 21.1123C16.3184 21.1123 15.7129 21.6396 15.5176 22.0107C15.4102 22.2256 15.2734 22.665 15.2344 22.9873C15.1367 23.6416 15.2832 28.7099 15.4297 30.0576C15.498 30.7314 15.498 31.0341 15.4004 31.3076L15.2832 31.6591L14.1113 30.9756C12.6562 30.1259 12.0508 29.8818 11.2012 29.8232C10.6348 29.7841 10.4688 29.8134 10 30.0478C9.05273 30.5166 8.70117 31.3466 9.0332 32.3525C9.24805 32.997 10.1074 33.8955 10.9863 34.3935C12.5391 35.2822 13.0273 36.2881 13.3301 39.2763C13.418 40.1064 13.5059 40.8681 13.5352 40.9658C13.584 41.1123 13.5547 41.1416 13.4082 41.0927C12.9102 40.9365 10.5273 40.7216 8.39844 40.6435C6.24023 40.5556 5.99609 40.5361 5.84961 40.3701C5.72266 40.2041 5.68359 38.9443 5.57617 30.0869C5.49805 23.2705 5.48828 19.9013 5.55664 19.7353C5.76172 19.1787 5.86914 19.1591 8.64258 19.1494C10.0391 19.1396 11.9043 19.1006 12.793 19.0615C15.625 18.9541 15.7617 18.9541 15.9961 19.1201ZM17.666 22.6357C17.8711 22.8896 17.8711 22.997 17.8711 27.1767V31.4541L18.125 31.6494C18.6523 32.0693 19.1797 31.8447 19.3945 31.1025C19.6094 30.3603 20.1074 30.1552 20.498 30.6435C20.6738 30.8681 20.7031 31.0439 20.7031 31.8056C20.7031 32.5673 20.7324 32.7431 20.9082 32.9677C21.1914 33.3291 21.7773 33.3291 22.0605 32.9677C22.1777 32.8213 22.2656 32.6064 22.2656 32.4892C22.2656 32.167 22.627 31.7568 22.9102 31.7568C23.0371 31.7568 23.2324 31.8447 23.3398 31.9521C23.5449 32.1572 23.5742 32.333 23.584 33.4853C23.584 34.3349 23.7695 34.6084 24.3555 34.6084C24.834 34.6084 25.0977 34.3642 25.0977 33.9345C25.0977 33.5732 25.5371 33.0263 25.8203 33.0263C26.0645 33.0263 26.3477 33.3681 26.4551 33.7783C26.5918 34.2861 26.4258 38.8857 26.2305 39.9599C25.6934 42.9482 24.4141 44.4912 22.1191 44.9111C20.9863 45.1162 18.9551 45.1162 18.1641 44.9013C16.7969 44.5302 15.9961 43.9052 15.4297 42.7822C15.0098 41.9619 14.7949 41.0439 14.5508 39.0615C14.1016 35.5459 13.7305 34.8232 11.6602 33.456C10.9766 33.0068 10.459 32.5869 10.2637 32.3232C9.36523 31.1025 10.5859 30.2529 12.3047 30.9072C12.9883 31.1611 15.1562 32.2256 15.2344 32.3427C15.2637 32.3818 15.4004 32.4404 15.5371 32.4697C15.7617 32.5283 15.8008 32.499 15.8496 32.2646C15.8789 32.1181 15.8594 31.9814 15.8105 31.9716C15.7617 31.9521 15.7227 31.8447 15.7227 31.7275C15.7227 31.5517 15.7031 31.542 15.625 31.6591C15.5566 31.7666 15.5273 31.7373 15.5273 31.5127C15.5273 31.3466 15.5664 31.1709 15.6152 31.122C15.8301 30.8877 16.1426 28.1826 16.2598 25.5556C16.3477 23.3779 16.416 22.8896 16.6211 22.6259C16.8848 22.2841 17.3926 22.2939 17.666 22.6357Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.2049 13.583C19.9998 13.749 19.9706 13.8662 19.9315 14.5791C19.912 15.0283 19.9315 16.4639 19.9706 17.7822C20.0389 20.0674 20.0487 20.1748 20.2635 20.3799C20.4784 20.6045 20.4881 20.6045 23.3006 20.5557C26.5526 20.5068 26.7088 20.4971 26.9041 20.2041C27.1581 19.8135 26.8065 15.3311 26.4452 14.4424C26.2303 13.8955 25.244 13.6318 22.8807 13.4951C20.6053 13.3682 20.4686 13.3682 20.2049 13.583ZM26.1424 14.7354C26.0252 15.458 25.8885 16.8838 25.8202 18.0752L25.7518 19.2861L25.1073 19.2178C24.7557 19.1885 23.7889 19.1592 22.9784 19.1592H21.4842V17.0596V14.9697L21.8065 14.9209C21.9725 14.8916 22.6952 14.8232 23.3885 14.7647C24.0916 14.6963 24.9706 14.5889 25.3416 14.5205C25.7225 14.4522 26.0643 14.3936 26.1131 14.3838C26.162 14.3838 26.1717 14.54 26.1424 14.7354Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.5973 13.4951C31.5426 13.6807 30.3708 14.6768 29.9704 15.7412C29.7067 16.415 29.6872 17.4502 29.9313 18.1338C30.4391 19.5986 31.777 20.5361 33.3688 20.5459C34.2379 20.5459 34.8532 20.3506 35.4977 19.8623C37.07 18.6807 37.2262 16.1416 35.7809 15.2529C35.5856 15.126 35.4391 14.9404 35.4 14.7646C35.3122 14.374 34.9411 14.0127 34.3454 13.7393C33.7301 13.4658 33.2028 13.3877 32.5973 13.4951ZM34.5797 14.9307C35.0973 15.0771 35.195 15.1357 35.361 15.458C35.6637 16.0635 35.7614 16.6787 35.6442 17.2549C35.3122 18.8369 33.8961 19.6084 32.5583 18.9346C31.0836 18.1924 30.859 16.6201 32.0504 15.4775C32.7731 14.7744 33.486 14.6279 34.5797 14.9307Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.9784 24.208C32.3631 24.6768 31.3866 25.9951 30.0487 28.1533C29.3553 29.2764 29.16 29.794 29.2967 30.1455C29.453 30.5654 29.746 30.585 33.5252 30.4678C37.3827 30.3604 37.4022 30.3506 37.4022 29.794C37.4022 29.3252 35.3124 26.1025 34.0233 24.5791C33.7303 24.2275 33.4471 23.9443 33.4081 23.9443C33.369 23.9443 33.1737 24.0615 32.9784 24.208ZM33.9842 25.6533C34.2674 26.2979 34.7362 27.294 35.0194 27.8701C35.3124 28.4463 35.5467 28.9443 35.5467 28.9736C35.5467 29.0029 34.5995 29.0225 33.4471 29.0225C32.2948 29.0225 31.3475 29.0127 31.3475 28.9932C31.3475 28.9736 31.5428 28.5732 31.7772 28.0947C32.0116 27.6065 32.4608 26.5713 32.7733 25.7803C33.0956 24.9893 33.3788 24.374 33.4178 24.4131C33.4471 24.4522 33.7108 25.0088 33.9842 25.6533Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M45.1364 11.4444C44.7263 13.4951 44.4626 16.0342 44.3161 19.2178C44.2185 21.3662 44.2282 22.7823 44.3259 26.3662C44.3942 28.8174 44.4821 32.8311 44.5017 35.2823L44.5505 39.7354L40.4196 39.794C38.1442 39.833 35.6833 39.9014 34.9607 39.96C33.0271 40.1162 31.6013 40.3213 31.5622 40.4385C31.5427 40.4971 31.4548 40.5459 31.3669 40.5557C30.8396 40.585 32.1482 40.7412 33.6911 40.8291C35.9958 40.9561 45.0974 41.083 45.4001 40.9854C45.9079 40.8291 45.8982 41.1026 45.8982 29.4424C45.8982 18.6807 45.8493 16.4053 45.5466 13.0557C45.3513 10.9561 45.2829 10.6826 45.1364 11.4444Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default webfrontend;
