import React from 'react';

const securescalable = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
        >
            <path
                d="M26.9609 9.49367C26.7749 9.59211 25.889 10.1718 24.9921 10.7843C22.7171 12.3155 21.4921 13.0155 19.939 13.6609C17.3249 14.7655 15.2468 15.3124 13.7265 15.3124C12.8624 15.3124 12.753 15.4218 12.7093 16.4718C12.6765 16.9202 12.6218 17.2812 12.578 17.2812C12.3812 17.2812 8.23585 16.3952 8.02804 16.3077C7.90773 16.253 7.42648 15.6187 6.9671 14.8968L6.11398 13.5843L6.3546 13.1249C7.27335 11.3749 6.03742 9.5046 4.05773 9.64679C3.21554 9.70148 2.23117 10.1937 1.93585 10.6968C1.24679 11.8671 1.65148 13.3984 2.83273 14.0874C3.25929 14.339 3.44523 14.3718 4.15617 14.3609L4.98742 14.3499L5.89523 15.7609C6.39835 16.5374 6.94523 17.2702 7.10929 17.3905C7.3171 17.5327 8.29054 17.7952 9.99679 18.1562C11.4296 18.4515 12.6327 18.7249 12.6765 18.7468C12.7202 18.7796 12.7968 19.5343 12.8296 20.4202C12.9499 23.1655 13.3765 25.878 14.0655 28.2405C14.1202 28.4265 13.9562 28.4374 11.7359 28.4374L9.35148 28.4265L9.0671 28.0109C8.23585 26.8187 6.12492 26.6765 5.11867 27.7374C4.48429 28.4155 4.30929 29.5093 4.6921 30.3624C5.05304 31.1499 6.11398 31.7624 7.10929 31.7515C8.1921 31.7405 9.02335 31.2155 9.36242 30.3515C9.53742 29.8921 9.53742 29.8921 10.1937 29.8155C10.5655 29.7827 11.6812 29.7499 12.6874 29.7499H14.514L15.0937 31.2484C15.7718 33.0202 16.8984 35.2843 17.8171 36.7499L18.4734 37.789L17.9593 37.8655C17.6749 37.8984 16.7015 37.9421 15.7827 37.9421C14.8749 37.953 13.989 38.0077 13.814 38.0734C13.6499 38.128 12.928 38.7515 12.228 39.4515C10.9921 40.6765 10.9484 40.7202 10.6421 40.5999C9.30773 40.0859 7.99523 40.6218 7.43742 41.9124C7.24054 42.3609 7.20773 42.6124 7.25148 43.0827C7.32804 43.7827 7.52492 44.089 8.26867 44.6468C9.38429 45.4999 10.664 45.4562 11.5937 44.5265C12.3374 43.7827 12.5015 42.9624 12.1187 41.9671C11.9984 41.6609 12.0421 41.6171 13.1905 40.4577L14.3827 39.2655H16.964H19.5562L20.3327 40.1734C22.4765 42.7109 25.7796 45.4234 27.2999 45.9155C29.214 46.539 31.0405 46.7577 33.2499 46.6593C35.5905 46.5609 37.078 46.2109 37.078 45.7843C37.078 45.2593 35.6562 44.9421 32.9874 44.8546L30.9312 44.789L31.7296 44.2421C33.1405 43.2796 34.7921 41.8577 36.203 40.3921L37.5702 38.9702L40.0093 39.2765C41.3655 39.4405 42.514 39.6265 42.6015 39.703C42.6999 39.7796 43.1812 40.3702 43.6843 41.0155L44.603 42.1968L44.3843 42.6562C43.7499 43.9359 44.4937 45.5437 45.8937 45.9374C47.064 46.2655 48.7155 45.5109 49.0984 44.4827C49.514 43.3999 48.9015 41.9124 47.8624 41.4421C47.3812 41.2234 46.4515 41.1687 46.003 41.3327C45.7515 41.4312 45.6859 41.3765 44.8874 40.3265C43.8374 38.9484 43.564 38.6312 43.1702 38.4343C42.9405 38.314 39.1562 37.7343 38.5765 37.7343C38.5327 37.7343 38.7843 37.3077 39.1452 36.7718C40.5671 34.6499 41.628 32.4515 42.3827 30.0452L42.7765 28.7655H45.653H48.5405L48.6171 29.0609C48.978 30.5155 50.6843 31.1171 52.2265 30.3405C53.5718 29.6624 53.9546 28.2405 53.1452 26.9718C52.2265 25.5062 49.6234 25.6155 48.8249 27.1468L48.6609 27.453H45.8718H43.0718L43.1265 27.2015C43.564 25.4515 43.7937 22.739 43.728 19.8405C43.6843 17.664 43.6952 17.3905 43.8484 17.3359C43.9468 17.303 44.8327 17.2921 45.828 17.314C47.1843 17.3249 47.6984 17.303 47.9062 17.1827C48.0593 17.1062 48.7265 16.5046 49.3937 15.8484L50.5968 14.6452L51.253 14.8202C51.6468 14.9187 52.0734 14.9624 52.3359 14.9187C53.703 14.7109 54.6327 13.3437 54.3374 11.9655C54.2062 11.3421 53.6484 10.7624 52.7843 10.3359C52.2812 10.0952 52.0624 10.0405 51.5812 10.0843C50.8374 10.1499 50.0718 10.5874 49.689 11.1671C49.328 11.7249 49.2296 12.6655 49.4702 13.2562L49.6343 13.639L48.464 14.8093L47.3046 15.9687H45.128C43.0827 15.9687 42.9405 15.9577 42.6234 15.728C42.4265 15.5968 42.1859 15.3999 42.0874 15.3015C41.978 15.1921 41.7046 15.0718 41.4859 15.039C36.914 14.2952 32.4405 12.5234 28.5249 9.90929C28.0218 9.57023 27.5405 9.29679 27.453 9.29679C27.3655 9.30773 27.1359 9.39523 26.9609 9.49367ZM27.8249 11.0796C28.0109 11.2218 28.6562 11.6374 29.2577 11.9984C32.4405 13.9124 36.3015 15.4218 39.7359 16.089L40.9937 16.3405L41.0812 17.3796C41.1905 18.8562 41.0593 22.6187 40.8624 23.8765C40.3155 27.3109 39.178 30.8546 37.8109 33.3593C35.7109 37.2312 32.3093 40.8952 28.3827 43.5312C27.7593 43.9577 27.1796 44.2968 27.114 44.2968C27.0484 44.2968 26.6655 44.0671 26.2609 43.7827C19.8296 39.2327 15.5093 31.664 14.3718 22.9577C14.2843 22.214 14.1749 20.4859 14.1421 19.1296L14.0765 16.6577L14.9187 16.5921C16.3734 16.4609 17.9812 15.9905 20.4312 14.9405C22.2468 14.1749 23.4062 13.5187 25.6374 12.0202C26.6109 11.364 27.4202 10.828 27.4421 10.828C27.464 10.828 27.639 10.9484 27.8249 11.0796ZM4.82335 11.1234C5.67648 11.6265 5.37023 12.8077 4.3421 12.9827C4.00304 13.0374 3.9046 12.9937 3.60929 12.7093C3.16085 12.2499 3.14992 11.703 3.59835 11.2546C3.97023 10.8937 4.35304 10.8499 4.82335 11.1234ZM52.4452 11.7577C52.9265 12.228 52.7515 13.0921 52.1171 13.3874C51.439 13.7155 50.739 13.2124 50.7171 12.3921C50.6952 11.5718 51.8546 11.1562 52.4452 11.7577ZM51.428 27.464C51.5484 27.5296 51.7124 27.7484 51.7999 27.9671C51.953 28.3062 51.9421 28.3827 51.7671 28.7437C51.5046 29.2796 51.089 29.4765 50.5859 29.3124C49.8421 29.0718 49.6452 28.2952 50.1593 27.6827C50.4655 27.3218 50.9796 27.2234 51.428 27.464ZM7.63429 28.5468C8.26867 28.9296 8.37804 29.5968 7.88585 30.089C7.42648 30.5484 6.80304 30.5374 6.40929 30.0671C6.25617 29.8812 6.12492 29.5968 6.12492 29.4327C6.12492 28.9515 6.68273 28.3609 7.16398 28.339C7.2296 28.328 7.43742 28.4265 7.63429 28.5468ZM10.4015 42.0327C11.2984 42.6343 10.9702 43.8593 9.92023 43.8593C9.42804 43.8593 8.85929 43.389 8.85929 42.9624C8.85929 42.6124 9.2421 42.0109 9.54835 41.8905C9.90929 41.7484 9.99679 41.7593 10.4015 42.0327ZM47.3593 43.0609C48.1687 44.0999 46.6155 45.2812 45.7734 44.2749C45.4562 43.903 45.4343 43.6515 45.6749 43.1921C46.014 42.514 46.878 42.4593 47.3593 43.0609Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M25.7356 15.4655C23.0668 16.3405 22.2137 19.7967 24.1606 21.9077C25.6262 23.4936 28.5574 23.4061 30.2418 21.7108C31.4012 20.5623 31.6637 18.9217 30.9418 17.4233C30.6356 16.7998 29.8809 16.1217 29.0059 15.717C28.3715 15.4217 28.1309 15.367 27.234 15.3452C26.5778 15.3233 26.0309 15.367 25.7356 15.4655ZM28.4153 16.953C29.0059 17.2045 29.684 17.8498 29.859 18.3311C30.1653 19.1186 29.8371 20.278 29.1699 20.8467C27.1684 22.542 24.4778 21.5577 24.4778 19.1295C24.4778 18.3858 24.8715 17.5655 25.4074 17.1717C26.1949 16.5811 27.3653 16.5045 28.4153 16.953Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M26.326 23.8547C22.8479 24.3141 20.1026 26.6656 18.4073 30.625C17.9151 31.7734 17.926 31.8828 18.6807 32.6266L19.3151 33.25H27.912C35.8198 33.25 36.5307 33.2391 36.6948 33.0641C36.9573 32.8125 36.8698 32.3422 36.3229 30.9313C35.7979 29.6078 35.0214 28.2516 34.2776 27.3766C33.5448 26.5125 31.576 24.9703 30.6792 24.5437C29.3339 23.8984 27.7917 23.6578 26.326 23.8547ZM28.9292 25.3203C31.0839 25.8891 32.7245 27.6391 33.9167 30.6469L34.1354 31.2047L30.176 31.1391C27.9995 31.0953 24.7401 31.0625 22.9464 31.0625H19.676L20.1682 30.1C21.437 27.6609 23.4495 25.9219 25.7026 25.3313C26.512 25.1125 28.1198 25.1125 28.9292 25.3203Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default securescalable;
