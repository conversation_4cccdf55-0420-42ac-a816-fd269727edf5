import React from 'react';

const ecommigration = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M29.3945 2.16248C28.0762 2.20154 24.9512 2.2699 22.4414 2.30896L17.8711 2.38708L17.2754 3.00232L16.6895 3.62732L16.7578 6.43005C16.7969 7.98279 16.7969 9.53552 16.7578 9.88708L16.6895 10.5316L13.8379 10.6097C12.2656 10.6488 9.11133 10.7074 6.81641 10.7465C2.60742 10.8148 2.34375 10.8441 2.34375 11.1761C2.34375 11.2347 2.16797 11.5668 1.96289 11.9183L1.57227 12.5629L1.47461 15.5414C1.25 21.7328 1.17188 27.5238 1.17188 37.2211V47.2406L1.62109 47.6117C1.86523 47.8168 2.09961 48.0902 2.14844 48.2172C2.19727 48.3441 2.29492 48.4515 2.36328 48.4515C8.97461 48.4711 13.9062 48.559 17.1387 48.7347C24.043 49.1058 28.125 49.1058 31.1523 48.7347C32.1289 48.6176 32.4902 48.4906 32.5684 48.266C32.6855 47.8949 32.8125 45.0433 32.8125 42.7191V40.2582L33.3301 40.307C34.668 40.4144 38.6621 40.5414 40.9082 40.5414C44.1797 40.5414 47.2266 40.2972 47.627 40.0043C47.7441 39.9164 47.8223 39.6625 47.8809 39.2133C47.9199 38.8422 47.998 33.5687 48.0371 27.5043C48.1152 16.6547 48.1543 15.639 48.4863 12.7094C48.5645 12.0355 48.6523 11.264 48.6816 11.0004C48.7402 10.5316 48.7207 10.4828 48.0762 9.46716C47.7148 8.89099 47.1289 8.06091 46.7773 7.61169C45.2832 5.7074 42.4316 3.26599 41.377 2.98279C38.7402 2.27966 34.7656 2.00623 29.3945 2.16248ZM36.3281 3.43201C37.7441 3.54919 39.2578 3.7738 40.3711 4.03748L41.2402 4.23279L41.1816 6.44958C41.1523 7.75818 41.0645 9.09607 40.9668 9.75037C40.8008 10.8051 40.8008 10.8539 40.9766 11.2152C41.25 11.7719 41.6406 11.9086 43.1738 12.0258C45.9961 12.2504 46.4258 12.641 46.5918 15.141C46.6406 15.8734 46.748 17.0648 46.8262 17.7875C46.9434 18.8519 46.9727 20.8344 46.9922 28.0414C47.0117 32.9535 46.9824 37.4457 46.9434 38.0219L46.8555 39.057L46.5527 39.1156C44.8535 39.4086 39.6094 39.4867 35.4004 39.2719C34.1113 39.2133 33.0469 39.1449 33.0371 39.1449C33.0273 39.1351 32.9883 37.8168 32.9492 36.2152L32.8809 33.3148H37.0508H41.2207L41.4551 33.0707C41.7383 32.7972 41.7578 32.6605 41.543 32.3578L41.3965 32.1429H37.1973H33.0078V29.7699C33.0078 28.4613 33.0371 26.7914 33.0762 26.059L33.1348 24.7211H35.4688H37.793V26.2152C37.793 27.8363 37.832 27.9828 38.3105 28.1C38.5742 28.1586 38.7207 28.0414 40.498 26.2543C42.2852 24.4769 42.4023 24.3304 42.3438 24.0668C42.2949 23.891 41.6797 23.1976 40.5371 22.0551C39.1016 20.6195 38.7598 20.3265 38.5254 20.3265C37.959 20.3265 37.8906 20.5023 37.8906 22.0551V23.4515H35.5859H33.2812L33.3496 22.7386C33.3789 22.3578 33.4766 21.4886 33.5645 20.8148C33.7988 18.9984 33.7988 19.0277 33.2129 18.0804C31.7969 15.7953 30.0684 13.9398 27.8027 12.2601C26.9238 11.6058 26.7773 11.5375 25.9473 11.3422C24.541 11.0199 22.7539 10.766 20.9375 10.6488L19.2676 10.5316L19.1992 8.42224C19.1699 7.26013 19.1113 5.66833 19.0723 4.87732L19.0137 3.43201L23.1055 3.40271C33.623 3.34412 35.2832 3.34412 36.3281 3.43201ZM43.6426 6.0199C44.8633 7.07458 46.1914 8.61755 46.9922 9.89685L47.1582 10.1508L46.6016 10.2191C45.9668 10.2875 42.207 10.3265 42.1484 10.2582C42.1289 10.2386 42.1484 9.97498 42.1973 9.68201C42.2461 9.38904 42.3145 8.21716 42.3438 7.07458C42.4121 5.12146 42.4219 5.01404 42.5879 5.12146C42.6855 5.18982 43.1543 5.59021 43.6426 6.0199ZM22.5586 12.0648C23.2324 12.1527 24.2383 12.3285 24.8047 12.4457L25.8301 12.6605L25.8984 13.1C25.9863 13.6957 25.8105 17.3187 25.6445 18.4711C25.4883 19.4965 25.5469 19.7601 26.0254 19.9359C26.1426 19.9847 26.2793 20.0629 26.3086 20.1215C26.4355 20.3168 27.1387 20.4242 28.9551 20.5219C31.5039 20.6488 31.6309 20.7465 31.7285 22.5531L31.7871 23.5492H28.4668C24.707 23.5492 24.707 23.5492 24.707 24.223C24.707 24.4183 24.7559 24.6332 24.8242 24.7015C24.9121 24.7894 25.8301 24.8187 28.3496 24.8187H31.7676L31.709 25.1801C31.6797 25.3851 31.6211 27.0355 31.582 28.8422L31.5137 32.1429H30.0586H28.6133V30.7953C28.6133 29.2523 28.5352 29.0179 27.998 29.0179C27.7246 29.0179 27.4707 29.2328 25.9082 30.7953C24.4141 32.2894 24.1211 32.6312 24.1211 32.8656C24.1211 33.1 24.4141 33.4418 25.8984 34.9359C27.4805 36.5375 27.7051 36.7328 27.9883 36.7328C28.5352 36.7328 28.6133 36.5082 28.6133 34.8578V33.4125H30.0684H31.5234L31.5918 35.141C31.6699 36.9281 31.5918 45.1605 31.4844 46.7621C31.4062 47.807 31.5723 47.6996 29.7852 47.8851C27.8711 48.0804 22.2656 48.0316 17.1875 47.7679C13.9355 47.6019 11.6309 47.5433 8.03711 47.5336L3.22266 47.5238V45.9125C3.22266 45.0238 3.29102 41.7719 3.36914 38.6859C3.70117 25.6488 3.74023 22.3871 3.62305 17.7484C3.56445 15.2679 3.48633 13.0219 3.45703 12.7484L3.38867 12.2406L4.69727 12.182C13.125 11.8109 20.3418 11.7719 22.5586 12.0648ZM28.9453 15.0531C30.2832 16.3422 31.9727 18.4613 31.7969 18.6371C31.7676 18.6761 30.6348 18.725 29.2773 18.7445L26.8262 18.7933L26.8945 18.266C26.9629 17.7972 27.1387 14.5941 27.1484 13.8031V13.4711L27.5684 13.8031C27.793 13.9789 28.4082 14.5453 28.9453 15.0531Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ecommigration;
