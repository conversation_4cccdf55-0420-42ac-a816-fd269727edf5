import React from 'react';

const RegressionTestingIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.1142 5.25331C14.3662 5.31191 11.1826 5.38027 9.02437 5.38027C5.36226 5.39003 5.09858 5.3998 4.9521 5.56581C4.51265 6.0541 4.97163 14.4721 5.46968 14.9896C5.63569 15.1654 5.64546 15.1557 5.70405 14.6478C5.83101 13.5736 5.95796 10.7318 5.95796 8.72011V6.64003L15.4599 6.70839C25.1376 6.77675 27.9013 6.72792 31.7392 6.41542C34.3759 6.19081 34.7861 6.15175 34.6982 6.07363C34.4736 5.86855 31.0166 5.38027 28.497 5.19472C26.6416 5.06777 20.1572 5.09706 16.1142 5.25331Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.707 9.20834C18.3398 9.25717 12.5684 9.32553 11.8652 9.36459C10.7227 9.44271 10.5371 9.48178 10.0684 9.73568C9.42383 10.097 8.95508 10.6341 8.68164 11.3372C8.38867 12.0892 8.33008 13.8372 8.53516 15.8783C8.62305 16.7376 8.69141 17.5384 8.69141 17.6654C8.69141 17.89 8.68164 17.89 7.45117 17.89C6.75781 17.89 6.00586 17.9388 5.76172 17.9974C5.14648 18.1341 4.62891 18.5052 4.375 18.9935L4.15039 19.4037V29.9994V40.5951L4.41406 41.0345C4.57031 41.3079 4.83398 41.5716 5.09766 41.7181C5.50781 41.9525 5.56641 41.9622 7.32422 41.9525C9.26758 41.9329 13.1055 41.7572 13.4277 41.6693C13.6133 41.6107 13.6523 41.6986 13.8574 42.4017C14.707 45.3119 17.2852 46.7279 21.1523 46.3958C23.3398 46.2103 24.5801 45.7415 25.6055 44.7162C26.377 43.9447 26.7969 43.2415 27.207 42.0306C27.6367 40.7708 27.832 39.6185 27.9004 37.9388C27.9297 37.1771 27.9883 36.5033 28.0273 36.4447C28.0859 36.3568 28.2715 36.3568 28.7695 36.4251C29.1406 36.4837 29.8438 36.5521 30.3223 36.5814C30.8105 36.6204 32.3438 36.7083 33.7402 36.7962C36.4844 36.972 38.4766 36.9329 39.3359 36.6986C40.5078 36.3763 41.3086 35.7318 41.7676 34.7454L42.041 34.1497V23.1634C42.041 12.8412 42.0312 12.1478 41.8652 11.6693C41.6113 10.9564 41.0059 10.1556 40.4102 9.75521C39.6094 9.22787 39.0332 9.10092 37.5586 9.12045C36.8555 9.13021 31.0742 9.16928 24.707 9.20834ZM30.7129 10.556C39.4238 10.6439 39.1406 10.6244 39.834 11.2201C40.5566 11.8353 40.5176 11.0833 40.5469 22.9779L40.5762 33.6615L40.3027 34.2279C40.127 34.5794 39.8926 34.8822 39.668 35.0384C38.9648 35.5169 38.6133 35.556 33.9844 35.6634C31.6016 35.722 29.2773 35.8099 28.8379 35.849L28.0273 35.9173V34.9798C28.0273 33.1244 27.7539 32.4017 26.8848 31.8841C26.4941 31.6595 26.2988 31.6107 25.7031 31.6009C25.0195 31.5912 24.9805 31.5716 24.8145 31.3079C24.5605 30.8978 23.9941 30.429 23.5742 30.2923C23.3691 30.224 22.9785 30.1947 22.666 30.224L22.1191 30.2728L21.8555 29.8431C21.7188 29.6185 21.4062 29.306 21.1816 29.1497C20.8203 28.9154 20.6738 28.8763 20.0586 28.8763H19.3457L19.3164 25.7513L19.2871 22.6263L19.0137 22.1283C18.6719 21.5228 18.3105 21.2396 17.6855 21.0833L17.207 20.9759L17.168 20.1361C17.1191 19.0521 16.9141 18.6908 16.1426 18.3197C15.625 18.0658 15.498 18.0462 13.6328 17.9779C12.5488 17.9388 11.0938 17.8997 10.4004 17.8997L9.13086 17.89L9.16016 15.3216C9.18945 12.597 9.26758 12.0794 9.6875 11.4447C10 10.9857 10.6055 10.5755 11.1133 10.4876C11.582 10.4095 18.5547 10.4388 30.7129 10.556ZM15.9961 19.1204C16.1133 19.1986 16.2793 19.4037 16.3574 19.5599C16.5527 19.9408 16.709 21.1126 16.5625 21.1126C16.3184 21.1126 15.7129 21.64 15.5176 22.0111C15.4102 22.2259 15.2734 22.6654 15.2344 22.9876C15.1367 23.6419 15.2832 28.7103 15.4297 30.0579C15.498 30.7318 15.498 31.0345 15.4004 31.3079L15.2832 31.6595L14.1113 30.9759C12.6562 30.1263 12.0508 29.8822 11.2012 29.8236C10.6348 29.7845 10.4688 29.8138 10 30.0482C9.05273 30.5169 8.70117 31.347 9.0332 32.3529C9.24805 32.9974 10.1074 33.8958 10.9863 34.3939C12.5391 35.2826 13.0273 36.2884 13.3301 39.2767C13.418 40.1068 13.5059 40.8685 13.5352 40.9662C13.584 41.1126 13.5547 41.1419 13.4082 41.0931C12.9102 40.9369 10.5273 40.722 8.39844 40.6439C6.24023 40.556 5.99609 40.5365 5.84961 40.3704C5.72266 40.2044 5.68359 38.9447 5.57617 30.0872C5.49805 23.2708 5.48828 19.9017 5.55664 19.7357C5.76172 19.179 5.86914 19.1595 8.64258 19.1497C10.0391 19.14 11.9043 19.1009 12.793 19.0619C15.625 18.9544 15.7617 18.9544 15.9961 19.1204ZM17.666 22.6361C17.8711 22.89 17.8711 22.9974 17.8711 27.1771V31.4544L18.125 31.6497C18.6523 32.0697 19.1797 31.8451 19.3945 31.1029C19.6094 30.3607 20.1074 30.1556 20.498 30.6439C20.6738 30.8685 20.7031 31.0443 20.7031 31.806C20.7031 32.5677 20.7324 32.7435 20.9082 32.9681C21.1914 33.3294 21.7773 33.3294 22.0605 32.9681C22.1777 32.8216 22.2656 32.6068 22.2656 32.4896C22.2656 32.1673 22.627 31.7572 22.9102 31.7572C23.0371 31.7572 23.2324 31.8451 23.3398 31.9525C23.5449 32.1576 23.5742 32.3333 23.584 33.4857C23.584 34.3353 23.7695 34.6087 24.3555 34.6087C24.834 34.6087 25.0977 34.3646 25.0977 33.9349C25.0977 33.5736 25.5371 33.0267 25.8203 33.0267C26.0645 33.0267 26.3477 33.3685 26.4551 33.7787C26.5918 34.2865 26.4258 38.8861 26.2305 39.9603C25.6934 42.9486 24.4141 44.4915 22.1191 44.9115C20.9863 45.1165 18.9551 45.1165 18.1641 44.9017C16.7969 44.5306 15.9961 43.9056 15.4297 42.7826C15.0098 41.9622 14.7949 41.0443 14.5508 39.0619C14.1016 35.5462 13.7305 34.8236 11.6602 33.4564C10.9766 33.0072 10.459 32.5872 10.2637 32.3236C9.36523 31.1029 10.5859 30.2533 12.3047 30.9076C12.9883 31.1615 15.1562 32.2259 15.2344 32.3431C15.2637 32.3822 15.4004 32.4408 15.5371 32.4701C15.7617 32.5287 15.8008 32.4994 15.8496 32.265C15.8789 32.1185 15.8594 31.9818 15.8105 31.972C15.7617 31.9525 15.7227 31.8451 15.7227 31.7279C15.7227 31.5521 15.7031 31.5423 15.625 31.6595C15.5566 31.7669 15.5273 31.7376 15.5273 31.513C15.5273 31.347 15.5664 31.1712 15.6152 31.1224C15.8301 30.888 16.1426 28.1829 16.2598 25.556C16.3477 23.3783 16.416 22.89 16.6211 22.6263C16.8848 22.2845 17.3926 22.2943 17.666 22.6361Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.2049 13.5834C19.9998 13.7494 19.9706 13.8666 19.9315 14.5795C19.912 15.0287 19.9315 16.4642 19.9706 17.7826C20.0389 20.0678 20.0487 20.1752 20.2635 20.3803C20.4784 20.6049 20.4881 20.6049 23.3006 20.556C26.5526 20.5072 26.7088 20.4974 26.9041 20.2045C27.1581 19.8138 26.8065 15.3314 26.4452 14.4428C26.2303 13.8959 25.244 13.6322 22.8807 13.4955C20.6053 13.3685 20.4686 13.3685 20.2049 13.5834ZM26.1424 14.7357C26.0252 15.4584 25.8885 16.8842 25.8202 18.0756L25.7518 19.2865L25.1073 19.2181C24.7557 19.1888 23.7889 19.1596 22.9784 19.1596H21.4842V17.0599V14.9701L21.8065 14.9213C21.9725 14.892 22.6952 14.8236 23.3885 14.765C24.0916 14.6967 24.9706 14.5892 25.3416 14.5209C25.7225 14.4525 26.0643 14.3939 26.1131 14.3842C26.162 14.3842 26.1717 14.5404 26.1424 14.7357Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.5993 13.4951C31.5446 13.6807 30.3727 14.6768 29.9723 15.7412C29.7086 16.415 29.6891 17.4502 29.9333 18.1338C30.4411 19.5986 31.779 20.5361 33.3708 20.5459C34.2399 20.5459 34.8551 20.3506 35.4997 19.8623C37.0719 18.6807 37.2282 16.1416 35.7829 15.2529C35.5875 15.126 35.4411 14.9404 35.402 14.7646C35.3141 14.374 34.943 14.0127 34.3473 13.7393C33.7321 13.4658 33.2047 13.3877 32.5993 13.4951ZM34.5817 14.9307C35.0993 15.0771 35.1969 15.1357 35.3629 15.458C35.6657 16.0635 35.7633 16.6787 35.6461 17.2549C35.3141 18.8369 33.8981 19.6084 32.5602 18.9346C31.0856 18.1924 30.861 16.6201 32.0524 15.4775C32.775 14.7744 33.4879 14.6279 34.5817 14.9307Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.9784 24.2086C32.3631 24.6774 31.3866 25.9957 30.0487 28.1539C29.3553 29.277 29.16 29.7946 29.2967 30.1461C29.453 30.566 29.746 30.5856 33.5252 30.4684C37.3827 30.361 37.4022 30.3512 37.4022 29.7946C37.4022 29.3258 35.3124 26.1032 34.0233 24.5797C33.7303 24.2282 33.4471 23.945 33.4081 23.945C33.369 23.945 33.1737 24.0621 32.9784 24.2086ZM33.9842 25.6539C34.2674 26.2985 34.7362 27.2946 35.0194 27.8707C35.3124 28.4469 35.5467 28.945 35.5467 28.9742C35.5467 29.0035 34.5995 29.0231 33.4471 29.0231C32.2948 29.0231 31.3475 29.0133 31.3475 28.9938C31.3475 28.9742 31.5428 28.5739 31.7772 28.0953C32.0116 27.6071 32.4608 26.5719 32.7733 25.7809C33.0956 24.9899 33.3788 24.3746 33.4178 24.4137C33.4471 24.4528 33.7108 25.0094 33.9842 25.6539Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M45.1364 11.4449C44.7263 13.4956 44.4626 16.0347 44.3161 19.2183C44.2185 21.3667 44.2282 22.7827 44.3259 26.3667C44.3942 28.8179 44.4821 32.8316 44.5017 35.2827L44.5505 39.7359L40.4196 39.7945C38.1442 39.8335 35.6833 39.9019 34.9607 39.9605C33.0271 40.1167 31.6013 40.3218 31.5622 40.439C31.5427 40.4976 31.4548 40.5464 31.3669 40.5562C30.8396 40.5855 32.1482 40.7417 33.6911 40.8296C35.9958 40.9566 45.0974 41.0835 45.4001 40.9859C45.9079 40.8296 45.8982 41.1031 45.8982 29.4429C45.8982 18.6812 45.8493 16.4058 45.5466 13.0562C45.3513 10.9566 45.2829 10.6831 45.1364 11.4449Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default RegressionTestingIcon;
