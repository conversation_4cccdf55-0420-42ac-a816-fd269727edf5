import React from 'react';

const BootStrapIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131098)"
                transform="translate(0 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131098"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131098" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131098"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAACf5JREFUeF7tWt1OHNkRrjrdPbBKYojXSS7NPoHxE2TmMokcZmCwkygbIKuNlKuMWRaTi2gdyYnBg+zZ21gRkJUdwu+wZJNL8BPYfgLbt052ASXxwkz3Odmqc7qnmb/uxrAgeVpC/PX0qfPVV1Vf1WmEN/zCN3z/0AGgw4A3HIFOCLzhBOgkwU4IdELgCAgUhv/UL9D5CBGzKBQAyCM85XU+IsyH9boSvLJE+H3p4a+eJH1q4hAoDD8YtWTPHIILtHdEBN+cpIu//v0SpHCBXYACXHt3rPTwvfkkz00EQGF4qd92zz0WSgCIfRBKgkAEoN9P40IXAF1QSCywwRMuuGrncmk5PhMSATCe+2fZll0Dgigv9gGIBWABnBoHJANAF4OAAB56G7PL+WxcfyQC4IPcZzuOdHoBaeEKr4FoMQNo8a/z4vALFjRMIAYA7BZXhr8d15ZEZk/m/q4sqemuhI+8E3gg7qL6Pp3A2IA2IUSerb+Qgp54pwTwzxwKFf7uoYCZlaHY+4p9Iy04lSsrZGOFiTsAFIkewQlLM8gFVBIIUNoIxbDe7OGKQsmt1cUFiECgzxhG0jNur+ZiGxX7xgYAQNM+ugweLllBvsAKJ1FLaU8SALR1BgGl9qwyQLexkhMySA0CSFAo4PbqQOx9xb7xKAAoBqkGAMet0h7XBlMSNbWciokBVdOb7q0lt9bhRc87wwC4Qmh6ilcgaMPSNrHve4y+22C73QDKZhA4Skyc+9n91AEo5O+nu73vbPk5gJINhYDFljZXgmS8K9X1u2s/LkUlyKnsXJ/t9vaBDVkpMAuAF+OEAIVUjQGaMVXnZWZ28f3tqDWDJBznxgAAk7U1AKItAHSr57mZ2XIuljG+HYXsXG+39a0SgjMiIzUGAUCMoURIOeCEABi/9udCqvL2PUoa2ud2DAAEeJ7MzJavJALAB2JyaHMeAUbaO6gRgIPUv6/f/et7kaxLxIAP8gvzjtczwiXMZG2iX/sqIAA9kZku/4ABmMqup4WALb+GczmVSosoBeWqs/fx7Mq7AVjEhJTTu4MmbzQHQgPAYUi2Eeus/yzcWfr5aBxmx64Ck4Mrz4Sy+/xFpEqxHtA5oPVVlSIzGwCwmUYhtnzhQ4sLElYowRPU2FTgAP6bKYVAmLj6YNvyzn2/tRaoK7MoQYJ8PrOae+fYAPgwu95vIT7Wm6cvAbEAUAKqCgIAJrKbacuqAeCXRZ1WXJBkvHQ3iuu5QMuP/2TuplO98FESADh5Sved6XLueRQIsRgwlV2fB0SmPxnJYkbGYEADAOtp4egQYAVnNAET2KhA9NxH0+Vc2jd84tpC2XLPD0QBoO1i12gBJa2F6fIPI8MgEoAPh+f6RbV3CxG5CUoEAABUZS0JTmTX05ZNABgQjaz2N8eMkO7HfyznCkEivPbgGbrn+pICANLa/RL2LpfKP23LgrYAUBLqwm9uITj9WsLqAURsBpCjpcxMmypASRCFvaVDSTdTVE205wAsCU/tHZm+uZ3bpb9N5h8WEN66RwIpGgD9NJ8B/Blx8ORLbydTKo/x85pdLQG4kbs/ALKnhJbdF+7WNAC0sxRTzaKBSJurKiuBDiA2IVgllnniABQqUNQEKXwuQG0XF2vTnN9d+WzUs9Q9JVRvu1Zb9wJ+CJGENqJM2Sy3Ef73HKy9wh9W3t+IDcDU0H1leSRNUyCFE8jTWggQADZ/tQOADD/ASqa0El8ITeU/STvV87/xBGS1DG4/b2wPgAsOvGIpTlXm1spIg7eaum8qu6ocqT3tWhZ3WP5VU2b6bxZPhJpfRwGAnvRV0u2Twh4FFAUE2dOOYVoD1AYyzACjVSwlwVE0uQKoYgpurTe2yc0BGNhQ/EGUGgBfjrLXiRk0E6SJkARVNw/QBtWuqqQyqJVgITvXl7Jtreyo7nNOodZX7QopnlSr+DQcr4Xh9f6UsrZRYVsQ9GohPWBmhZaU0OXp/1aFDbfWGgclLQP4t9lPskI5JSlSFxkARtUGkN16MZ4JRgOgJISS4GZaoNBlkC/6vGYaN1kSdz2h5mfWBq77d0zkl9OW6g59pp4PpqIc+rMeuBADLAkvlNov3Cq/W27GpOgqAN/dRrAu6eGjAcFUBG4+6lxez4BoAMxs0WR6Atu1dsaKy78Ixts3BjefAMKldqFw+H+CFaYC7+m++lf6SFXAfyCpQAFvbSO6NRr6M7xjAUCrS62oyWia73/xqLg0EoihycGNm4iipRpsBEYQW/ekfJUulnNtD0sihRA9XCtBh2M3PKig31+PAWYERgDQ0IQUHKTAtfYeFZd/FgFAuDr4DVFNUSrwFu6sDr6+EqRNUv22vJ7HVBY1ALrv1gAcxjBZCGiP84AUdCgQAJ69c6iba84AA4DJ+MKEkKRQFRWQsHO5GOOAJBYDmAWDD56D6rlYYwA1L+ZcIMTBRgDczFfa3rTD9UnQAMCChUbbVBdwz7M+TxeXxwLq3hj6tAyAjf2AyfbEHEHJmdp0UQFl776487eRlvI5HDKxAZjML8wL7+2RJADwSU1oIjSRn0tbJIU5lrQHgxIr4QWg3K6IVzdLi78O9DuVzi7rwrPmCZBOqHToHALA+XzhzuIvI+nPZsTNrOP5vxRS3vl74RDQDGj9CAqTCriJlGDYHupFHKd3S0i7v5WdikfoetLMwYkSqqmXxz8RKuTn0t3u97ZIX9Oivj4/KQAK3DdcmHMU9LebuRCDeLBiBBirT+dlprQ4FmsMF5sBDQAY+Rk5EVLu9btrucj5nPZ21yWJ+30C7Cyqb2RJeOkpdLtL/99vgmhQeyIA6HL4D6UZYBrPiLG4DnXSov4mRChkajM83W02jrZ0vYkGwJ8K6+ZJwMzqj2I7NvaNYQDo5zjnAvrlEcNNM06rhQyJFbO5YNTGvvTrq06S5ti7NQdCU2GyS5xFAIINSn2YShvnul3fwOhxVhiEMwuAf44XdTjKIRAapiLQcXrI+75r/Xv8noPBoflO+xDwsz8fjDBjBNxeO6EQmBz8VFGHpY+stHFRAChVD0BX6/cBghcvms8Mm4WB/44AnTtSBSDH3F67Eju0Y99Ii08OLe8K7s3Jg3okpuh8O3Q22OyFhsNnh028X8+CYKfNvR9+QcJ/P0BAhWcLHuLezMpgb1x9kwiAiaGlsgAYYNqZgxGsK9Jf16syNclN43WtCEmfuIAbxZUTekeI3xKT9mPbNES0d35HKLJUxfVH0vtqpdTPSQdYvVxavhr7fcFEDCDzxoeXRm1pz1HMMQB+Uktq+7Hcryc//mtyEqtjxeWrJ/eeoG9zIb+cthUUBOCARQBEqrVj2W3jQ1iSuyTNNhSo0uzKcCz5G35QYgac0FZO7bEdAE4N+jOycIcBZ8QRp2ZGhwGnBv0ZWbjDgDPiiFMzo8OAU4P+jCz8fxwM6H1PDZ32AAAAAElFTkSuQmCC"
                />
            </defs>
        </svg>
    );
};

export default BootStrapIcon;
