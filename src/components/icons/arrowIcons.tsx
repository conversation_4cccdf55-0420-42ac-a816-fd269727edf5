import { IconProps } from '../../types/icon';

export function RightArrowWithLineIcon({ width, height, fill, className = '' }: IconProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            fill={fill}
            className={className}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M0 5.99981C0 5.83405 0.0632157 5.67508 0.17574 5.55787C0.288264 5.44066 0.44088 5.37481 0.600014 5.37481H8.7518L6.17534 2.69231C6.06267 2.57496 5.99938 2.41578 5.99938 2.24981C5.99938 2.08384 6.06267 1.92467 6.17534 1.80731C6.28801 1.68995 6.44081 1.62402 6.60015 1.62402C6.75948 1.62402 6.91229 1.68995 7.02496 1.80731L10.625 5.55731C10.6809 5.61537 10.7252 5.68434 10.7555 5.76027C10.7857 5.8362 10.8013 5.9176 10.8013 5.99981C10.8013 6.08202 10.7857 6.16342 10.7555 6.23935C10.7252 6.31529 10.6809 6.38426 10.625 6.44231L7.02496 10.1923C6.91229 10.3097 6.75948 10.3756 6.60015 10.3756C6.44081 10.3756 6.28801 10.3097 6.17534 10.1923C6.06267 10.075 5.99938 9.91578 5.99938 9.74981C5.99938 9.58384 6.06267 9.42467 6.17534 9.30731L8.7518 6.62481H0.600014C0.44088 6.62481 0.288264 6.55897 0.17574 6.44176C0.0632157 6.32455 0 6.16557 0 5.99981Z"
                fill="var(--color-gray-500)"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.3998 1C11.559 1 11.7116 1.06585 11.8241 1.18306C11.9366 1.30027 11.9999 1.45924 11.9999 1.625V10.375C11.9999 10.5408 11.9366 10.6997 11.8241 10.8169C11.7116 10.9342 11.559 11 11.3998 11C11.2407 11 11.0881 10.9342 10.9756 10.8169C10.863 10.6997 10.7998 10.5408 10.7998 10.375V1.625C10.7998 1.45924 10.863 1.30027 10.9756 1.18306C11.0881 1.06585 11.2407 1 11.3998 1Z"
                fill="var(--color-gray-500)"
            />
        </svg>
    );
}
export function OpenLinksIcon(
    {
        // width = '16',
        // height = '11',
        // fill = 'none',
        // className = '',
    }: IconProps
) {
    return (
        <svg
            height="7"
            width="12"
            fill="none"
            viewBox="0 0 12 7"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M1 1L6 6L11 1"
                stroke="var(--color-primary-800)"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
            />
        </svg>
        // <svg
        //     width={width}
        //     height={height}
        //     fill={fill}
        //     className={className}
        //     viewBox="0 0 16 11"
        //     xmlns="http://www.w3.org/2000/svg"
        // >
        //     <path
        //         d="M0.818636 4.40303L0.0968247 4.39211L0.0750013 5.83574L0.796812 5.84665L0.818636 4.40303ZM0.807724 5.12484L0.796812 5.84665L14.0635 6.0472L14.0744 5.32539L14.0853 4.60358L0.818636 4.40303L0.807724 5.12484ZM10.5215 0.375313L9.7996 0.375313C9.7996 3.48539 12.2454 6.04733 15.3119 6.04733V5.32544V4.60354C13.0871 4.60354 11.2434 2.733 11.2434 0.375313L10.5215 0.375313ZM15.3119 5.32544V4.60354C12.2455 4.60354 9.79949 7.16547 9.79949 10.2756H10.5214H11.2433C11.2433 7.9179 13.0871 6.04733 15.3119 6.04733V5.32544Z"
        //         fill="white"
        //     />
        // </svg>
    );
}

export function RightArrowIcon({ width, height, fill, className = '', stroke = '' }: IconProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            fill={fill}
            className={className}
        >
            <path
                d="M2.01526 5.72223L1.01538 5.70712L0.985147 7.70689L1.98503 7.72201L2.01526 5.72223ZM1.98503 7.72201L20.3627 7.99982L20.3929 6.00005L2.01526 5.72223L1.98503 7.72201ZM14.4561 0.14286C14.4561 4.45108 17.8442 8 22.0921 8V6C19.0101 6 16.4561 3.40883 16.4561 0.14286L14.4561 0.14286ZM22.0921 6C17.8442 6 14.456 9.54889 14.456 13.8571H16.456C16.456 10.5912 19.01 8 22.0921 8V6Z"
                stroke={stroke}
            />
        </svg>
    );
}
