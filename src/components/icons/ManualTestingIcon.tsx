import React from 'react';

const ManualTestingIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M28.3216 3.87733C27.5989 3.95546 23.2044 4.45351 18.5559 4.98085C13.9075 5.51796 9.46415 6.02577 8.67313 6.1039C6.4368 6.32851 5.51883 6.62148 4.71805 7.41249C4.16141 7.9496 3.81962 8.65273 3.69266 9.55116C3.59501 10.1957 3.61454 10.5668 3.80008 12.0609C3.91727 13.0277 4.49344 18.018 5.07938 23.1449C7.08133 40.4984 7.13016 40.8598 7.67704 42.7348C8.00907 43.8871 8.19461 44.2191 8.90751 44.8734C9.85477 45.7621 11.1829 46.2894 12.4329 46.2894C12.7161 46.2894 16.7688 45.85 21.4368 45.3129C26.1145 44.7855 30.1966 44.3168 30.5188 44.2875C31.0657 44.2289 31.2122 44.2582 32.9114 44.8734C36.8567 46.2992 37.5989 46.475 38.5364 46.1527C39.5813 45.8012 40.1673 45.1273 40.5677 43.848C40.8118 43.0863 45.0403 31.182 45.9095 28.8383C46.3489 27.6273 46.3977 27.432 46.3489 26.9633C46.2317 25.9379 45.6263 25.0297 44.7864 24.5902C44.4153 24.3949 42.345 23.6332 38.6243 22.3148C37.6087 21.9535 36.7395 21.6312 36.7005 21.5922C36.6321 21.5336 35.4407 11.2699 35.1966 8.64296C35.0794 7.2953 35.0208 6.99257 34.7669 6.46523C34.4153 5.70351 33.3802 4.63905 32.6477 4.25819C31.5149 3.69179 30.7337 3.62343 28.3216 3.87733ZM31.2903 4.93202C32.0227 5.12733 32.8235 5.66444 33.3313 6.28944C33.8391 6.93398 33.9563 7.22694 34.0345 8.05702C34.0638 8.42812 34.4055 11.4652 34.7766 14.7953C35.1477 18.1254 35.4407 20.9184 35.4309 21.0062C35.4016 21.143 35.1575 21.0844 33.7903 20.5961C31.4563 19.7465 30.6555 19.7367 29.8938 20.4984C29.2688 21.1234 29.4837 20.5668 25.5872 31.5434C23.4485 37.5785 23.0872 38.7699 23.1751 39.6195C23.2825 40.6156 24.0247 41.5824 25.0501 42.0316C25.3235 42.1488 26.2122 42.4906 27.0325 42.7738C28.1067 43.1547 28.4876 43.3207 28.3509 43.3793C28.0677 43.4867 14.4641 45.0199 13.7708 45.0199C12.5794 45.0101 11.0755 44.2582 10.5091 43.3793C10.2552 42.9887 9.8743 41.6801 9.6204 40.3422C9.46415 39.4535 9.40555 38.9652 7.52079 22.7543C6.82743 16.768 6.21219 11.5141 6.14383 11.0844C5.82157 8.78944 6.67118 7.54921 8.78055 7.21718C9.38602 7.12929 25.8802 5.23476 28.6634 4.94179C30.3723 4.76601 30.6263 4.76601 31.2903 4.93202ZM36.7688 22.8519C44.1907 25.5082 43.8001 25.3422 44.1516 25.9086C44.5423 26.5531 44.5716 26.9144 44.2884 27.6762C43.6634 29.3363 38.7708 43.1059 38.7708 43.2035C38.7708 43.2621 38.6634 43.5453 38.5266 43.8187C38.3509 44.1996 38.1751 44.4144 37.8235 44.6391C37.3938 44.9125 37.3059 44.932 36.72 44.893C36.3587 44.8637 35.7239 44.7074 35.2552 44.5316C34.7962 44.3558 32.4134 43.5062 29.9524 42.6273C27.4915 41.7484 25.304 40.9183 25.0989 40.7816C24.8548 40.6254 24.6399 40.3519 24.4641 39.9906L24.2005 39.4535L24.4056 38.5258C24.6497 37.4711 30.0891 22.1 30.3919 21.5922C30.6458 21.1918 31.0852 20.9769 31.5442 21.0551C31.7298 21.0844 34.0833 21.8949 36.7688 22.8519Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M25.5469 8.16482C25.459 8.24294 25.3906 8.3699 25.3906 8.43826C25.3906 8.59451 25.6738 10.9187 25.7324 11.2508C25.7715 11.5144 25.752 11.5242 25.4004 11.5242C24.9707 11.5242 24.707 11.7586 24.707 12.1199C24.707 12.4715 24.8828 12.5984 25.4004 12.5984C25.918 12.5984 25.8887 12.5496 26.0352 13.8191C26.1816 15.0984 26.6992 15.8894 27.5488 16.114C28.3301 16.3289 29.1992 15.7625 29.1992 15.0496C29.1992 14.6394 28.8184 14.3758 28.457 14.5418C28.3203 14.6004 28.1836 14.7469 28.1543 14.8543C28.0664 15.157 27.6367 15.0594 27.4219 14.6785C27.2559 14.4051 27.1973 14.1414 27.0117 12.8328L26.9531 12.4324L27.5586 12.3738C28.2422 12.3152 28.5742 12.0613 28.457 11.6804C28.3496 11.3094 28.125 11.2117 27.4707 11.2898L26.8652 11.3484L26.8164 11.0261C26.7871 10.8406 26.6895 10.1472 26.6113 9.48318C26.5332 8.82888 26.4258 8.22341 26.3574 8.14529C26.2109 7.95974 25.7324 7.96951 25.5469 8.16482Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M12.686 10.0489C11.4653 10.2442 10.4008 10.4688 10.313 10.5371C10.1176 10.6934 10.1079 11.0352 10.2934 11.2793C10.4008 11.4356 10.5083 11.4453 11.0942 11.3965C11.4653 11.3574 11.934 11.3086 12.1294 11.2696C12.5688 11.1914 12.4614 10.791 13.0864 14.8438C13.3989 16.7969 13.6333 18.0664 13.7212 18.1543C13.8969 18.3301 14.3364 18.3203 14.5219 18.1348C14.7075 17.959 14.7075 18.0274 14.0337 13.7012L13.6137 11.0449L13.8579 10.9961C13.9946 10.9668 14.4438 10.8985 14.8442 10.8301C15.6352 10.7129 15.9184 10.5469 15.9184 10.1953C15.9184 9.8633 15.6645 9.66799 15.2544 9.67776C15.059 9.68752 13.8969 9.85354 12.686 10.0489Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M21.3184 11.4167C20.7422 11.6804 20.4102 12.2078 20.4102 12.8718C20.4102 13.9363 21.1816 14.6492 22.3438 14.6492C22.7539 14.6492 23.0371 14.7956 23.1348 15.0691C23.291 15.4792 22.9004 15.9187 22.4609 15.8308C22.3828 15.8113 22.1777 15.6746 21.9922 15.5183C21.8164 15.3621 21.6113 15.2351 21.5332 15.2351C21.3184 15.2351 20.9961 15.5964 20.9961 15.821C20.9961 16.1628 21.6699 16.7781 22.1875 16.9148C23.6621 17.2956 24.8535 15.616 23.9941 14.3562C23.6523 13.8386 23.3398 13.6726 22.5488 13.5652C21.7676 13.4578 21.4844 13.282 21.4844 12.9304C21.4844 12.364 22.1094 12.2078 22.5391 12.657C22.793 12.9206 23.252 12.9695 23.4375 12.7449C23.9062 12.1882 22.9688 11.2312 21.9727 11.2312C21.8359 11.2312 21.543 11.3191 21.3184 11.4167Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M16.75 11.748C16.3301 11.9434 15.8418 12.373 15.6465 12.7344C15.5293 12.959 15.4902 13.2715 15.4902 13.9648C15.5098 16.3184 16.4082 17.6758 17.9609 17.6758C19.1621 17.6758 20.4219 16.377 19.709 15.8789C19.377 15.6445 19.2109 15.6836 18.8594 16.0742C18.0879 16.9141 17.1992 16.6797 16.8086 15.5371C16.75 15.3516 16.7207 15.1855 16.7402 15.166C16.7598 15.1465 17.3164 15.0391 17.9805 14.9316C19.4941 14.6973 19.582 14.6191 19.5137 13.6523C19.4551 12.9004 19.1328 12.2559 18.6348 11.8945C18.2441 11.6211 17.209 11.543 16.75 11.748ZM18.0293 12.8613C18.1953 13.0273 18.498 13.6621 18.4395 13.7207C18.3711 13.7891 16.5938 14.0234 16.5645 13.9746C16.5449 13.9453 16.5547 13.7402 16.5938 13.5254C16.7012 12.8711 17.6094 12.4414 18.0293 12.8613Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M17.9311 23.3789C11.5151 24.3164 10.4018 24.502 10.2944 24.6582C9.99168 25.0684 10.2163 25.4883 10.7436 25.4883C11.31 25.4883 26.0757 23.3496 26.2807 23.2324C26.4956 23.125 26.5346 22.5684 26.3491 22.3828C26.2807 22.3145 26.0561 22.2656 25.8413 22.2754C25.6167 22.2852 22.062 22.7832 17.9311 23.3789Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M17.6768 28.6719C14.21 29.1699 11.2803 29.6094 11.1533 29.6387C11.0361 29.668 10.8897 29.8047 10.8311 29.9512C10.6943 30.3223 10.9385 30.6641 11.3584 30.6641C11.8564 30.6641 24.5322 28.8184 24.7861 28.7109C24.9424 28.6426 25.001 28.5352 25.001 28.291C25.001 27.8613 24.8447 27.7344 24.376 27.7441C24.1514 27.7441 21.1436 28.1641 17.6768 28.6719Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M19.2398 33.702C17.4918 33.9559 15.0796 34.3075 13.8785 34.4832C12.482 34.6883 11.6519 34.8543 11.5543 34.9422C11.3785 35.1278 11.4273 35.6454 11.6421 35.7821C11.7691 35.8602 13.146 35.7039 17.2574 35.118C20.2554 34.6981 22.814 34.2977 22.9507 34.2391C23.2535 34.1024 23.3316 33.6043 23.0972 33.3602C22.8921 33.1551 22.9507 33.1551 19.2398 33.702Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M15.9685 39.4238C13.4294 39.7949 12.3064 40 12.1892 40.1074C11.8376 40.4199 12.072 41.0156 12.531 41.0156C13.0192 41.0156 20.2751 39.9121 20.3923 39.8242C20.5388 39.6973 20.5485 39.1406 20.3923 38.9844C20.2165 38.8086 19.8942 38.8379 15.9685 39.4238Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M35.0692 27.9301C34.8934 27.9691 34.5614 28.1352 34.3465 28.2816C33.2625 29.0531 33.2039 30.7621 34.2196 31.6117C35.6356 32.7738 37.6961 31.8363 37.6961 30.0297C37.6961 29.434 37.5106 28.8871 37.1785 28.5356C36.7098 28.0277 35.7625 27.7543 35.0692 27.9301Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.2448 33.1547C29.4245 33.4672 28.8679 34.1117 28.5456 35.1175C28.3405 35.7621 28.4382 36.2406 28.8386 36.6703C29.1022 36.9437 29.6784 37.2074 32.2761 38.223C34.0046 38.8968 35.5476 39.473 35.7136 39.5121C36.739 39.7172 37.6569 38.5355 37.6276 37.0414C37.6179 36.3285 37.2565 35.5765 36.7194 35.1468C36.5339 34.9906 35.3718 34.473 34.1315 33.9847C31.6706 33.0082 30.9968 32.8617 30.2448 33.1547Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M41.1343 18.086C40.9683 18.252 40.9683 20.9083 41.1343 21.0743C41.3101 21.25 41.7495 21.211 41.9253 21.0157C42.062 20.8692 42.0913 20.6153 42.0913 19.5899C42.0913 18.9063 42.0425 18.2618 41.9937 18.1543C41.8765 17.9395 41.3296 17.8907 41.1343 18.086Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M43.701 21.4941C43.1639 21.7188 42.6463 21.9629 42.5584 22.041C42.2069 22.3535 42.4705 22.9492 42.9393 22.9492C43.2615 22.9492 45.0487 22.1777 45.244 21.9531C45.4295 21.7285 45.3905 21.416 45.1365 21.2109C44.9119 21.0352 44.7752 21.0645 43.701 21.4941Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ManualTestingIcon;
