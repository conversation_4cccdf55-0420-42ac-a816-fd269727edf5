import { CloseIcon } from './closeIcon';
import { HamburgerIcon } from './hamburgerIcon';
import { RightArrowIcon, OpenLinksIcon, RightArrowWithLineIcon } from './arrowIcons';
import { PlainIcon } from './plainIcon';
import WebsiteIcon from './WebsiteIcon';
import AppIcon from './AppIcon';
import PlatformIcon from './PlatformIcon';
import UIUXIcon from './UIUXIcon';
import IntegrationIcon from './IntegrationIcon';
import CustomFeatureIcon from './CustomFeatureIcon';
import CustomFeature2Icon from './CustomFeature2Icon';
import SecurityIcon from './SecurityIcon';
import MigrationIcon from './MigrationIcon';
import PlayIcon from './PlayIcon';
import LocationPin from './LocationPin';
import StarIcon from './StarIcon';
import GrowthIcon from './GrowthIcon';
import CalendarIcon from './CalendarIcon';
import QuoteBubble from './QuoteBubble';
import SearchIcon from './SerchIcon';
import PhpIcon from './PhpIcon';
import MySqlIcon from './MySqlIcon';
import LaravelIcon from './LaravelIcon';
import HtmlIcone from './HtmlIcone';
import CssIcon from './CssIcon';
import BootStrapIcon from './BootStrapIcon';
import RightIcon from './RightIcon';
import ContactIcon from './ContactIcon';
import MessageIcon from './MessageIcon';
import FacebookIcon from './FacebookIcon';
import InstagramIcon from './InstagramIcon';
import LinkedinIcon from './LinkedinIcon';
import UpArrow from './UpArrow';
import IdeaIcon from './IdeaIcon';
import ExpertTeamIcon from './EexpertTeamIcon';
import SupportIcon from './SupportIcon';
import OnTimeIcon from './OnTimeIcon';
import SaveCostIcon from './SaveCostIcon';
// import SolutionsIcon from './SolutionsIcon';
import SolutionServicesIcon from './SolutionsIcon';
import UiuxServicesIcon from './UiuxServicesIcon';
import TechServicesIcon from './TechServicesIcon';
import CrossServicesIcon from './CrossServicesIcon';
// import upportServicesIcon from './SupportServicesIcon';
import SupportServicesIcon from './SupportServicesIcon';
import WebsiteIconECommerce from './WebsiteIconECommerce';
import AppIconECommerce from './AppIconECommerce';
import PlatformIconECommerce from './PlatformIconECommerce';
import UiuxIconECommerce from './UiuxIconECommerce';
import IntegrationIconECommerce from './IntegrationIconECommerce';
import CustomFeatureIconECommerce from './CustomFeatureIconECommerce';
import SecurityIconECommerce from './SecurityIconECommerce';
import MigrationIconECommerce from './MigrationIconECommerce';
import CustomFeature2IconECommerce from './CustomFeature2IconECommerce';
import AnalyticsIconECommerce from './AnalyticsIconECommerce';
import EcomsecureIconECommerce from './EcomsecureIconECommerce';
// import FastScalableIconECommerce from './fastScalableIconECommerce';
import ResponsiveIntuitiveIconECommerce from './ResponsiveIntuitiveIconECommerce';
// import Detailscountershape from './Detailscountershape';
import DetailscountershapeIcon from './DetailscountershapeIcon';
import EcommerceIcon from './EcommerceIcon';
import QaTestingIcon from './QaTestingIcon';
import SeoMarketingIcon from './SeoMarketingIcon';
import UiUxDesignIcon from './UiUxDesignIcon';
import ManualTestingIcon from './ManualTestingIcon';
import AutomatedTestingIcon from './AutomatedTestingIcon';
import PerformanceTestingIcon from './PerformanceTestingIcon';
import SecurityTesting from './SecurityTesting';
import UsabilityTestingIcon from './UsabilityTestingIcon';
import RegressionTestingIcon from './RegressionTestingIcon';
import CompatibilityTestingIcon from './CompatibilityTestingIcon';
import ApiTestingIcon from './ApiTestingIcon';
import LoadTestingIcon from './LoadTestingIcon';
import ComprehensiveIcon from './ComprehensiveIcon';
import AutomationTestingIcon from './AutomationTestingIcon';
import BugTestingIcon from './BugTestingIcon';
import SupportTestingIcon from './SupportTestingIcon';
import ResultsSeoIcon from './ResultsSeoIcon';
import ExpertiseSeoIcon from './ExpertiseSeoIcon';
import CustomizedSeoIcon from './CustomizedSeoIcon';
import ReportingSeoIcon from './ReportingSeoIcon';
import KeywordResearchSeoIcon from './KeywordResearchSeoIcon';
import OnpageSeoIcon from './OnpageSeoIcon';
import TechnicalSeoIcon from './TechnicalSeoIcon';
import BacklinksSeoIcon from './BacklinksSeoIcon';
import LocalSeoIcon from './LocalSeoIcon';
import ContentSeoIcon from './ContentSeoIcon';
import EcommerceSeoIcon from './EcommerceSeoIcon';
import AuditSeoIcon from './AuditSeoIcon';
import AnalyticsSeoIcon from './AnalyticsSeoIcon';
import ResearchUiuxIcon from './ResearchUiuxIcon';
import WireframeUiuxIcon from './WireframeUiuxIcon';
import UiDesignUiuxIcon from './UiDesignUiuxIcon';
import UxStrategyUiuxIcon from './UxStrategyUiuxIcon';
import DesignSystemUiuxIcon from './DesignSystemUiuxIcon';
import MotionUiuxIcon from './MotionUiuxIcon';
import ResponsiveUiuxIcon from './ResponsiveUiuxIcon';
import TestingUiuxIcon from './TestingUiuxIcon';
import AccessibilityUiuxIcon from './AccessibilityUiuxIcon';
import CustomIntuitiveUiuxIcon from './CustomIntuitiveUiuxIcon';
import PixelPerfectUiuxIcon from './PixelPerfectUiuxIcon';
import DataDrivenUiuxIcon from './DataDrivenUiuxIcon';
import ScaleAdaptUiuxIcon from './ScaleAdaptUiuxIcon';
import ConsultIcon from './ConsultIcon';
import UiuxAppDevIcon from './UiuxAppDevIcon';
import WebDevIcon from './WebDevIcon';
import DeploymentIcon from './DeploymentIcon';
import ComplianceIcon from './ComplianceIcon';
import CustomIcon from './CustomIcon';
import SpeedIcon from './SpeedIcon';
import CustomScalableIcon from './CustomScalableIcon';
import ArchitectureIcon from './ArchitectureIcon';
import UxIcon from './UxIcon';
import SupportSoftwareDevIcon from './SupportSoftwareDevIcon';
import CustomWebDevIcon from './CustomWebDevIcon';
import WebappIcon from './WebappIcon';
import BackendIcon from './BackendIcon';
import PerformanceIcon from './PerformanceIcon';
import CustomResponsiveIcon from './CustomResponsiveIcon';
import SeoSpeedIcon from './SeoSpeedIcon';
import SecureScalableIcon from './SecureScalableIcon';
import IntegrationWebDevIcon from './IntegrationWebDevIcon';
import DisasterRecoveryIcon from './DisasterRecoveryIcon';
import CicdpipelineIcon from './CicdpipelineIcon';
import CloudInfrastructureIcon from './CloudInfrastructureIcon';
import ConfigurationIcon from './ConfigurationIcon';
import ContainerIcon from './ContainerIcon';
import MonitoringIcon from './MonitoringIcon';
import SecurityDevopsIcon from './SecurityDevopsIcon';
import InfrastructureIcon from './InfrastructureIcon';
import PerformanceDevopsIcon from './PerformanceDevopsIcon';
import ContinuousMonitoringIcon from './ContinuousMonitoringIcon';
import ScalabilityIcon from './ScalabilityIcon';
import SecurityIconDevops from './SecurityIconDevops';
import DiscoveryIcon from './DiscoveryIcon';
import UiuxMvpIcon from './UiuxMvpIcon';
import PrototypeIcon from './PrototypeIcon';
import AgileIcon from './AgileIcon';
import ApiIcon from './ApiIcon';
import UsabilityIcon from './UsabilityIcon';
import DeploymentMvpDevIcon from './DeploymentMvpDevIcon';
import PostLaunchIcon from './PostLaunchIcon';
import TimetomarketIcon from './TimetomarketIcon';
import CostEffectiveIcon from './CostEffectiveIcon';
import RiskReductionIcon from './RiskReductionIcon';
import FastScalableIconECommerce from './FastScalableIconECommerce';
import AagileServicesIcon from './AagileServicesIcon';
import AcelanLogo from './AcelanLogo';
import HeaderLogo from './HeaderLogo';
import ClientSupportIcon from './ClientSupportIcon';
import ProfessionalsIcon from './ProfessionalsIcon';
import SuperiorIcon from './SuperiorIcon';
import InnovativeIcon from './InnovativeIcon';
import AppDevelopmentIcon from './AppDevelopmentIcon';
import ECommerceIconSolution from './ECommerceIconSolution';
import WebDevelopmentIcon from './WebDevelopmentIcon';
import MVPDevelopmentIcon from './MVPDevelopmentIcon';
import UXDesignIcon from './UXDesignIcon';
import SoftwareSolutionIcon from './SoftwareSolutionIcon';
import DevopsSolutionIcon from './DevopsSolutionIcon';
import QAandTestingIcon from './QAandTestingIcon';
import SeoIcon from './SeoIcon';

export {
    CloseIcon,
    RightArrowWithLineIcon,
    OpenLinksIcon,
    RightArrowIcon,
    HamburgerIcon,
    PlainIcon,
};

const Icons = {
    // General/common icons
    CloseIcon,
    RightArrowWithLineIcon,
    OpenLinksIcon,
    RightArrowIcon,
    HamburgerIcon,
    PlainIcon,
    play: PlayIcon,

    acelanLogo: AcelanLogo,
    headerLogo: HeaderLogo,

    // E-commerce icons (add as needed)
    website: WebsiteIcon,
    app: AppIcon,
    platform: PlatformIcon,
    uiux: UIUXIcon,
    integration: IntegrationIcon,
    customFeature: CustomFeatureIcon,
    customFeature2: CustomFeature2Icon,
    security: SecurityIcon,
    migration: MigrationIcon,
    locationPin: LocationPin,
    ideaIcon: IdeaIcon,
    starIcon: StarIcon,
    growthIcon: GrowthIcon,
    calendarIcon: CalendarIcon,
    quoteBubble: QuoteBubble,
    searchIcon: SearchIcon,

    phpIcon: PhpIcon,
    mySqlIcon: MySqlIcon,
    laravelIcon: LaravelIcon,
    htmlIcone: HtmlIcone,
    cssIcon: CssIcon,
    bootStrapIcon: BootStrapIcon,
    rightIcon: RightIcon,
    // UIUXIcon: UIUXIcon,
    uiuxServicesIcon: UiuxServicesIcon,
    solutionServicesIcon: SolutionServicesIcon,
    techServicesIcon: TechServicesIcon,
    aagileServicesIcon: AagileServicesIcon,

    crossServicesIcon: CrossServicesIcon,
    supportServicesIcon: SupportServicesIcon,

    detailscountershapeIcon: DetailscountershapeIcon,

    ecommerceIcon: EcommerceIcon,
    qaTestingIcon: QaTestingIcon,
    seoMarketingIcon: SeoMarketingIcon,
    uiUxDesignIcon: UiUxDesignIcon,

    // WebsiteIconECommerce-----

    websiteIconECommerce: WebsiteIconECommerce,
    appIconECommerce: AppIconECommerce,
    platformIconECommerce: PlatformIconECommerce,
    uiuxIconECommerce: UiuxIconECommerce,
    integrationIconECommerce: IntegrationIconECommerce,
    customFeatureIconECommerce: CustomFeatureIconECommerce,
    securityIconECommerce: SecurityIconECommerce,
    migrationIconECommerce: MigrationIconECommerce,
    customFeature2IconECommerce: CustomFeature2IconECommerce,

    analyticsIconECommerce: AnalyticsIconECommerce,
    ecomsecureIconECommerce: EcomsecureIconECommerce,
    // fastScalableIconECommerce: FastScalableIconECommerce,
    fastScalableIconECommerce: FastScalableIconECommerce,
    responsiveIntuitiveIconECommerce: ResponsiveIntuitiveIconECommerce,

    // ManualTestingIcon-----------

    manualTestingIcon: ManualTestingIcon,
    automatedTestingIcon: AutomatedTestingIcon,
    performanceTestingIcon: PerformanceTestingIcon,
    securityTesting: SecurityTesting,
    usabilityTestingIcon: UsabilityTestingIcon,
    regressionTestingIcon: RegressionTestingIcon,
    compatibilityTestingIcon: CompatibilityTestingIcon,
    apiTestingIcon: ApiTestingIcon,
    loadTestingIcon: LoadTestingIcon,

    comprehensiveIcon: ComprehensiveIcon,
    automationTestingIcon: AutomationTestingIcon,
    bugTestingIcon: BugTestingIcon,
    supportTestingIcon: SupportTestingIcon,

    // seo-----------------------
    keywordResearchSeoIcon: KeywordResearchSeoIcon,
    onpageSeoIcon: OnpageSeoIcon,
    technicalSeoIcon: TechnicalSeoIcon,
    backlinksSeoIcon: BacklinksSeoIcon,
    localSeoIcon: LocalSeoIcon,
    contentSeoIcon: ContentSeoIcon,
    ecommerceSeoIcon: EcommerceSeoIcon,
    auditSeoIcon: AuditSeoIcon,
    analyticsSeoIcon: AnalyticsSeoIcon,

    resultsSeoIcon: ResultsSeoIcon,
    expertiseSeoIcon: ExpertiseSeoIcon,
    customizedSeoIcon: CustomizedSeoIcon,
    reportingSeoIcon: ReportingSeoIcon,

    // UiuxIcon--------------

    researchUiuxIcon: ResearchUiuxIcon,
    wireframeUiuxIcon: WireframeUiuxIcon,
    uiDesignUiuxIcon: UiDesignUiuxIcon,
    uxStrategyUiuxIcon: UxStrategyUiuxIcon,
    designSystemUiuxIcon: DesignSystemUiuxIcon,
    motionUiuxIcon: MotionUiuxIcon,
    responsiveUiuxIcon: ResponsiveUiuxIcon,
    testingUiuxIcon: TestingUiuxIcon,
    accessibilityUiuxIcon: AccessibilityUiuxIcon,

    consultIcon: ConsultIcon,
    uiuxAppDevIcon: UiuxAppDevIcon,
    webDevIcon: WebDevIcon,
    deploymentIcon: DeploymentIcon,
    complianceIcon: ComplianceIcon,
    customIcon: CustomIcon,
    speedIcon: SpeedIcon,
    customScalableIcon: CustomScalableIcon,
    architectureIcon: ArchitectureIcon,
    uxIcon: UxIcon,
    supportSoftwareDevIcon: SupportSoftwareDevIcon,
    customWebDevIcon: CustomWebDevIcon,
    webappIcon: WebappIcon,
    backendIcon: BackendIcon,
    performanceIcon: PerformanceIcon,
    customResponsiveIcon: CustomResponsiveIcon,
    seoSpeedIcon: SeoSpeedIcon,
    secureScalableIcon: SecureScalableIcon,
    integrationWebDevIcon: IntegrationWebDevIcon,

    disasterRecoveryIcon: DisasterRecoveryIcon,
    cicdpipelineIcon: CicdpipelineIcon,
    cloudInfrastructureIcon: CloudInfrastructureIcon,
    configurationIcon: ConfigurationIcon,
    containerIcon: ContainerIcon,
    monitoringIcon: MonitoringIcon,
    securityDevopsIcon: SecurityDevopsIcon,
    infrastructureIcon: InfrastructureIcon,
    performanceDevopsIcon: PerformanceDevopsIcon,
    continuousMonitoringIcon: ContinuousMonitoringIcon,
    scalabilityIcon: ScalabilityIcon,
    securityIconDevops: SecurityIconDevops,

    // mvp--------------

    discoveryIcon: DiscoveryIcon,
    uiuxMvpIcon: UiuxMvpIcon,
    agileIcon: AgileIcon,
    apiIcon: ApiIcon,
    prototypeIcon: PrototypeIcon,
    usabilityIcon: UsabilityIcon,
    deploymentMvpDevIcon: DeploymentMvpDevIcon,
    postLaunchIcon: PostLaunchIcon,

    timetomarketIcon: TimetomarketIcon,
    costEffectiveIcon: CostEffectiveIcon,
    riskReductionIcon: RiskReductionIcon,

    customIntuitiveUiuxIcon: CustomIntuitiveUiuxIcon,
    pixelPerfectUiuxIcon: PixelPerfectUiuxIcon,
    dataDrivenUiuxIcon: DataDrivenUiuxIcon,
    scaleAdaptUiuxIcon: ScaleAdaptUiuxIcon,

    contactIcon: ContactIcon,
    messageIcon: MessageIcon,
    facebookIcon: FacebookIcon,
    instagramIcon: InstagramIcon,
    linkedinIcon: LinkedinIcon,
    upArrow: UpArrow,

    expertTeamIcon: ExpertTeamIcon,
    onTimeIcon: OnTimeIcon,
    supportIcon: SupportIcon,
    saveCostIcon: SaveCostIcon,

    clientSupportIcon: ClientSupportIcon,
    professionalsIcon: ProfessionalsIcon,
    superiorIcon: SuperiorIcon,
    innovativeIcon: InnovativeIcon,

    appDevelopmentIcon: AppDevelopmentIcon,
    eCommerceIconSolution: ECommerceIconSolution,
    webDevelopmentIcon: WebDevelopmentIcon,
    mVPDevelopmentIcon: MVPDevelopmentIcon,
    uXDesignIcon: UXDesignIcon,
    softwareSolutionIcon: SoftwareSolutionIcon,
    devopsSolutionIcon: DevopsSolutionIcon,
    qAandTestingIcon: QAandTestingIcon,
    seoIcon: SeoIcon,

    uiuxIconBestAtApp: UiuxIconBestAtApp,
    scalableSolutions: ScalableSolutions,
    technologiesIcon: TechnologiesIcon,
    agileDevelopment: AgileDevelopment,
    crossPlatform: CrossPlatform,
    support: Support,

    // caseStudy -----------
    acelanBlogSvg: AcelanBlogSvg,
    laMercerLogo: LaMercerLogo,
    acelanBlogSvg2: AcelanBlogSvg2,
    catSvgCaseStudy: CatSvgCaseStudy,
};
// export const getIconByKey = (key: keyof typeof Icons | string) => Icons[key as keyof typeof Icons];

import { FC } from 'react';
import { CSSProperties } from 'react';
import UiuxIconBestAtApp from './UiuxIconBestAtApp';
import ScalableSolutions from './ScalableSolutions';
import TechnologiesIcon from './TechnologiesIcon';
import AgileDevelopment from './AgileDevelopment';
import CrossPlatform from './CrossPlatform';
import Support from './Support';
import AcelanBlogSvg from './AcelanBlogSvg';
import LaMercerLogo from './LaMercerLogo';
import AcelanBlogSvg2 from './AcelanBlogSvg2';
import CatSvgCaseStudy from './CatSvgCaseStudy';

export type IconProps = {
    width?: number | string;
    height?: number | string;
    fill?: string;
    className?: string;
    bgColor?: string;
    iconColor?: string;
    size?: number;
    style?: CSSProperties;
    [key: string]: unknown;
};
type IconComponent = FC<IconProps>;

export const getIconByKey = (key: string): IconComponent => {
    return Icons[key] || (() => null);
};

export default Icons;
