import React from 'react';

const graphql = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="42"
            height="50"
            viewBox="0 0 42 50"
            fill="none"
        >
            <path
                d="M4.29602 36.695L2.61133 35.7204L21.3789 3.15137L23.0636 4.12593L4.29602 36.695Z"
                fill="#E535AB"
            />
            <path d="M2.22656 33.1182H39.7617V35.0673H2.22656V33.1182Z" fill="#E535AB" />
            <path
                d="M21.7453 45.0745L2.9707 34.2135L3.94335 32.5255L22.718 43.3865L21.7453 45.0745ZM38.0548 16.7678L19.2802 5.90673L20.2528 4.21875L39.0274 15.0798L38.0548 16.7678Z"
                fill="#E535AB"
            />
            <path
                d="M3.94727 16.7599L2.97461 15.072L21.7492 4.21094L22.7219 5.8988L3.94727 16.7599Z"
                fill="#E535AB"
            />
            <path
                d="M37.7072 36.695L18.9396 4.12593L20.6243 3.15137L39.3919 35.7204L37.7072 36.695ZM3.71484 13.7796H5.66016V35.5017H3.71484V13.7796Z"
                fill="#E535AB"
            />
            <path d="M36.3398 13.7793H38.2852V35.5014H36.3398V13.7793Z" fill="#E535AB" />
            <path
                d="M21.4141 44.2488L20.5645 42.7743L36.8934 33.3281L37.743 34.8025L21.4141 44.2488Z"
                fill="#E535AB"
            />
            <path
                d="M40.8625 36.1357C39.7375 38.0966 37.2297 38.7658 35.2726 37.6386C33.3156 36.5114 32.6476 33.9987 33.7726 32.0379C34.8976 30.077 37.4055 29.4077 39.3625 30.5349C41.3312 31.6739 41.9992 34.1748 40.8625 36.1357ZM8.21407 17.2433C7.08907 19.2042 4.58125 19.8735 2.62422 18.7463C0.667196 17.6191 -0.000772417 15.1064 1.12422 13.1455C2.24922 11.1846 4.75704 10.5154 6.71407 11.6426C8.6711 12.7815 9.33907 15.2825 8.21407 17.2433ZM1.13594 36.1357C0.0109463 34.1748 0.678914 31.6739 2.63594 30.5349C4.59297 29.4077 7.08908 30.077 8.22579 32.0379C9.35079 33.9987 8.68282 36.4997 6.72579 37.6386C4.75704 38.7658 2.26094 38.0966 1.13594 36.1357ZM33.7844 17.2433C32.6594 15.2825 33.3273 12.7815 35.2844 11.6426C37.2414 10.5154 39.7375 11.1846 40.8742 13.1455C41.9992 15.1064 41.3312 17.6073 39.3742 18.7463C37.4172 19.8735 34.9094 19.2042 33.7844 17.2433ZM20.9992 47.6308C18.7375 47.6308 16.9094 45.7991 16.9094 43.533C16.9094 41.2668 18.7375 39.4351 20.9992 39.4351C23.2609 39.4351 25.089 41.2668 25.089 43.533C25.089 45.7874 23.2609 47.6308 20.9992 47.6308ZM20.9992 9.84608C18.7375 9.84608 16.9094 8.01438 16.9094 5.74824C16.9094 3.48209 18.7375 1.65039 20.9992 1.65039C23.2609 1.65039 25.089 3.48209 25.089 5.74824C25.089 8.01438 23.2609 9.84608 20.9992 9.84608Z"
                fill="#E535AB"
            />
        </svg>
    );
};

export default graphql;
