import { IconProps } from '../../types/icon';

export function HamburgerIcon({ width, height, fill, className = '' }: IconProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 30 30"
            fill={fill}
            className={className}
        >
            <path
                d="M5.84766 6.29297C5.51367 6.41016 5.34961 6.53906 5.16797 6.80859C5.02734 7.01953 5.00977 7.10742 5.00977 7.50586C5.00977 7.91602 5.02734 7.98047 5.18555 8.21484C5.28516 8.34961 5.48438 8.53125 5.625 8.61328L5.88867 8.75977H15H24.1113L24.375 8.61328C24.5156 8.53125 24.7148 8.34961 24.8145 8.21484C24.9727 7.98047 24.9902 7.91602 24.9902 7.5C24.9902 7.08398 24.9727 7.01953 24.8145 6.78516C24.7148 6.65039 24.5156 6.46875 24.375 6.38672L24.1113 6.24023L15.0879 6.22852C7.66406 6.2168 6.02344 6.22852 5.84766 6.29297Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M5.84766 13.793C5.51367 13.9102 5.34961 14.0391 5.16797 14.3086C5.02734 14.5195 5.00977 14.6074 5.00977 15.0059C5.00977 15.416 5.02734 15.4805 5.18555 15.7148C5.28516 15.8496 5.48438 16.0313 5.625 16.1133L5.88867 16.2598H15H24.1113L24.375 16.1133C24.5156 16.0313 24.7148 15.8496 24.8145 15.7148C24.9727 15.4805 24.9902 15.416 24.9902 15C24.9902 14.584 24.9727 14.5195 24.8145 14.2852C24.7148 14.1504 24.5156 13.9688 24.375 13.8867L24.1113 13.7402L15.0879 13.7285C7.66406 13.7168 6.02344 13.7285 5.84766 13.793Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M5.84766 21.293C5.51367 21.4102 5.34961 21.5391 5.16797 21.8086C5.02734 22.0195 5.00977 22.1074 5.00977 22.5059C5.00977 22.916 5.02734 22.9805 5.18555 23.2148C5.28516 23.3496 5.48438 23.5313 5.625 23.6133L5.88867 23.7598H15H24.1113L24.375 23.6133C24.5156 23.5313 24.7148 23.3496 24.8145 23.2148C24.9727 22.9805 24.9902 22.916 24.9902 22.5C24.9902 22.084 24.9727 22.0195 24.8145 21.7852C24.7148 21.6504 24.5156 21.4688 24.375 21.3867L24.1113 21.2402L15.0879 21.2285C7.66406 21.2168 6.02344 21.2285 5.84766 21.293Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
}
