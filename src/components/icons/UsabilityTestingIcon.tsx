import React from 'react';

const UsabilityTestingIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_1961_346385)">
                <path
                    d="M10.8699 1.45452C10.6258 2.00139 10.6648 3.00725 10.9285 3.27092C11.1727 3.52483 11.7098 3.52483 12.0809 3.29045C12.3152 3.1342 12.325 3.08538 12.2664 2.5678C12.1492 1.41545 11.9246 1.09319 11.2703 1.09319C11.0848 1.09319 10.9871 1.18108 10.8699 1.45452Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M18.2919 3.44665C18.0673 3.55408 17.7255 3.82751 17.5302 4.04236C17.2372 4.35486 17.1786 4.49158 17.2079 4.78454C17.2275 5.05798 17.3056 5.18494 17.5693 5.36072C18.1747 5.77087 18.4872 5.60486 19.0829 4.57947C19.3466 4.12048 19.3368 3.76892 19.0341 3.44665C18.7997 3.19275 18.7997 3.19275 18.2919 3.44665Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M3.93546 3.53423C3.35928 3.76861 3.5546 4.54009 4.35538 5.22369C4.94132 5.72173 5.49796 5.63384 5.76163 5.00884C5.95694 4.52056 5.82022 4.26666 5.14639 3.8272C4.53116 3.43658 4.31632 3.37798 3.93546 3.53423Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M10.5465 4.52045C7.80239 4.84271 5.50746 6.50287 4.55043 8.86615C3.83754 10.624 4.08168 13.3974 5.10707 14.9794C5.29262 15.2626 5.97621 16.1513 6.62075 16.9619C7.27504 17.7626 7.81215 18.5048 7.81215 18.5927C7.81215 18.6806 7.74379 19.0126 7.66567 19.3251C7.58754 19.6279 7.51918 20.165 7.51918 20.5068C7.51918 21.0829 7.53871 21.1513 7.85121 21.4638C8.05629 21.6689 8.2809 21.7958 8.43715 21.7958C8.66176 21.7958 8.69106 21.8349 8.69106 22.1572C8.69106 22.9579 9.41371 23.9638 10.2438 24.3154C10.4489 24.3935 10.9079 24.4619 11.3278 24.4716C11.943 24.4814 12.1481 24.4326 12.6168 24.2079C12.9293 24.0517 13.3297 23.749 13.525 23.5244C13.8864 23.1044 14.2086 22.2353 14.1012 21.9619C14.0524 21.8349 14.1012 21.7958 14.3161 21.7958C14.7262 21.7958 15.2243 21.454 15.3903 21.0634C15.4977 20.8095 15.5075 20.4677 15.4684 19.579C15.3805 18.1142 15.2536 18.3876 17.3825 15.6435C18.2418 14.5302 18.5836 13.6025 18.7497 11.9033C18.9645 9.70599 18.4079 8.17279 16.8454 6.60052C16.1032 5.85834 15.82 5.65326 15.0387 5.26263C14.5309 5.01849 13.8473 4.74506 13.525 4.66693C12.734 4.48138 11.4352 4.41302 10.5465 4.52045ZM12.7145 6.12201C14.1598 6.37592 15.2633 6.96185 16.1422 7.93842C17.0993 8.98334 17.4801 9.9892 17.4801 11.3954C17.4704 12.8994 17.1579 13.8076 15.7028 16.4247L15.1852 17.3525L14.2672 17.3232C13.3786 17.2939 13.359 17.2939 13.4176 17.079C13.4469 16.9619 13.5055 16.0634 13.5446 15.0771L13.6032 13.2997H13.8668C14.3063 13.2997 15.1071 13.0165 15.4293 12.7431C16.7672 11.6201 16.2204 9.17865 14.5309 8.80756C13.2516 8.51459 12.275 9.35443 12.0114 10.9658C11.9625 11.3076 11.9137 11.6201 11.9137 11.6591C11.9137 11.7079 11.6989 11.7372 11.4254 11.7372H10.9372V11.3173C10.9372 10.7607 10.7223 10.0576 10.4 9.56927C10.0973 9.12006 9.51137 8.81732 8.92543 8.80756C8.37856 8.80756 7.59731 9.21771 7.29457 9.66693C6.21059 11.2587 7.25551 13.2802 9.18911 13.2997H9.63832L9.70668 14.6865C9.74575 15.4579 9.79457 16.3759 9.83364 16.7372L9.89223 17.4013H8.9645H8.04653L7.82192 16.9326C7.70473 16.6786 7.29457 15.9462 6.93325 15.3017C6.05434 13.7685 5.8395 13.1337 5.7809 11.8349C5.70278 9.99896 6.0934 8.94427 7.28481 7.76263C8.06606 6.98138 9.18911 6.36615 10.1559 6.1806C10.4489 6.12201 10.7809 6.05365 10.8883 6.03412C11.2985 5.95599 11.9918 5.98529 12.7145 6.12201ZM9.38442 10.5165C9.48207 10.6728 9.59926 11.0439 9.64809 11.3369L9.74575 11.8544L9.26723 11.8154C8.36879 11.7275 7.98793 10.9755 8.54457 10.4091C8.85707 10.0966 9.16957 10.1357 9.38442 10.5165ZM14.3942 10.5849C14.736 11.0732 14.5016 11.5712 13.8864 11.6982C13.6032 11.7568 13.5739 11.7372 13.5739 11.5224C13.5739 11.249 13.7497 10.6728 13.8961 10.4872C14.0329 10.3115 14.2282 10.3408 14.3942 10.5849ZM12.0993 13.8076C12.2555 14.54 12.5778 17.2451 12.5289 17.3232C12.5094 17.372 12.0211 17.4013 11.4547 17.4013H10.4293L10.4879 16.7372C10.5661 15.9755 10.7028 14.8915 10.8493 13.954L10.9567 13.2997H11.4743H12.0016L12.0993 13.8076ZM13.3102 18.6415L13.9645 18.7001V19.4619V20.2333H11.4352C8.58364 20.2333 8.85707 20.3212 8.73989 19.4033C8.71059 19.1298 8.66176 18.8369 8.63246 18.7392C8.58364 18.5829 8.72036 18.5732 10.6149 18.5732C11.7282 18.5732 12.9391 18.6025 13.3102 18.6415ZM12.8903 22.0302C12.8903 22.2841 12.3434 22.7529 11.9039 22.8701C11.3082 23.0361 10.82 22.8505 10.3707 22.3036L10.0289 21.8935H11.4645C12.6168 21.8935 12.8903 21.9228 12.8903 22.0302Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M37.5883 15.0967C37.5688 15.126 35.6743 15.1553 33.3989 15.1651C27.9692 15.1944 25.518 15.2822 23.4575 15.5361C22.0903 15.7022 21.7387 15.7803 21.5825 15.9365C21.3872 16.1319 21.3872 16.1319 21.6118 16.1807C22.1391 16.2979 26.6801 16.4346 33.4477 16.5322C37.4809 16.5811 40.8696 16.669 40.9965 16.7276C41.1137 16.7764 41.2504 16.9131 41.2993 17.0401C41.3481 17.167 41.4067 19.7158 41.4165 22.7041L41.4457 28.1436L41.1821 28.417C40.9282 28.6709 40.8598 28.6807 39.8637 28.71L38.8188 28.749L39.0727 28.4658C39.4243 28.0947 39.6 27.5381 39.4536 27.2647C39.3754 27.1182 39.2875 27.0791 39.1801 27.1182C39.0922 27.1572 38.8872 27.2354 38.7211 27.294C38.2915 27.4404 36.9145 28.3096 36.5532 28.6612C36.3188 28.876 36.2309 29.0518 36.2309 29.2764C36.2309 29.6572 36.3872 29.833 37.2856 30.4776C38.4965 31.3369 39.3266 31.5908 39.561 31.1612C39.6977 30.8975 39.6 30.3799 39.3559 30.0674L39.1606 29.8233L40.3715 29.8428C41.5727 29.8526 41.5825 29.8526 41.9926 29.5596C42.2368 29.3936 42.5395 29.042 42.7153 28.7295L43.018 28.1924L43.0473 23.3096C43.0668 20.624 43.0375 18.0166 42.9887 17.5186C42.8715 16.4053 42.5786 15.8486 41.8559 15.3994L41.3774 15.1065L39.5122 15.0772C38.4868 15.0576 37.6176 15.0674 37.5883 15.0967Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M24.7072 20.1748C23.4865 20.5264 17.3342 23.5732 16.797 24.1006C16.6701 24.2275 16.6506 24.9502 16.6213 29.2178C16.6017 32.4111 16.631 34.2666 16.6896 34.3838C16.7482 34.4814 18.5842 35.5752 20.7717 36.8057C25.342 39.3838 25.3224 39.374 25.5666 39.374C25.6642 39.374 26.5334 38.9053 27.5002 38.3389C28.4572 37.7725 30.3029 36.6885 31.592 35.9365C32.881 35.1748 34.0236 34.4912 34.1213 34.4033C34.297 34.2568 34.3068 34.0713 34.2482 29.3545C34.2189 26.6689 34.1603 24.4033 34.131 24.3252C33.9943 23.9736 26.1525 20.0479 25.8302 20.165C25.7521 20.2041 25.6642 20.1846 25.6349 20.1357C25.5568 20.0088 25.2443 20.0186 24.7072 20.1748ZM27.4416 21.5811C27.9006 21.874 28.9748 22.5381 29.8342 23.0654C30.6935 23.5928 31.5627 24.1494 31.7677 24.2959L32.1486 24.5596L31.2697 25.0186C30.7912 25.2725 29.9416 25.79 29.3849 26.1611C28.8283 26.5322 27.9299 27.1279 27.383 27.4795C26.8459 27.8311 26.2892 28.2314 26.1623 28.3779L25.9181 28.6318L24.8537 28.0654C24.258 27.7529 23.4279 27.2939 22.9982 27.0498C21.3185 26.1025 19.7072 25.292 19.0822 25.0869L18.4377 24.8623L19.883 23.9346C20.6838 23.4268 21.7873 22.7236 22.3439 22.3721C22.9006 22.0303 23.8185 21.415 24.3849 21.0049L25.4299 20.2627L26.0256 20.6631C26.3478 20.8877 26.9924 21.2979 27.4416 21.5811ZM18.7795 26.083C19.0627 26.2881 19.9709 26.874 20.8009 27.3818C21.631 27.8994 22.842 28.6416 23.4865 29.042L24.6584 29.7744L24.717 32.792C24.756 34.4521 24.8342 36.1318 24.8927 36.5322C24.9513 36.9326 24.9806 37.2744 24.9611 37.2939C24.9318 37.333 18.4767 33.6611 18.34 33.5342C18.2619 33.4463 18.1252 25.7021 18.2131 25.7021C18.2424 25.7021 18.4963 25.8682 18.7795 26.083ZM32.9103 29.6865V33.4854L30.1076 35.0576C28.5549 35.9268 27.0119 36.7959 26.6701 36.9912C26.1623 37.2842 26.0549 37.3135 26.1134 37.167C26.2795 36.7666 26.3869 34.2275 26.3283 31.874C26.2892 29.9404 26.3088 29.4131 26.3967 29.4131C26.7873 29.4131 31.3381 26.9619 32.6662 26.0342C32.7736 25.9561 32.8713 25.8975 32.881 25.8975C32.9006 25.8975 32.9103 27.6064 32.9103 29.6865Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M11.5137 27.3725C11.3477 27.685 11.4746 28.2318 11.7871 28.5541L12.0508 28.8275L11.7188 28.7592C11.5332 28.7201 10.9766 28.6908 10.4883 28.6811C9.67773 28.6811 9.57031 28.7006 9.13086 28.974C8.80859 29.1791 8.57422 29.433 8.35938 29.8041L8.05664 30.3412V36.0053V41.6693L8.28125 42.1283C8.56445 42.7045 9.17969 43.2514 9.70703 43.3881C10.1562 43.515 20.8887 43.4272 24.2676 43.2807C26.7383 43.1635 29.2383 42.8607 29.4531 42.6459C29.541 42.558 29.5898 42.4604 29.5508 42.4311C29.3848 42.265 24.5117 42.1088 14.2676 41.9525C10.1855 41.8939 10.1172 41.8842 9.91211 41.6791C9.70703 41.474 9.69727 41.3764 9.63867 38.2025C9.59961 36.4154 9.57031 33.9447 9.57031 32.724V30.4877L9.84375 30.185C10.1074 29.892 10.1367 29.8822 11.1816 29.8334L12.2559 29.7748L12.002 30.058C11.5527 30.5561 11.3965 31.4643 11.7578 31.4643C12.1387 31.4643 13.9746 30.4096 14.5215 29.8822C14.9414 29.4818 14.9512 29.0619 14.5801 28.7104C14.0332 28.2123 12.8516 27.4408 12.4121 27.3041C11.7969 27.1186 11.6504 27.1283 11.5137 27.3725Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M38.4368 35.2725C38.0169 35.4092 37.7337 35.7217 37.4505 36.3857C37.2356 36.8838 37.1282 37.0205 36.8743 37.1084C36.7083 37.1768 36.3079 37.3818 35.9954 37.5674C35.4778 37.8799 35.3997 37.8994 35.0286 37.8115C34.3841 37.665 33.7005 37.6943 33.3782 37.8799C33.0462 38.085 32.2552 39.2861 32.1673 39.7451C32.0598 40.2627 32.2161 40.7021 32.6751 41.2783L33.1145 41.8154L33.1243 42.792L33.1341 43.7588L32.7239 44.2178C32.2845 44.7256 32.0794 45.2822 32.177 45.751C32.2649 46.1709 32.9485 47.3232 33.261 47.5674C33.6028 47.8408 34.218 47.8994 34.9016 47.7334C35.468 47.5967 35.4778 47.5967 35.8391 47.8506C36.0345 47.9971 36.4153 48.2217 36.6985 48.3486C37.1673 48.5732 37.2161 48.6221 37.4016 49.1885C37.7337 50.1943 38.2024 50.5068 39.3255 50.5068C40.6243 50.4971 41.0442 50.1846 41.4153 48.9639C41.5227 48.6025 41.6106 48.5049 42.0305 48.29C42.304 48.1533 42.7044 47.9189 42.929 47.7627C43.2805 47.5283 43.3684 47.499 43.7102 47.5771C44.6282 47.792 45.2923 47.665 45.6829 47.1865C45.9075 46.9131 46.3665 45.9658 46.386 45.7217C46.4348 45.126 46.3665 44.9404 45.9075 44.3838L45.429 43.8076L45.3997 42.7627L45.3704 41.7275L45.8098 41.1807C46.0442 40.8877 46.2688 40.5166 46.3079 40.3701C46.4153 39.999 46.2591 39.5205 45.8391 38.9053C45.2532 38.0654 44.843 37.9482 43.7786 38.3096L43.1829 38.5049L42.7141 38.2217C42.0598 37.8213 41.2981 37.5674 41.2005 37.7236C41.0345 37.9971 40.9173 37.665 40.9173 36.9229C40.9173 36.0049 40.7513 35.6045 40.302 35.4189C39.8821 35.2529 38.7884 35.165 38.4368 35.2725ZM40.4485 36.9717C40.6536 37.3428 40.7903 37.6748 40.7708 37.7236C40.7415 37.7627 41.0345 37.9873 41.4251 38.2119C41.8157 38.4365 42.304 38.7783 42.5091 38.9639L42.8704 39.3154L43.7591 39.1689L44.638 39.0127L44.9212 39.5107L45.2044 40.0088L44.843 40.3994C44.0618 41.2295 44.1106 41.0732 44.0911 42.7822L44.0716 44.3447L44.5891 44.9111L45.097 45.4775L44.8919 45.8193C44.7845 46.0049 44.6673 46.2002 44.6282 46.2393C44.5891 46.2783 44.2669 46.249 43.9055 46.1709C42.9876 45.9561 42.929 45.9658 42.2552 46.4443C41.9134 46.6885 41.4055 46.9717 41.1126 47.0889C40.4485 47.3428 40.302 47.5088 40.0384 48.3096L39.8235 48.9443H39.4036H38.9837L38.7591 48.29C38.6419 47.9189 38.4954 47.5674 38.4368 47.4893C38.3782 47.4209 38.0169 47.2061 37.6165 47.0303C37.2259 46.8545 36.718 46.5615 36.4934 46.376C35.9368 45.9268 35.8587 45.9072 35.0872 46.0732C34.2083 46.2588 34.2473 46.2686 34.0032 45.8291L33.7884 45.458L33.9934 45.2725C34.1106 45.1748 34.3352 44.9209 34.5013 44.7061L34.804 44.3252V42.7627L34.8138 41.2002L34.2962 40.624L33.7786 40.0479L34.0032 39.6572C34.1985 39.2959 34.2473 39.2666 34.5208 39.3252C34.677 39.3545 35.0774 39.4033 35.3997 39.4424C35.9856 39.4912 35.9856 39.4912 36.5227 39.0518C36.8157 38.8076 37.343 38.4658 37.6848 38.29L38.3098 37.958L38.5345 37.1963C38.6614 36.7861 38.7884 36.3955 38.8177 36.3467C38.8567 36.2881 39.1399 36.249 39.4817 36.2686L40.0872 36.2979L40.4485 36.9717Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M37.6357 39.9695C37.1865 40.0964 36.5419 40.7703 36.249 41.405C35.6923 42.6355 35.8974 44.0417 36.7763 45.0281C37.4013 45.7312 38.1044 46.0339 39.1787 46.0925C39.9306 46.1316 40.0771 46.1121 40.5849 45.8777C41.4443 45.4773 41.9326 45.0281 42.3037 44.2761C42.5869 43.7 42.6259 43.5339 42.6259 42.8406C42.6259 42.1863 42.5771 41.9714 42.3525 41.5027C41.8447 40.4578 41.0927 40.0183 39.9306 40.0671C39.5888 40.0867 39.3056 40.0476 39.2372 39.9792C39.1005 39.8425 38.0849 39.8425 37.6357 39.9695ZM39.5302 40.5359C39.6181 40.5847 39.8525 40.6921 40.0478 40.7703C40.4287 40.9363 41.0341 41.6003 41.2001 42.0398C41.4638 42.7332 41.2587 43.5339 40.6923 44.032C39.3056 45.2429 37.1669 44.0808 37.6064 42.3523C37.8017 41.571 38.0947 41.1414 38.6611 40.7898C39.2665 40.4187 39.2861 40.4089 39.5302 40.5359Z"
                    fill="var(--color-black-300)"
                />
            </g>
            <defs>
                <clipPath id="clip0_1961_346385">
                    <rect height="50" width="50" fill="white" transform="translate(0 0.799805)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default UsabilityTestingIcon;
