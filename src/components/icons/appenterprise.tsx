import React from 'react';

const appenterprise = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M24.4444 4.13963C24.1416 4.28611 24.1026 4.56932 24.2881 5.32127C24.4151 5.86814 24.4932 6.01463 24.7178 6.13182C25.2842 6.42479 25.5381 6.28807 25.7432 5.58494C25.8604 5.18455 25.7823 4.38377 25.6163 4.19822C25.4405 4.00291 24.7959 3.97361 24.4444 4.13963Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M16.0156 6.33691C15.7324 6.43457 15.332 6.89355 15.332 7.1084C15.332 7.32324 15.9863 8.18261 16.2891 8.3584C16.4746 8.46582 17.1973 8.36816 17.2949 8.21191C17.4609 7.92871 17.2168 7.08886 16.8164 6.60058C16.6406 6.37597 16.2598 6.25879 16.0156 6.33691Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M33.2324 6.49316C33.125 6.61035 32.959 6.98145 32.8711 7.31348C32.7148 7.88965 32.7148 7.93848 32.8711 8.17285C33.0371 8.42676 33.3008 8.6123 33.4863 8.6123C33.6816 8.6123 34.1992 8.0752 34.4238 7.62598C34.6973 7.09863 34.6484 6.77637 34.2578 6.53223C33.8379 6.26855 33.4473 6.25879 33.2324 6.49316Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.4433 10.0967C24.2285 10.3311 24.2187 10.3896 24.2187 12.3916V14.4424L23.3691 14.5107C22.8906 14.54 22.1582 14.6084 21.7285 14.6572C21.3086 14.7061 20.8984 14.7256 20.8398 14.6963C20.7714 14.6768 20.664 14.5205 20.6054 14.3643C20.332 13.7002 19.5507 13.3096 18.7695 13.4268C18.5351 13.4658 18.3398 13.4365 18.2226 13.3486C17.9199 13.124 17.1777 13.1631 16.7871 13.4268C15.3808 14.374 15.5761 16.7275 17.1191 17.5186C17.6953 17.8018 18.6328 17.8604 19.2089 17.6455C19.7851 17.4209 20.3906 16.874 20.6152 16.3662L20.791 15.9756L21.5527 16.0537C21.9726 16.1025 22.7441 16.1318 23.2714 16.1123L24.2187 16.083V18.9248V21.7666L23.6132 21.6689C22.6074 21.5127 18.0859 21.5322 17.6269 21.6885C17.2753 21.8154 17.2363 21.8057 17.1386 21.6494C16.7578 20.9854 16.2011 20.6924 15.5761 20.8193C15.3808 20.8584 15.2246 20.8291 15.0976 20.7314C14.9902 20.6338 14.7461 20.5752 14.4336 20.585C13.9843 20.585 13.9062 20.624 13.5644 20.9854C12.9394 21.6592 12.8222 22.5088 13.2421 23.3486C13.5546 23.9639 14.0039 24.2959 14.7558 24.4424C15.5664 24.6182 16.0351 24.4521 16.7187 23.7393C17.0312 23.4268 17.3339 23.1533 17.4023 23.1533C17.4707 23.1436 17.9199 23.1533 18.3886 23.1631C19.2871 23.1826 22.5488 23.0068 23.6132 22.8799L24.2187 22.8115V25.4189C24.2187 26.8447 24.248 28.1631 24.2773 28.3291L24.3457 28.6416L23.9941 28.583C23.7988 28.5439 22.998 28.5928 22.1972 28.6807C21.4062 28.7783 20.7519 28.8174 20.7324 28.7686C20.6152 28.4561 20.2246 27.8896 19.9902 27.7139C19.7753 27.5576 19.5605 27.5088 19.0625 27.5088C18.7011 27.499 18.3007 27.4404 18.1738 27.3721C17.8222 27.1768 17.0703 27.2842 16.6601 27.5869C15.9668 28.124 15.6347 29.3838 15.9668 30.2529C16.3476 31.249 17.2363 31.8545 18.3398 31.8545C18.6718 31.8545 19.1015 31.7959 19.2871 31.7178C19.7461 31.5225 20.4785 30.8193 20.6152 30.4385L20.7324 30.1162L22.5293 30.1357C24.2382 30.1455 24.3261 30.1553 24.3652 30.3408C24.3847 30.4482 24.4336 31.6104 24.4628 32.9287C24.5019 34.2471 24.5605 35.6143 24.5996 35.9756L24.6679 36.6299L22.1191 36.6592C19.248 36.6885 19.2578 36.6885 18.5742 37.4697C17.8808 38.2607 17.7929 39.3252 18.3691 40.1846C18.5253 40.4287 18.789 40.6729 18.9453 40.7412L19.2382 40.8584L18.9941 41.1514C18.6816 41.5127 18.6132 41.7861 18.6718 42.4502C18.7597 43.4658 19.5605 44.2568 20.5859 44.335L21.1425 44.374L21.1523 44.7939C21.1816 45.6729 22.0507 46.7666 23.125 47.2646C23.623 47.499 23.7988 47.5283 24.7558 47.5283C25.6543 47.5283 25.9179 47.4893 26.3964 47.3037C27.1582 46.9912 27.705 46.5518 28.164 45.8877C28.4863 45.4287 28.5546 45.2236 28.5937 44.7549L28.6328 44.1787L29.1894 44.1396C29.9414 44.0908 30.4589 43.8076 30.8007 43.251C31.1523 42.6748 31.2304 42.0596 31.0058 41.6201L30.8398 41.2881L31.1132 41.0928C32.4121 40.1553 32.6171 38.5537 31.5918 37.3428L31.3281 37.0205L31.5918 36.2686C31.748 35.8584 31.8945 35.4678 31.9336 35.4189C31.9726 35.3604 32.1484 34.9893 32.3144 34.5791C32.6464 33.7783 34.0722 31.1611 34.5703 30.4385C35.1074 29.6572 36.4062 27.04 36.748 26.0439C36.9336 25.5068 37.1386 24.7549 37.2168 24.3838C37.4121 23.417 37.3828 20.9951 37.1777 19.9209C36.0546 14.2471 31.4648 10.2334 25.7324 9.9209C24.6875 9.8623 24.6484 9.8623 24.4433 10.0967ZM27.4902 11.7373C28.7597 12.001 30.6152 12.8799 31.7871 13.7783C34.58 15.917 36.1816 19.4131 36.0058 22.9678C35.9179 24.7549 35.4785 26.1025 34.1211 28.7002C32.9394 30.9658 32.4902 31.8838 32.0996 32.8311C31.7285 33.7393 31.0058 35.9463 30.9082 36.4834C30.8496 36.7959 30.8203 36.8154 30.5957 36.7275C30.4687 36.6787 29.1894 36.6396 27.7636 36.6396H25.1757L25.2343 36.3271C25.2636 36.1514 25.332 35.5166 25.3906 34.9307C25.4394 34.335 25.5371 33.3291 25.5957 32.6846C25.7324 31.1514 25.8593 21.0146 25.8007 15.6826L25.7519 11.4736L26.3281 11.542C26.6503 11.5811 27.1679 11.6689 27.4902 11.7373ZM18.7793 14.1299C19.5898 14.6865 19.6777 15.7021 18.9453 16.083C18.2519 16.4443 17.4804 16.0342 17.4804 15.3018C17.4804 14.8232 17.6953 14.3545 18.0273 14.1006C18.3789 13.8369 18.3398 13.8369 18.7793 14.1299ZM15.5273 21.5029C15.664 21.5029 15.9179 22.04 15.9179 22.3135C15.9179 22.8213 15.1757 23.085 14.8046 22.7139C14.5214 22.4307 14.6093 21.9131 15.0097 21.4443C15.1562 21.2783 15.205 21.2686 15.3125 21.376C15.3808 21.4443 15.4785 21.5029 15.5273 21.5029ZM18.75 28.1924C19.58 28.7197 19.6875 29.7646 18.9453 30.1455C18.0078 30.6338 17.1972 29.8037 17.5781 28.7588C17.666 28.5439 18.2031 27.9678 18.3398 27.9482C18.3496 27.9482 18.5351 28.0557 18.75 28.1924ZM30.4003 38.3975C30.8984 38.7979 30.8105 39.54 30.2246 39.8721C29.9707 40.0088 29.4726 40.0479 26.8066 40.1064C24.3261 40.1553 23.3691 40.2139 22.1679 40.3896C21.2109 40.5361 20.6152 40.585 20.5371 40.5264C20.4687 40.4775 20.3125 40.4482 20.1757 40.4678C19.873 40.5068 19.3847 40.0576 19.2285 39.5986C19.1406 39.3252 19.1601 39.208 19.3261 38.8467C19.4433 38.6123 19.6289 38.3877 19.7363 38.3389C19.8437 38.29 22.2265 38.2412 25.039 38.2217C30.0195 38.2021 30.1562 38.2119 30.4003 38.3975ZM24.0722 41.3271C25.0976 41.3857 26.7968 41.4248 27.8613 41.4248H29.7851L29.8925 41.6592C30.0293 41.9424 29.9414 42.3428 29.707 42.5771C29.541 42.7432 29.2382 42.7529 25.0878 42.7432C20.4687 42.7236 20.3418 42.7139 20.1171 42.3037C19.9609 42.001 20 41.7568 20.2832 41.4248L20.5371 41.1123L21.3769 41.1709C21.8359 41.21 23.0566 41.2783 24.0722 41.3271ZM27.5683 44.4717C27.207 45.1162 26.5429 45.6924 25.9863 45.8584C24.7168 46.2295 23.7402 45.9561 22.8711 44.9795C22.5488 44.6279 22.4023 44.3936 22.4804 44.374C22.5586 44.3643 23.623 44.3154 24.8535 44.2666C26.0937 44.2275 27.2461 44.1787 27.4218 44.1787L27.7441 44.1592L27.5683 44.4717Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M9.44405 12.7822C9.07295 12.8799 8.81905 13.9248 9.11201 14.1592C9.2585 14.2764 10.2644 14.5693 10.5378 14.5693C10.655 14.5693 10.8601 14.4619 10.9968 14.3154C11.1921 14.1299 11.2312 14.0127 11.1823 13.8076C11.0944 13.4463 10.7722 13.124 10.2644 12.8994C9.83467 12.7139 9.75655 12.7041 9.44405 12.7822Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M39.3745 13.2314C38.8374 13.7783 38.8081 13.8662 38.9741 14.3544C39.0913 14.6962 39.3257 14.7744 39.8432 14.6181C40.9663 14.2861 41.3179 13.6709 40.7612 13.0166C40.3608 12.538 39.9995 12.5966 39.3745 13.2314Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M7.17781 21.5713C6.65047 21.708 6.48446 22.2842 6.78719 22.8701C6.95321 23.1924 7.18758 23.2314 7.81258 23.0752C7.99813 23.0263 8.27156 22.958 8.40828 22.9287C8.8868 22.8213 9.05281 22.0107 8.65242 21.747C8.40828 21.581 7.51961 21.4736 7.17781 21.5713Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M41.8848 21.6689C41.2988 21.8252 41.1133 22.0302 41.1133 22.5576C41.1133 22.9287 41.4258 23.0849 42.2461 23.1435C42.9102 23.1826 42.9492 23.1728 43.1445 22.9189C43.3789 22.6162 43.4082 22.3623 43.252 21.913C43.1055 21.4736 42.8223 21.4248 41.8848 21.6689Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M9.6965 30.2432C9.42306 30.3897 9.13986 30.5654 9.0715 30.6436C8.88595 30.8682 8.94454 31.3662 9.19845 31.6299C9.68673 32.1572 9.90158 32.1377 10.5852 31.4834C10.9465 31.1318 11.0344 30.9951 11.0344 30.7119C11.0344 29.9502 10.5949 29.794 9.6965 30.2432Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M39.0229 30.3409C38.7007 30.6339 38.7007 30.878 39.0229 31.2686C39.4526 31.7764 40.146 32.0596 40.5757 31.8936C40.9565 31.7471 41.1616 30.8292 40.8589 30.5753C40.6831 30.4288 39.6968 30.0967 39.4429 30.0967C39.355 30.0967 39.1694 30.2042 39.0229 30.3409Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default appenterprise;
