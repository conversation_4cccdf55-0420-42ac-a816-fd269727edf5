import React from 'react';

const SecurityIconDevops = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M27.5633 5.21722C25.6602 5.37034 22.8602 5.99378 21.2633 6.60628C17.6321 8.00628 15.2477 9.69065 13.3336 12.1953C12.6774 13.0594 12.6774 13.0703 12.7758 13.3438C12.8633 13.5735 12.918 13.5407 13.793 12.775C16.1117 10.7188 19.218 8.87034 21.843 7.9844C23.9758 7.27347 26.5133 6.89065 29.1383 6.89065C32.4852 6.89065 35.7774 7.71097 38.8289 9.31878C40.3055 10.0953 41.9242 11.0469 42.3617 11.3969L42.668 11.6485L41.7602 11.5828C40.2836 11.4844 40.0102 11.7469 40.7539 12.5672C41.1805 13.0485 42.0336 13.3875 43.8055 13.7813C45.1399 14.0766 45.6649 14.0657 45.8727 13.7594C45.9711 13.6282 46.0477 13.4313 46.0477 13.3328C46.0477 13.2344 45.7742 12.3157 45.4461 11.2985C44.7789 9.24222 44.3086 8.4219 43.7727 8.4219H43.4992L43.5758 9.31878C43.6086 9.8219 43.6742 10.3688 43.7071 10.5547C43.7836 10.861 43.718 10.8282 42.9633 10.1063C41.443 8.66253 38.5883 7.03284 36.2149 6.24534C33.7321 5.41409 30.2211 4.99847 27.5633 5.21722Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M28 10.325C27.5516 10.5219 26.3156 11.1125 25.2656 11.6484C24.2156 12.1734 21.5797 13.4969 19.4141 14.5688C17.2484 15.6516 15.3891 16.6141 15.2906 16.7016C15.1047 16.8656 15.0938 17.3687 15.0938 24.1719C15.0938 32.1344 15.1156 32.4078 15.7281 34.2344C16.7344 37.1875 18.2875 39.0141 22.0391 41.6719C27.825 45.7625 28.5688 46.2656 28.9078 46.2656C29.1375 46.2656 29.5641 46.0359 30.3406 45.4891C30.9531 45.0516 32.8891 43.6953 34.6609 42.4484C36.5969 41.1031 38.2594 39.8453 38.8172 39.3203C41.125 37.1328 42.4375 34.4641 42.7766 31.2484C42.9188 29.9359 42.8531 17.1609 42.7109 16.8109C42.6672 16.7016 42.1531 16.3953 41.5734 16.1219C40.9938 15.8375 38.6094 14.6672 36.2578 13.5078C32.3531 11.5719 29.3016 10.1719 28.9844 10.1719C28.9188 10.1719 28.8969 10.1172 28.9297 10.0625C29.0281 9.89844 28.8969 9.93125 28 10.325ZM29.6516 10.85C30.5266 11.5609 35.7 14.7109 39.2109 16.6578L41.0703 17.6859L41.1031 23.6359C41.125 27.1141 41.0813 30.1109 41.0156 30.8547C40.8844 32.3531 40.5125 33.7312 39.9219 34.9125C38.8172 37.1219 38.0516 37.8547 33.3813 41.1906C31.2922 42.6781 29.4328 44.0125 29.2469 44.1547L28.9188 44.4062L27.7156 43.5641C27.0594 43.1047 25.3422 41.9234 23.8984 40.9281C20.8578 38.8391 20.5297 38.5875 19.6 37.625C17.9594 35.9406 16.9313 33.7641 16.6359 31.3359C16.5813 30.8547 16.5156 27.5625 16.4938 24.0188L16.4609 17.5875L18.2656 16.5703C22.1266 14.4047 28.175 10.7953 28.4703 10.4781C28.8094 10.1172 28.7109 10.0844 29.6516 10.85Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M28.3281 13.7054C28.0656 13.8148 21.5906 16.9867 19.1625 18.1789C18.5391 18.4851 17.9594 18.8461 17.8719 18.9664C17.7406 19.1523 17.7188 20.1148 17.7188 24.9929C17.7188 30.221 17.7406 30.8882 17.9266 31.7742C18.3531 33.8304 19.3266 35.6132 20.8031 37.0351C21.2406 37.4617 22.7828 38.6429 24.2266 39.671C28.525 42.7117 28.6562 42.7992 29.0062 42.7226C29.2906 42.6679 33.3156 39.9226 35.7984 38.0851C37.6031 36.7507 38.9812 34.957 39.5938 33.1086C40.1844 31.3586 40.1953 31.2273 40.1844 25.1023C40.1844 21.9742 40.1406 19.2726 40.0969 19.1085C40.0312 18.8461 39.8562 18.7148 38.9594 18.2992C38.3797 18.0257 36.4984 17.1179 34.7922 16.2757C33.0859 15.4445 31.1391 14.5367 30.4719 14.2632C29.8047 14.0007 29.1703 13.7273 29.0719 13.6726C28.8313 13.5414 28.6781 13.5414 28.3281 13.7054ZM30.7016 15.182C32.025 16.0789 35.7437 18.332 37.1219 19.0867L38.5219 19.8523L38.4672 25.2664C38.4016 31.0632 38.3687 31.4023 37.8109 32.8132C37.0016 34.8148 36.1266 35.7007 32.0578 38.5992C29.6734 40.3054 28.9187 40.7976 28.7766 40.721C28.6672 40.6773 27.2562 39.7257 25.6375 38.621C21.8969 36.0507 20.9781 35.1867 20.1469 33.4586C19.2281 31.5445 19.2391 31.6539 19.1406 25.3211C19.0969 22.2476 19.0641 19.7429 19.075 19.732C19.6984 19.4914 28.4922 14.1867 28.4266 14.0882C28.4047 14.0445 28.4812 13.9351 28.6016 13.8585C28.7875 13.7054 28.8641 13.7382 29.4219 14.2195C29.75 14.5039 30.3297 14.9414 30.7016 15.182Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M34.7266 24.2703C33.7094 24.7516 31.2375 27.1359 28.4266 30.3297L28.0438 30.7781L27.0484 29.9031C25.6375 28.6562 24.1719 27.9344 24.1719 28.4813C24.1719 28.6234 24.4672 29.0828 24.675 29.2578C24.7406 29.3125 25.0688 29.7062 25.3969 30.1328C26.3484 31.325 27.7047 32.5938 28.0438 32.5938C28.2406 32.5938 28.6016 32.3531 29.2469 31.7734C30.45 30.7016 34.5188 26.5234 34.8359 26.0312C34.9672 25.8234 35.2625 25.4297 35.4813 25.1672C35.8859 24.6859 36.0609 24.2703 35.9297 24.0734C35.8094 23.8766 35.4156 23.9422 34.7266 24.2703Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M7.56956 20.4202C7.44925 20.5296 6.83675 21.1858 6.22425 21.8749C5.11956 23.1327 4.55081 24.0733 4.61643 24.5436C4.66018 24.8718 5.043 24.8827 5.41487 24.5546C5.60081 24.4015 5.77581 24.2811 5.81956 24.2811C5.87425 24.2811 6.10393 24.1061 6.34456 23.8874C6.58518 23.6686 6.78206 23.5046 6.78206 23.5265C6.78206 23.5483 6.7055 23.8108 6.618 24.1061C6.37737 24.8936 6.12581 26.9718 6.12581 28.153C6.12581 35.328 9.97581 42.3608 16.1664 46.4843C17.4461 47.3374 19.1742 48.2233 20.2352 48.5515C22.2914 49.1968 24.7633 49.4155 24.4571 48.9124C24.4242 48.8468 23.8883 48.639 23.2649 48.4421C19.9836 47.403 16.4071 45.1936 14.0992 42.7874C10.7414 39.2874 8.64143 35.0218 8.03987 30.428C7.87581 29.1593 7.79925 24.7186 7.94143 24.3468C7.99612 24.2046 8.11643 24.3468 8.42268 24.9483C8.893 25.8561 9.14456 26.0968 9.42893 25.878C9.77893 25.6265 9.91018 24.828 9.74612 23.8874C9.56018 22.7499 8.893 20.7046 8.6305 20.4421C8.368 20.1796 7.86487 20.1686 7.56956 20.4202Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M49.3609 21.4487C49.3937 21.6565 49.525 22.2909 49.6562 22.8597C49.9953 24.4237 50.1484 27.3768 49.9953 29.2034C49.7219 32.2659 49.0875 34.3987 47.5234 37.5159C46.0578 40.4253 43.5094 43.3675 40.8953 45.1393C39.55 46.0472 37.2859 47.3925 36.5969 47.6878L36.1047 47.8956L36.5969 47.119C36.8703 46.6925 37.1 46.2222 37.1109 46.0581C37.1328 45.7956 37.0891 45.7737 36.6734 45.7409C36.05 45.6972 35.6125 45.9159 34.7484 46.7034C33.5562 47.7972 32.5938 48.9565 32.5938 49.3175C32.5938 49.6784 32.9547 50.0503 33.4141 50.1378C33.6 50.1706 34.4641 50.3456 35.35 50.5315C36.7719 50.8378 37.0234 50.8597 37.5812 50.7612C38.3906 50.6081 38.6641 50.2581 38.15 50.0175C37.8984 49.8972 36.8047 49.4378 36.3672 49.2737C36.1812 49.1972 36.225 49.1753 36.5859 49.0987C38.1609 48.8034 41.0922 47.3159 42.9297 45.8831C43.8922 45.1393 45.6422 43.4112 46.4078 42.4487C48.2125 40.1737 49.5797 37.5815 50.4656 34.7268C51.1984 32.3534 51.3516 31.2268 51.3516 28.1643C51.3516 25.3315 51.2531 24.7518 50.4109 22.5862C49.9078 21.2847 49.8969 21.2737 49.5578 21.1643C49.3062 21.0878 49.2953 21.0987 49.3609 21.4487Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default SecurityIconDevops;
