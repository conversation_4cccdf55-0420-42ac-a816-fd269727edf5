import React from 'react';

const seemlessintegration = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
        >
            <path
                d="M42.416 4.06875C42.1425 4.27656 40.6331 11.2328 40.7863 11.5609C41.005 12.0531 41.8253 11.9766 41.9456 11.4516C42.2847 10.0625 43.4222 4.71406 43.4222 4.52812C43.4222 4.03594 42.8206 3.7625 42.416 4.06875Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M48.5406 7.88594C46.8672 9.1875 43.9688 12.5562 43.9688 13.2016C43.9688 13.6063 44.1437 13.7812 44.5703 13.7812C44.8766 13.7812 44.9969 13.6719 45.675 12.7641C46.4078 11.7797 48.4641 9.61406 49.4266 8.82656C49.8094 8.50938 49.9078 8.35625 49.9078 8.08281C49.9078 7.72188 49.6562 7.4375 49.3172 7.4375C49.2188 7.4375 48.8688 7.64531 48.5406 7.88594Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M27.727 7.84219C24.2052 8.14844 21.7552 9.15469 19.6333 11.1781C18.3208 12.4141 17.4677 13.65 16.8224 15.225L16.4724 16.0781H15.5317C12.2177 16.0781 9.10048 18.4844 8.38954 21.5906L8.24735 22.2031L7.20829 22.6734C4.16766 24.0406 3.13954 25.55 3.15048 28.6016C3.16141 31.2266 4.18954 33.5562 6.16923 35.4047C7.17548 36.3453 8.5536 37.0672 10.1177 37.4828C11.113 37.7453 11.4411 37.7672 13.8474 37.8219L16.4833 37.8656L16.5489 39.0906C16.6692 41.0266 17.0083 41.8031 17.9708 42.3719C18.7036 42.8094 19.688 42.9844 21.6567 43.05C22.652 43.0719 23.7239 43.1266 24.0411 43.1594L24.6099 43.2141V46.0578V48.8906H18.288H11.9661L11.8239 48.3766C11.627 47.6219 10.752 46.7797 9.96454 46.5938C8.09423 46.1453 6.4536 47.4141 6.4536 49.2953C6.46454 50.3672 7.00048 51.275 7.93016 51.7672C8.50985 52.0734 9.82235 52.0953 10.402 51.8109C10.9161 51.5484 11.452 51.0234 11.7145 50.5094L11.9224 50.0938H25.0802H38.227L38.6099 50.6078C38.8614 50.9578 39.2005 51.2422 39.5942 51.4391C40.1302 51.7125 40.2942 51.7453 41.027 51.7125C42.0661 51.6688 42.7552 51.2969 43.2583 50.4875C43.5536 49.9953 43.5864 49.8641 43.5864 49C43.5864 48.1359 43.5536 48.0047 43.2583 47.5125C42.7552 46.7031 42.0661 46.3313 41.0161 46.2875C40.1192 46.2438 39.5286 46.4297 38.9599 46.9656C38.6317 47.2609 38.2598 47.9609 38.1833 48.4531L38.1177 48.8359L31.9599 48.8688L25.813 48.8906V46.0688V43.2359L27.0489 43.1703C27.7161 43.1266 29.0177 43.0938 29.9145 43.0938C32.6161 43.0938 33.3927 42.8859 34.1911 41.9344C34.913 41.0813 35.0333 40.7531 35.088 39.6156L35.1317 38.6094H36.4442C43.0942 38.6094 47.8848 35.6672 49.6895 30.4828C50.0177 29.5203 50.0395 29.4109 50.0395 27.7266C50.0395 26.3594 49.9958 25.8125 49.8317 25.2219C49.3505 23.4281 48.5083 21.9188 47.2614 20.6391C45.9927 19.3266 44.4724 18.4625 41.6177 17.4562L40.0208 16.8984L39.6927 15.9141C39.113 14.175 38.2161 12.7531 36.7942 11.3422C34.7927 9.35156 32.3208 8.17031 29.5864 7.875C29.0505 7.82031 28.5036 7.77656 28.3833 7.7875C28.263 7.79844 27.9677 7.82031 27.727 7.84219ZM31.0083 9.40625C34.6833 10.4016 37.6145 13.2781 38.6645 16.8875C38.8177 17.4344 38.9599 17.8828 38.9817 17.9156C39.0036 17.9375 39.802 18.2109 40.7536 18.5391C43.8489 19.5781 45.1067 20.2672 46.463 21.7109C47.163 22.4438 47.4473 22.8594 47.8848 23.7563C48.1911 24.3797 48.5192 25.2219 48.6286 25.6484C48.8911 26.6766 48.8911 28.5797 48.6395 29.5312C48.1802 31.2484 47.0974 32.8344 45.6755 33.8406C43.5098 35.3828 40.2614 36.3672 36.8489 36.4984L35.1427 36.5641L35.0661 33.4688C34.9895 30.0453 34.9567 29.8813 34.2677 29.1812C33.5567 28.4484 33.6114 28.4594 26.2505 28.4047C22.6083 28.3828 19.4364 28.4047 19.1958 28.4594C18.9552 28.5141 18.5833 28.6781 18.3536 28.8203C17.8942 29.1266 17.0411 30.3406 16.7895 31.0625C16.5599 31.7188 16.4177 33.3703 16.4942 34.7375C16.538 35.7547 16.527 35.8422 16.3411 35.8969C16.0786 35.9625 13.3333 35.8094 12.1849 35.6562C10.2927 35.4156 8.96923 34.8797 7.74423 33.8625C5.99423 32.4188 4.94423 30.1438 5.0536 28.0547C5.08641 27.3219 5.16298 27.0703 5.45829 26.5016C6.21298 25.0906 7.66766 24.1172 9.24266 23.9641C9.6911 23.9203 10.1067 23.8438 10.1724 23.7781C10.227 23.7234 10.3474 23.2641 10.4349 22.7719C10.752 20.9563 11.638 19.6656 13.0599 18.9766C14.3833 18.3312 15.3349 18.2328 16.8661 18.6156C17.7739 18.8344 17.8395 18.8344 18.0364 18.6594C18.1567 18.55 18.2661 18.2438 18.3099 17.9156C18.5505 16.1109 19.6224 14 21.077 12.4688C22.6849 10.7625 24.4895 9.73438 26.688 9.24219C27.7052 9.0125 29.8708 9.08906 31.0083 9.40625ZM33.0974 29.9031C33.7645 30.2969 33.7427 30.1219 33.7755 35.4703L33.8083 40.3703L33.4364 40.7422C33.1739 41.0047 32.9333 41.125 32.5833 41.1797C32.3208 41.2125 29.2474 41.2344 25.7583 41.2125C19.9177 41.1797 19.3927 41.1688 19.1302 40.9828C18.5395 40.5781 18.5395 40.6109 18.5395 35.3719C18.5395 30.625 18.5395 30.5266 18.7692 30.2203C19.2286 29.6078 18.988 29.6297 26.1411 29.6625C32.3427 29.6953 32.7802 29.7172 33.0974 29.9031Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M49.6891 14.9735C48.5406 15.2578 47.5125 15.5532 47.425 15.6297C47.1516 15.8594 47.2062 16.3735 47.5234 16.5813C47.775 16.7453 47.9172 16.7235 50.225 16.1657C52.5328 15.6078 52.675 15.5641 52.8062 15.2797C52.9375 15.0172 52.9266 14.9297 52.7844 14.7C52.5328 14.3282 52.1828 14.361 49.6891 14.9735Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default seemlessintegration;
