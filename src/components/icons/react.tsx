import React from 'react';

const react = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="42"
            height="46"
            viewBox="0 0 42 46"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337763)">
                <path
                    d="M41.5 23.0037C41.5 20.3875 38.0987 17.9082 32.8838 16.3707C34.0872 11.2511 33.5524 7.17793 31.1957 5.87387C30.6525 5.56798 30.0173 5.42309 29.3237 5.42309V7.21818C29.7081 7.21818 30.0173 7.29063 30.2764 7.42747C31.413 8.05535 31.906 10.4461 31.5216 13.5211C31.4297 14.2778 31.2792 15.0747 31.0954 15.8877C29.4574 15.5014 27.669 15.2035 25.7886 15.0103C24.6604 13.5211 23.4904 12.1688 22.3121 10.9855C25.0365 8.54639 27.5938 7.21013 29.332 7.21013V5.41504C27.0338 5.41504 24.0253 6.99279 20.9833 9.7297C17.9413 7.00889 14.9327 5.44724 12.6345 5.44724V7.24233C14.3645 7.24233 16.9301 8.57054 19.6545 10.9935C18.4845 12.1768 17.3145 13.5211 16.203 15.0103C14.3143 15.2035 12.5259 15.5014 10.8879 15.8958C10.6957 15.0908 10.5536 14.31 10.4533 13.5614C10.0605 10.4864 10.5453 8.0956 11.6735 7.45967C11.9242 7.31478 12.2501 7.25038 12.6345 7.25038V5.45529C11.9325 5.45529 11.2974 5.60018 10.7458 5.90607C8.39747 7.21013 7.87097 11.2752 9.08276 16.3788C3.88463 17.9243 0.5 20.3956 0.5 23.0037C0.5 25.6199 3.90134 28.0992 9.11618 29.6367C7.91276 34.7563 8.44762 38.8295 10.8043 40.1335C11.3475 40.4394 11.9827 40.5843 12.6847 40.5843C14.9829 40.5843 17.9914 39.0066 21.0334 36.2697C24.0754 38.9905 27.084 40.5521 29.3822 40.5521C30.0842 40.5521 30.7193 40.4072 31.2709 40.1013C33.6192 38.7973 34.1457 34.7322 32.934 29.6286C38.1154 28.0911 41.5 25.6118 41.5 23.0037ZM30.619 17.6345C30.3098 18.673 29.9254 19.7436 29.4908 20.8142C29.1482 20.1702 28.7888 19.5262 28.396 18.8822C28.0116 18.2383 27.6021 17.6104 27.1926 16.9986C28.3793 17.1677 29.5243 17.3769 30.619 17.6345ZM26.7915 26.2075C26.1396 27.2942 25.4711 28.3246 24.7774 29.2825C23.5322 29.3871 22.2703 29.4435 21 29.4435C19.7381 29.4435 18.4762 29.3871 17.2393 29.2906C16.5457 28.3326 15.8687 27.3103 15.2169 26.2317C14.5817 25.1771 14.0051 24.1065 13.4786 23.0279C13.9967 21.9492 14.5817 20.8705 15.2085 19.816C15.8604 18.7293 16.5289 17.6989 17.2226 16.741C18.4678 16.6364 19.7297 16.58 21 16.58C22.2619 16.58 23.5238 16.6364 24.7607 16.733C25.4543 17.6909 26.1313 18.7132 26.7831 19.7919C27.4183 20.8464 27.9949 21.917 28.5214 22.9957C27.9949 24.0743 27.4183 25.153 26.7915 26.2075ZM29.4908 25.161C29.9421 26.2397 30.3265 27.3184 30.6441 28.3648C29.5493 28.6224 28.396 28.8398 27.201 29.0088C27.6105 28.389 28.02 27.7531 28.4044 27.101C28.7888 26.457 29.1482 25.805 29.4908 25.161ZM21.0167 33.7501C20.2395 32.9773 19.4623 32.116 18.6934 31.1742C19.4456 31.2064 20.2144 31.2305 20.9916 31.2305C21.7772 31.2305 22.5544 31.2144 23.3149 31.1742C22.5628 32.116 21.7856 32.9773 21.0167 33.7501ZM14.799 29.0088C13.6123 28.8398 12.4674 28.6305 11.3726 28.3729C11.6818 27.3345 12.0662 26.2639 12.5008 25.1932C12.8435 25.8372 13.2028 26.4812 13.5956 27.1252C13.9884 27.7692 14.3895 28.397 14.799 29.0088ZM20.9749 12.2573C21.7521 13.0301 22.5294 13.8914 23.2982 14.8332C22.5461 14.801 21.7772 14.7769 21 14.7769C20.2144 14.7769 19.4372 14.793 18.6767 14.8332C19.4289 13.8914 20.2061 13.0301 20.9749 12.2573ZM14.7907 16.9986C14.3812 17.6184 13.9717 18.2544 13.5872 18.9064C13.2028 19.5504 12.8435 20.1944 12.5008 20.8383C12.0495 19.7597 11.6651 18.681 11.3475 17.6345C12.4423 17.385 13.5956 17.1677 14.7907 16.9986ZM7.22748 27.0769C4.26906 25.8614 2.35528 24.2675 2.35528 23.0037C2.35528 21.7399 4.26906 20.138 7.22748 18.9305C7.94619 18.6327 8.73176 18.3671 9.5424 18.1175C10.0188 19.6953 10.6455 21.3374 11.4227 23.0198C10.6539 24.6942 10.0355 26.3282 9.56747 27.8979C8.74012 27.6484 7.95455 27.3747 7.22748 27.0769ZM11.7236 38.5799C10.587 37.9521 10.094 35.5613 10.4784 32.4863C10.5703 31.7296 10.7208 30.9327 10.9046 30.1197C12.5426 30.5061 14.331 30.8039 16.2114 30.9971C17.3396 32.4863 18.5096 33.8387 19.6879 35.022C16.9635 37.461 14.4062 38.7973 12.668 38.7973C12.2919 38.7892 11.9743 38.7168 11.7236 38.5799ZM31.5467 32.446C31.9395 35.521 31.4547 37.9118 30.3265 38.5477C30.0758 38.6926 29.7499 38.757 29.3655 38.757C27.6355 38.757 25.0699 37.4288 22.3455 35.0059C23.5155 33.8226 24.6855 32.4782 25.797 30.989C27.6857 30.7959 29.4741 30.498 31.1121 30.1036C31.3043 30.9166 31.4548 31.6974 31.5467 32.446ZM34.7642 27.0769C34.0455 27.3747 33.2599 27.6404 32.4492 27.8899C31.9729 26.3122 31.3461 24.67 30.5689 22.9876C31.3378 21.3133 31.9562 19.6792 32.4242 18.1095C33.2515 18.359 34.0371 18.6327 34.7725 18.9305C37.7309 20.1461 39.6447 21.7399 39.6447 23.0037C39.6364 24.2675 37.7226 25.8694 34.7642 27.0769Z"
                    fill="var(--color-react)"
                />
                <path
                    d="M20.993 26.6827C23.1022 26.6827 24.8122 25.0356 24.8122 23.0039C24.8122 20.9722 23.1022 19.3252 20.993 19.3252C18.8837 19.3252 17.1738 20.9722 17.1738 23.0039C17.1738 25.0356 18.8837 26.6827 20.993 26.6827Z"
                    fill="var(--color-react)"
                />
            </g>
            <defs>
                <clipPath id="clip0_1749_337763">
                    <rect width="41" height="45" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default react;
