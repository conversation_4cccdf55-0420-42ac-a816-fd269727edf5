import React from 'react';

const ECommerceIconSolution = ({ fill = 'white' }) => {
    return (
        <svg
            height="17"
            width="20"
            fill="none"
            viewBox="0 0 20 17"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.9275 3.8975C18.6931 3.6162 18.3996 3.38996 18.0679 3.23485C17.7363 3.07973 17.3745 2.99955 17.0083 3H3.535L3.5 2.7075C3.42837 2.09951 3.13615 1.53894 2.67874 1.13206C2.22133 0.725186 1.63052 0.500285 1.01833 0.5L0.833333 0.5C0.61232 0.5 0.400358 0.587797 0.244078 0.744078C0.0877974 0.900358 0 1.11232 0 1.33333C0 1.55435 0.0877974 1.76631 0.244078 1.92259C0.400358 2.07887 0.61232 2.16667 0.833333 2.16667H1.01833C1.22244 2.16669 1.41945 2.24163 1.57198 2.37726C1.72451 2.5129 1.82195 2.69979 1.84583 2.9025L2.9925 12.6525C3.11154 13.6665 3.59873 14.6015 4.36159 15.28C5.12445 15.9585 6.10988 16.3334 7.13083 16.3333H15.8333C16.0543 16.3333 16.2663 16.2455 16.4226 16.0893C16.5789 15.933 16.6667 15.721 16.6667 15.5C16.6667 15.279 16.5789 15.067 16.4226 14.9107C16.2663 14.7545 16.0543 14.6667 15.8333 14.6667H7.13083C6.61505 14.6652 6.11233 14.5043 5.69161 14.2059C5.27089 13.9075 4.95276 13.4863 4.78083 13H14.7142C15.6911 13.0001 16.6369 12.6569 17.3865 12.0304C18.1361 11.4039 18.6417 10.5339 18.815 9.5725L19.4692 5.94417C19.5345 5.58417 19.5198 5.21422 19.4262 4.86053C19.3326 4.50684 19.1623 4.17806 18.9275 3.8975ZM17.8333 5.64833L17.1783 9.27667C17.0743 9.85417 16.7704 10.3767 16.3199 10.7527C15.8694 11.1287 15.301 11.3342 14.7142 11.3333H4.51583L3.73167 4.66667H17.0083C17.1307 4.66594 17.2518 4.69218 17.3629 4.74355C17.4741 4.79491 17.5725 4.87012 17.6513 4.96384C17.73 5.05756 17.7872 5.16748 17.8186 5.28578C17.8501 5.40409 17.8551 5.52787 17.8333 5.64833Z"
                fill={fill}
            />
        </svg>
    );
};

export default ECommerceIconSolution;
