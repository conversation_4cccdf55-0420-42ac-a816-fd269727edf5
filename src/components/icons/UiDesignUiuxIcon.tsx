import React from 'react';

const UiDesignUiuxIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.27734 6.61104C4.18945 6.70869 3.75 7.36299 3.30078 8.06612L2.48047 9.34541L2.74414 12.2653C3.35938 18.9841 3.73047 24.3747 3.90625 29.2966L4.01367 32.1774L4.47266 32.5974C5.22461 33.2712 5.75195 33.5935 6.14258 33.5935C6.33789 33.5935 7.70508 33.4665 9.17969 33.3005C10.6543 33.1442 13.7305 32.8415 16.0156 32.6267C18.3008 32.4021 20.3516 32.2067 20.5859 32.1774L20.9961 32.1189V33.5837C20.9961 34.8923 20.9766 35.0485 20.8301 35.0974C20.6348 35.1462 19.2773 35.4099 17.1875 35.8005C16.1914 35.9763 15.7129 36.113 15.5762 36.2399C15.4102 36.406 15.3809 36.5817 15.3516 37.529L15.3223 38.6228L17.3828 41.0153C18.5254 42.3337 19.4824 43.4372 19.5215 43.486C19.5605 43.5251 20.1367 43.447 20.8008 43.3103C21.4648 43.1735 23.4375 42.8024 25.1855 42.4802C26.9336 42.1579 29.6289 41.6403 31.1621 41.3474L33.9551 40.8005L34.1309 39.7946C34.2285 39.238 34.2773 38.7204 34.248 38.6423C34.1602 38.4079 29.4434 34.4626 28.9355 34.1989C28.6719 34.0524 28.1641 34.0622 27.2852 34.2185L26.5918 34.3454L26.5332 33.0173C26.5039 32.2849 26.4941 31.6794 26.5137 31.6599C26.5527 31.6208 34.6094 30.8591 34.6387 30.8981C34.6484 30.9079 34.7266 31.7282 34.8145 32.7146C35.0488 35.4587 35.0098 35.322 35.4004 35.4489C35.5762 35.5075 35.7812 35.6247 35.8398 35.7028C36.0449 35.9372 36.7578 35.8591 40.0977 35.2439C41.8652 34.9216 44.209 34.4919 45.3125 34.2868C47.1875 33.9548 47.3242 33.9157 47.4316 33.6911C47.5195 33.4958 47.5195 32.9294 47.4121 31.1032C47.334 29.8142 47.207 27.5485 47.1191 26.0544C47.041 24.57 46.9434 23.2907 46.9141 23.2126C46.8848 23.1247 46.7871 23.0368 46.709 22.9978C46.582 22.9489 44.6094 23.1833 43.418 23.3884L43.1348 23.4372L43.1836 15.4294C43.2129 9.73604 43.2031 7.39229 43.125 7.33369C42.9785 7.21651 37.0703 7.07002 23.0469 6.83565C11.1621 6.64034 8.18359 6.58174 5.81055 6.48409C4.56055 6.43526 4.42383 6.44502 4.27734 6.61104ZM11.377 7.76338C12.7734 7.79268 18.9258 7.90987 25.0488 8.00752C31.1719 8.11494 37.5195 8.23213 39.1504 8.26143L42.1191 8.32979L42.0605 13.2224C42.0215 15.9177 41.9922 19.3552 41.9922 20.8591V23.613L41.4355 23.6716C41.123 23.7106 40.8008 23.7595 40.7031 23.7888C40.5566 23.8278 40.5469 23.3005 40.5078 16.9235C40.4883 13.1247 40.4297 9.93135 40.3906 9.84346C40.3516 9.75557 40.2539 9.65791 40.1758 9.62862C40.0977 9.59932 34.7461 9.51143 28.291 9.4333C21.8359 9.34541 14.4336 9.25752 11.8457 9.21846L7.14844 9.15987L6.99219 9.39424C6.85547 9.60909 6.875 10.1267 7.32422 16.0153C7.7832 22.1579 8.02734 25.7712 8.16406 28.8083C8.24219 30.4978 8.29102 30.6052 8.98438 30.5271C9.22852 30.488 12.168 30.2634 15.5273 30.029C22.2852 29.5407 29.2383 28.9939 32.373 28.7106C33.4961 28.613 34.4336 28.5349 34.4336 28.5446C34.4434 28.5544 34.4629 28.8278 34.4824 29.1501L34.5215 29.736L28.0273 30.3415C19.1211 31.1716 12.5586 31.7966 9.42383 32.1286C7.97852 32.2849 6.78711 32.4021 6.76758 32.3923C6.72852 32.3435 6.34766 26.5329 6.00586 20.5075C5.81055 17.2067 5.5957 13.486 5.51758 12.2556C5.43945 11.0153 5.35156 9.57002 5.3125 9.03291C5.2832 8.4958 5.24414 7.94893 5.21484 7.82198L5.16602 7.5876L7.00195 7.65596C8.00781 7.68526 9.98047 7.73409 11.377 7.76338ZM28.8867 10.5661L39.3555 10.6833V17.3337V23.9841L37.2852 24.2673L35.2051 24.5505L34.6973 25.0974L34.1797 25.6345L34.2383 26.2204C34.2676 26.5427 34.3066 26.9431 34.3359 27.0993L34.375 27.4021L31.2793 27.6657C25.4395 28.1638 10.3516 29.2966 9.61914 29.2966H9.30664L9.23828 27.7048C9.13086 24.9606 8.25195 12.3337 8.06641 10.7028L8.01758 10.322L13.2129 10.3806C16.0742 10.4196 23.125 10.4978 28.8867 10.5661ZM45.8984 24.6091C45.8984 24.8239 45.9863 26.4548 46.0938 28.2419C46.1914 30.029 46.2988 31.8161 46.3086 32.2165L46.3379 32.9392L43.7988 33.4079C42.4023 33.6618 40.2734 34.0524 39.0723 34.2673C37.8711 34.4919 36.875 34.6579 36.8457 34.6384C36.7969 34.5896 36.0156 25.6638 36.0645 25.6247C36.0938 25.5954 45.3906 24.2478 45.6836 24.2282C45.8691 24.2185 45.8984 24.2673 45.8984 24.6091ZM25.3906 34.1989C25.3906 36.904 25.4102 37.0114 25.9863 37.0114C26.377 37.0114 26.5625 36.6892 26.5625 36.0349V35.488L27.4902 35.361L28.4082 35.2341L28.8281 35.5564C29.0625 35.7321 29.9609 36.4743 30.8398 37.197L32.4316 38.5251L31.4258 38.7009C30.8691 38.7985 28.5938 39.1989 26.3672 39.5993C24.1406 39.9997 21.8457 40.4001 21.2793 40.4978L20.2441 40.654L19.9023 40.2731C18.5059 38.7302 17.1094 37.0505 17.168 37.0017C17.207 36.9724 18.0273 36.7966 18.9941 36.6208C19.9609 36.445 20.8105 36.279 20.8789 36.2595C20.957 36.2204 20.9961 36.3278 20.9961 36.6208C20.9961 37.2067 21.1328 37.4021 21.5332 37.4021C22.1582 37.4021 22.1973 37.2556 22.1582 34.4724C22.1191 32.3532 22.1387 32.031 22.2656 32.0212C22.3438 32.0212 22.959 31.9626 23.6328 31.8942C24.3066 31.8161 24.9707 31.7575 25.127 31.7478L25.3906 31.738V34.1989Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M21.7959 14.0234C21.6202 14.1504 21.5811 14.2676 21.5811 14.7168V15.2637L20.0479 15.2051C19.1983 15.166 17.8995 15.1367 17.167 15.1367H15.8194V14.9121C15.8194 14.4434 15.5069 14.3067 14.4424 14.3067C13.5733 14.3067 13.4561 14.3262 13.2608 14.5313C13.0655 14.7266 13.0362 14.8535 13.0362 15.5274C13.0362 16.2012 13.0655 16.3281 13.2608 16.5234C13.4561 16.7285 13.5733 16.7481 14.4424 16.7481C15.3116 16.7481 15.4385 16.7285 15.6436 16.5234C15.8682 16.3184 15.9561 16.3086 17.4405 16.3086L18.9932 16.3184L18.6124 16.5332C16.8936 17.5195 15.2237 19.2188 14.3838 20.8692L14.0127 21.582H13.3096C12.3038 21.582 12.3038 21.5918 12.3038 23.2324C12.3038 25 12.1963 24.9024 14.1788 24.9024C16.1221 24.9024 16.0147 25 16.0147 23.2227C16.0147 22.0703 15.9952 21.8848 15.8389 21.7481C15.7413 21.6504 15.585 21.582 15.4971 21.582C15.3409 21.582 15.3409 21.5527 15.5167 21.2109C16.0538 20.166 17.3526 18.75 18.4756 18.0078C19.2178 17.5098 20.3213 16.9824 21.0733 16.7676C21.5616 16.6211 21.5811 16.6211 21.5811 16.7969C21.5811 17.1875 21.9229 17.2852 23.3877 17.2852C24.8526 17.2852 25.1944 17.1875 25.1944 16.7871C25.1944 16.5527 25.253 16.5527 26.1319 16.8457C28.0655 17.4902 29.9307 18.8477 30.9463 20.3418L31.2686 20.8106L31.0147 21.0449C30.7706 21.2793 30.7608 21.3086 30.7608 22.5586C30.7608 23.2617 30.8096 23.9258 30.8584 24.0332C30.9561 24.209 31.0831 24.2188 32.6163 24.2188C34.1495 24.2188 34.2764 24.209 34.3741 24.0332C34.4229 23.9258 34.4717 23.252 34.4717 22.5195C34.4717 20.9277 34.4522 20.8984 33.3487 20.8984L32.6651 20.8887L32.128 20.0488C31.4639 19.0039 30.2725 17.793 29.2667 17.1094L28.4952 16.6016H29.6084C30.419 16.6113 30.7608 16.6406 30.8975 16.7481C31.0342 16.8555 31.3565 16.8945 32.0499 16.8945C33.3194 16.8945 33.3975 16.8262 33.3975 15.6836C33.3975 14.9121 33.378 14.8438 33.1436 14.6582C32.9288 14.4922 32.7432 14.4531 32.0694 14.4531C31.0538 14.4531 30.7901 14.5606 30.6436 15.0586L30.5362 15.4297H28.6124C27.5577 15.4297 26.3565 15.4004 25.9463 15.3711L25.1944 15.3027V14.7363C25.1944 13.916 25.0967 13.8672 23.3877 13.8672C22.3038 13.8672 21.9717 13.8965 21.7959 14.0234Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M42.2363 26.9043C41.6504 27.0312 40.4199 27.2558 39.5117 27.4023C37.5488 27.7148 37.5 27.7539 37.5 28.6035C37.5 29.2676 37.6465 29.4922 38.0859 29.4922C38.3887 29.4922 38.6719 29.1992 38.6719 28.8867C38.6719 28.7891 38.7695 28.6914 38.8965 28.6621C39.4531 28.5351 40.459 28.418 40.5176 28.4668C40.5469 28.5058 40.6445 29.1992 40.7422 30.0098C40.8789 31.2988 40.8887 31.5137 40.7617 31.6504C40.6836 31.7285 40.625 31.9433 40.625 32.1094C40.625 32.5293 40.8789 32.6465 41.5723 32.5586C42.4219 32.4512 42.7344 31.9726 42.2559 31.5137C42.0605 31.3281 42.0117 31.1133 41.8555 29.7949C41.7676 28.9746 41.7188 28.2715 41.748 28.2324C41.7871 28.2031 42.1484 28.1055 42.5586 28.0176L43.3105 27.8613L43.5156 28.1055C43.8574 28.5254 44.5312 28.3691 44.5312 27.8613C44.5312 27.5488 44.2773 26.9531 44.0625 26.7969C43.8379 26.6211 43.4961 26.6406 42.2363 26.9043Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default UiDesignUiuxIcon;
