import React from 'react';

const ts = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="46"
            height="46"
            viewBox="0 0 46 46"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337809)">
                <mask
                    id="mask0_1749_337809"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="46"
                    height="46"
                >
                    <path d="M45.5 0.5H0.5V45.5H45.5V0.5Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337809)">
                    <path d="M0.5 23V0.5H45.5V45.5H0.5" fill="#007ACC" />
                    <path
                        d="M10.3652 23.0783V24.9121H16.2152V41.5621H20.3665V24.9121H26.2165V23.1121C26.2165 22.0996 26.2165 21.2783 26.1715 21.2558C26.1715 21.2221 22.6052 21.2108 18.274 21.2108L10.399 21.2446V23.0896L10.3652 23.0783ZM36.6565 21.1996C37.804 21.4696 38.6815 21.9871 39.469 22.8083C39.8852 23.2583 40.504 24.0458 40.549 24.2483C40.549 24.3158 38.6027 25.6321 37.4215 26.3633C37.3765 26.3971 37.1965 26.2058 37.0165 25.9133C36.4315 25.0808 35.8352 24.7208 34.9015 24.6533C33.5515 24.5633 32.6515 25.2721 32.6515 26.4533C32.6515 26.8133 32.719 27.0158 32.854 27.3083C33.1577 27.9271 33.7202 28.2983 35.464 29.0633C38.6815 30.4471 40.0765 31.3583 40.9202 32.6633C41.8765 34.1258 42.0902 36.4208 41.449 38.1421C40.729 40.0208 38.974 41.2921 36.4652 41.7083C35.6777 41.8433 33.8777 41.8208 33.034 41.6746C31.234 41.3371 29.5127 40.4371 28.4552 39.2783C28.039 38.8283 27.2402 37.6246 27.2852 37.5458L27.7127 37.2758L29.4002 36.2971L30.6715 35.5546L30.964 35.9483C31.3352 36.5333 32.1677 37.3208 32.6515 37.5908C34.114 38.3446 36.0715 38.2433 37.039 37.3658C37.4552 36.9833 37.6352 36.5783 37.6352 36.0158C37.6352 35.4983 37.5565 35.2621 37.2977 34.8683C36.9377 34.3733 36.2177 33.9683 34.1927 33.0683C31.864 32.0783 30.874 31.4483 29.9515 30.4808C29.4227 29.8958 28.939 28.9846 28.714 28.2308C28.5452 27.5783 28.489 25.9808 28.6465 25.3396C29.1302 23.0896 30.829 21.5146 33.259 21.0646C34.0465 20.9071 35.9027 20.9746 36.679 21.1771L36.6565 21.1996Z"
                        fill="#252525"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_1749_337809">
                    <rect width="45" height="45" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default ts;
