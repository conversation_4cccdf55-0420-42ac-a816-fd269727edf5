import React from 'react';

const ResponsiveUiuxIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.428 5.00476C8.35965 5.06335 7.14871 5.10241 6.54324 5.22937C4.56082 5.64929 3.22293 6.92859 2.50027 9.09655C1.40652 12.3876 0.69363 21.9384 0.957302 29.7313C1.08425 33.2567 1.20144 34.9462 1.37722 35.8055C1.43582 36.0692 1.52371 37.1727 1.56277 38.2469C1.65066 40.3661 1.73855 40.8348 2.27566 41.8407C2.85183 42.9344 4.01394 43.6571 5.88894 44.1063C9.06277 44.8583 14.9221 45.6005 20.9475 46.0302C23.3011 46.1962 31.553 46.1962 33.6917 46.0302C38.1643 45.6786 43.0179 44.9071 44.7464 44.2528C47.9886 43.0419 49.385 40.3661 49.1897 35.7567C49.1507 35.0341 49.0628 33.0126 48.9846 31.2645C48.8284 27.9247 48.6331 22.5243 48.3889 15.2001C48.2229 10.1903 48.1741 9.82898 47.3929 8.21765C46.8264 7.03601 46.055 6.24499 45.0003 5.74694C43.7405 5.14148 43.2522 5.08288 39.0139 4.99499C34.5999 4.89734 22.2952 4.89734 13.428 5.00476ZM43.1253 6.7821C45.6839 7.34851 46.7874 9.23327 46.9436 13.3348C47.0022 14.7411 47.0022 14.7801 46.8167 14.6825C46.4749 14.4969 45.1175 14.37 42.5393 14.2723C37.803 14.0966 25.9866 13.9598 14.4925 13.9501L3.06668 13.9305L3.34011 12.329C3.73074 10.0341 4.1409 8.82312 4.80496 7.91491C5.08816 7.53405 5.80105 7.04577 6.3284 6.86999C7.10965 6.61609 9.31668 6.58679 26.1721 6.60632C40.5667 6.62585 42.51 6.64538 43.1253 6.7821ZM6.05496 14.6141C9.43386 14.8876 15.1663 15.1903 22.0218 15.4442C23.4768 15.5028 27.6077 15.5907 31.2014 15.6395C39.5413 15.7665 45.3616 15.6298 46.719 15.288L46.9729 15.2294V16.577C46.9729 18.4618 47.1292 22.3192 47.4124 27.6024C47.7932 34.7802 47.8714 36.7137 47.803 37.6024C47.5491 40.7958 46.4065 42.4364 43.8479 43.2665C42.5003 43.6962 38.7405 44.3798 36.0354 44.6923C30.4495 45.3173 23.555 45.3856 16.9925 44.8778C13.5647 44.6141 7.56863 43.8719 5.9573 43.5106C3.77957 43.0223 2.66629 42.0653 2.19754 40.2977C1.97293 39.4091 1.73855 36.4696 1.85574 35.9032C1.91433 35.6298 1.99246 33.1298 2.05105 30.3368C2.14871 24.7313 2.2073 22.8466 2.35379 20.3759C2.45144 18.6571 2.78347 15.2489 2.8909 14.6727L2.95925 14.3407L3.65261 14.4091C4.03347 14.4481 5.11746 14.536 6.05496 14.6141Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M8.67218 8.03234C7.51984 8.30578 6.69952 9.51672 6.87531 10.6788C6.9632 11.2648 7.37335 11.7335 7.79327 11.7335C7.95929 11.7335 8.10577 11.7921 8.13507 11.88C8.24249 12.1437 9.00421 12.4562 9.57062 12.4562C10.2933 12.4659 10.8109 12.1925 11.1722 11.5968C11.4163 11.2062 11.4554 11.0402 11.4651 10.4347C11.4651 9.82922 11.4261 9.65343 11.1819 9.20422C10.8694 8.62804 10.3226 8.16906 9.85382 8.08117C9.21906 7.95422 9.01398 7.94445 8.67218 8.03234ZM9.58038 9.68273C9.98077 9.90734 10.1859 10.7179 9.95148 11.1671C9.78546 11.4698 9.24835 11.6554 8.71124 11.5773L8.24249 11.5187L8.25226 10.962C8.25226 9.95617 8.90656 9.33117 9.58038 9.68273Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M19.5898 8.03183C18.5645 8.26621 17.7734 9.2623 17.7734 10.317C17.7734 11.108 18.1836 11.733 18.7109 11.733C18.8281 11.733 19.0625 11.86 19.2285 12.0162C19.8242 12.5728 20.8496 12.6217 21.6406 12.1334C22.5391 11.5768 22.6367 9.80917 21.8262 8.74472C21.3965 8.18808 20.3613 7.85605 19.5898 8.03183ZM20.5664 9.76035C20.9766 10.0924 21.123 10.7271 20.8887 11.1666C20.7227 11.4693 20.1855 11.6549 19.6484 11.5768L19.1895 11.5182L19.1992 11.0201C19.209 10.2389 19.2188 10.1998 19.541 9.8873C19.8828 9.5455 20.2441 9.49667 20.5664 9.76035Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M13.7127 8.19824C12.7752 8.63769 12.2478 9.53613 12.326 10.5713C12.365 11.1182 12.4041 11.206 12.7361 11.5088C12.9705 11.7236 13.1658 11.831 13.283 11.8018C13.3904 11.7725 13.5369 11.8408 13.6443 11.9482C14.1131 12.4853 15.158 12.6611 15.8807 12.3291C16.6424 11.9873 16.9451 11.333 16.8768 10.2197C16.8182 9.35058 16.4959 8.72558 15.8904 8.34472C15.3436 7.99316 14.3084 7.9248 13.7127 8.19824ZM15.0213 9.7412C15.5193 10.083 15.6756 10.8447 15.3338 11.2744C15.0994 11.5674 14.6893 11.6748 14.1228 11.5967L13.7029 11.5283L13.7127 10.8935C13.7225 10.2783 13.742 10.2295 14.0936 9.91699C14.4939 9.54589 14.699 9.50683 15.0213 9.7412Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.4089 18.1C30.1647 18.4418 29.4226 19.6136 28.7683 20.7172C23.8464 28.9594 17.8405 41.1566 17.987 42.6215C18.0163 42.8949 18.0554 42.934 18.3093 42.934C18.5339 42.934 18.6608 42.8363 18.9538 42.4554C19.9206 41.1761 29.1101 22.5238 30.7214 18.5785C31.239 17.2992 31.1315 17.1234 30.4089 18.1Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M15.859 19.0479C14.6579 19.5948 7.48014 25.4737 6.68912 26.5577C6.10318 27.3683 6.00553 28.4425 6.44498 29.3116C7.11881 30.6397 14.2672 38.3448 15.3219 38.8819C16.279 39.3604 17.4411 39.1749 17.8708 38.4718C18.0856 38.1202 18.1247 37.6124 17.9684 37.3097C17.9001 37.1827 17.9001 37.1241 17.9684 37.1241C18.1344 37.1241 18.0758 36.5577 17.8219 35.796C17.5387 34.9561 16.904 33.6573 15.195 30.503L13.9743 28.2179L15.2145 26.9386C16.777 25.3175 17.7731 24.1847 18.2028 23.5304C19.3551 21.8018 19.1501 19.7608 17.7536 19.0479C17.2751 18.8038 16.3962 18.8038 15.859 19.0479ZM17.07 20.5518C17.5583 21.0694 17.402 21.9093 16.6501 22.9151C16.4059 23.2374 15.4977 24.2628 14.6286 25.1905C13.7497 26.128 12.9391 27.0265 12.8122 27.212C12.5192 27.6222 12.402 28.1397 12.4997 28.5792C12.6266 29.1554 17.2067 36.8409 17.6071 37.1436C17.8122 37.2999 17.8122 37.3194 17.6754 37.7296C17.5094 38.1983 17.0212 38.589 16.6012 38.589C16.0934 38.589 15.3805 38.1202 14.6188 37.2804C12.1286 34.546 7.74381 29.1749 7.5192 28.589C7.19693 27.7393 7.55826 27.3292 11.279 24.3311C13.4567 22.5831 16.5524 20.3272 16.7868 20.3272C16.8258 20.3272 16.9528 20.4249 17.07 20.5518Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M32.9004 19.4965C32.0215 19.77 31.3477 20.7758 31.3477 21.7817C31.3477 22.6215 31.8262 23.6664 32.7637 24.8676C32.998 25.1801 33.8672 26.1371 34.6875 26.9868C35.5078 27.8461 36.2109 28.5981 36.25 28.6567C36.3281 28.7836 36.1914 29.0473 34.5996 32.016C33.9355 33.2563 33.1641 34.7993 32.8809 35.434C32.373 36.6059 32.2949 37.0453 32.3047 38.5102C32.3145 39.5942 33.7109 40.0825 35.0195 39.4672C35.5371 39.2231 37.373 37.4164 40.4297 34.145C42.6367 31.7817 43.3984 30.8637 43.8184 30.0239C44.1113 29.4282 44.1699 29.2231 44.1602 28.7153C44.1504 27.9828 43.8281 27.2602 43.2324 26.6547C42.3438 25.7368 38.877 22.8461 36.5234 21.059C34.4629 19.4965 33.8086 19.2133 32.9004 19.4965ZM35.1172 21.9868C37.6758 23.8227 42.1289 27.4067 42.6074 28.0219C42.9883 28.5102 42.9688 29.018 42.5391 29.7309C42.1094 30.4438 41.543 31.1274 38.1348 35.1215C35.2539 38.4907 34.7266 38.9985 34.0234 39.1352C33.6035 39.2133 33.0566 39.0278 32.8125 38.7153C32.5781 38.4125 32.5586 37.8852 32.7734 37.7094C32.8516 37.6313 33.4863 36.6743 34.1699 35.5707C36.8555 31.2348 37.8906 29.3598 37.8906 28.7836C37.8906 28.2368 37.4902 27.6606 36.0645 26.1664C34.248 24.2621 33.6523 23.5493 33.3008 22.8266C32.8418 21.9184 32.959 21.0102 33.5352 21.0102C33.6621 21.0102 34.3359 21.4203 35.1172 21.9868Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ResponsiveUiuxIcon;
