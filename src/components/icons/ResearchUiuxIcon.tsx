import React from 'react';

const ResearchUiuxIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M34.4737 4.93192C33.751 5.01005 32.0127 5.2249 30.626 5.42021C26.1241 6.04521 25.587 6.05497 20.8604 5.59599C17.8624 5.31279 17.8526 5.31279 16.9346 6.00615C16.0069 6.68974 15.6065 7.39286 15.4795 8.56474C15.4209 9.06279 15.7334 15.762 15.8702 17.0413L15.9288 17.5296L15.294 17.8909C12.2666 19.639 10.3526 22.8323 10.1866 26.4261C10.0987 28.3401 10.4405 29.9808 11.2999 31.68L11.6905 32.4515L11.2706 32.8518C10.8116 33.3011 10.587 33.8968 10.6846 34.4339C10.7432 34.7268 10.7237 34.7561 10.3819 34.8636C9.34673 35.1956 8.10649 36.1331 5.93852 38.2425C4.99126 39.1702 3.96587 40.137 3.66313 40.3909C2.90141 41.055 2.61821 41.553 2.55962 42.3831C2.52055 42.969 2.54985 43.1253 2.76469 43.5843C3.07719 44.219 3.39946 44.5315 4.14165 44.9124C4.83501 45.2835 5.52837 45.3128 6.47563 45.0198C7.4229 44.7269 8.07719 44.18 10.8018 41.4651C13.1749 39.0921 13.5362 38.6624 13.6729 38.0374C13.7217 37.7542 13.7608 37.7444 14.3467 37.6858C14.8155 37.637 15.0596 37.5589 15.4014 37.3147L15.8409 37.0022L15.7823 37.4222C15.6651 38.3011 15.6065 40.0394 15.6749 40.6839C15.7334 41.3186 15.7725 41.3772 16.1729 41.7581C16.6709 42.2269 16.9932 42.3245 18.5069 42.4808C21.2217 42.764 32.1592 42.764 41.6514 42.4808C46.3096 42.3538 46.5538 42.3147 47.042 41.7874C47.462 41.3284 47.5108 40.8304 47.3643 38.5061C47.2666 36.9143 47.2276 33.555 47.2276 25.137L47.2178 13.887L46.6124 12.6858C45.6456 10.7425 44.4639 9.02372 42.7647 7.08036C41.4073 5.52763 40.8897 5.01981 40.5772 4.94169C39.9424 4.80497 35.8018 4.7952 34.4737 4.93192ZM39.1319 5.95732C40.0206 5.95732 40.0401 5.99638 40.0401 7.54911C40.0401 9.65849 39.669 11.387 38.8096 13.2913C38.4581 14.0921 38.3799 14.3362 38.4581 14.5022C38.5948 14.7952 39.542 15.137 41.9932 15.7718C43.0967 16.0647 44.1514 16.3577 44.3467 16.4456C44.7471 16.6116 45.3135 17.2073 45.5967 17.7542C46.0948 18.721 46.0948 18.7991 46.0948 26.5726C46.1045 30.5667 46.1534 35.1565 46.212 36.7679C46.2706 38.3792 46.3194 39.9612 46.3194 40.2737C46.3291 41.0745 46.1827 41.1722 44.9522 41.2503C40.9288 41.5237 24.8936 41.6897 20.8506 41.514C19.4541 41.4554 18.126 41.3772 17.9112 41.3479L17.501 41.2894L17.5401 40.2933C17.5694 39.7561 17.6084 38.9554 17.6377 38.5159L17.6963 37.7249L18.292 37.8811C18.7413 37.9886 19.376 38.0276 20.7041 38.0276C22.3448 38.0276 22.5987 38.0081 23.4678 37.764C27.4424 36.6897 30.4014 33.6331 31.3194 29.639C31.5928 28.4378 31.5635 26.0257 31.2608 24.8636C30.7432 22.8616 29.8838 21.4065 28.2334 19.7268C27.1592 18.6331 26.2999 18.0081 25.0694 17.4319C23.1065 16.5042 20.7139 16.2112 18.5948 16.6507L17.9991 16.7679L17.9307 11.7972C17.8819 8.02763 17.8916 6.7874 17.9795 6.67997C18.1358 6.49443 18.5557 6.50419 20.8018 6.7581C22.2276 6.91435 23.3409 6.96318 25.2452 6.97294C27.7256 6.98271 27.8526 6.97294 30.6651 6.57255C34.542 6.01591 36.3291 5.85966 37.7452 5.91825C38.1749 5.93779 38.7999 5.95732 39.1319 5.95732ZM42.794 8.92607C43.2237 9.45341 43.8194 10.2737 44.1416 10.7327C44.7471 11.6409 45.8506 13.5647 45.792 13.6136C45.7725 13.6331 44.4249 13.6819 42.8135 13.7112L39.8643 13.7893L40.1963 12.969C40.7725 11.5433 41.2022 9.26786 41.212 7.57841L41.2217 7.08036L41.6221 7.51982C41.8467 7.76396 42.3741 8.38896 42.794 8.92607ZM23.0772 17.9104C24.7764 18.4183 25.8311 19.0237 27.0713 20.1956C28.3409 21.4065 29.2491 22.9202 29.6885 24.5608C30.0108 25.7425 30.0596 27.5198 29.8057 28.7308C29.21 31.5823 27.3741 34.0042 24.8545 35.2542C21.2706 37.0315 17.1202 36.4261 14.2295 33.7015C12.7647 32.3147 11.8565 30.6643 11.417 28.5647C11.212 27.6175 11.2608 25.7815 11.5049 24.7659C12.2959 21.5726 14.8057 18.9261 17.9405 17.969C19.0342 17.637 19.4834 17.5882 20.8995 17.6272C21.9639 17.6468 22.3838 17.7054 23.0772 17.9104ZM12.9112 34.0335C13.1749 34.3265 13.6729 34.7952 14.0245 35.0686C14.3663 35.3421 14.6495 35.6253 14.6495 35.7034C14.6495 35.7718 14.542 35.9085 14.4053 35.9964C14.0538 36.2308 13.3018 36.1331 12.794 35.8011C11.9932 35.2737 11.5635 34.1409 12.003 33.7015C12.1104 33.594 12.2569 33.4964 12.3155 33.4964C12.3741 33.4964 12.6377 33.7405 12.9112 34.0335ZM11.6905 36.4456C12.0323 36.7776 12.3057 37.1292 12.3057 37.2171C12.3057 37.6077 10.9874 39.0335 8.03813 41.8558C5.78227 44.0042 5.5479 44.1604 4.87407 43.9554C4.35649 43.7991 3.83891 43.3206 3.72173 42.9007C3.53618 42.2464 3.66313 42.012 4.77641 40.9671C5.34282 40.43 6.44633 39.3753 7.22758 38.6136C8.00883 37.8616 8.9268 37.0413 9.27837 36.7972C9.95219 36.3186 10.8018 35.8401 10.9776 35.8401C11.0362 35.8401 11.3584 36.1136 11.6905 36.4456Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M37.002 21.2305C31.9727 21.3477 32.0313 21.3477 32.0313 21.8848C32.0313 22.2852 32.2657 22.4609 32.793 22.4609C34.7852 22.4609 42.0801 22.2266 42.2168 22.1582C42.4707 22.0215 42.4414 21.4355 42.168 21.25C42.0508 21.1621 41.8848 21.1035 41.7969 21.1133C41.7188 21.1133 39.5606 21.1719 37.002 21.2305Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M34.707 28.311C34.1016 28.3891 33.8867 28.5454 33.8867 28.9165C33.8867 29.4243 34.1602 29.5024 35.498 29.3852C37.4023 29.2094 40.0488 29.4145 42.041 29.8833C42.666 30.0298 42.8711 30.0395 43.0664 29.9516C43.3789 29.8052 43.4473 29.3071 43.1934 29.0825C42.9883 28.8969 40.9863 28.477 39.6289 28.3305C38.623 28.2134 35.5469 28.2036 34.707 28.311Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M33.5941 35.2051C31.7973 35.2735 30.2445 35.3516 30.1469 35.4004C29.9027 35.5078 29.8344 35.8985 30.0199 36.1817C30.2152 36.4746 29.8539 36.4649 35.2348 36.2891C37.9984 36.1914 39.2094 36.1133 39.307 36.0352C39.5024 35.8789 39.4926 35.4004 39.2973 35.2149C39.1215 35.0293 38.5551 35.0293 33.5941 35.2051Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M23.3993 20.4296C23.1552 20.7714 23.2138 20.957 23.6435 21.3574C25.3036 22.8906 26.1923 25.3222 25.8798 27.5C25.7724 28.2519 25.7724 28.3496 25.9286 28.5156C26.1239 28.7304 26.3778 28.7597 26.661 28.6132C26.954 28.457 27.081 27.5683 27.0224 26.2207C26.9638 24.9218 26.7978 24.2187 26.3192 23.1738C25.8798 22.2168 25.4013 21.5234 24.6982 20.8203C24.0341 20.1464 23.6728 20.039 23.3993 20.4296Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.8146 30.2637C24.4142 30.6641 24.6877 31.25 25.2638 31.25C25.4884 31.25 25.7814 30.918 25.7814 30.6641C25.7814 30.1953 25.1369 29.9414 24.8146 30.2637Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ResearchUiuxIcon;
