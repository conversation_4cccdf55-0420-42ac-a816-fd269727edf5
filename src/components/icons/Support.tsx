import React from 'react';

const Support = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.0875 4.62774C5.50156 5.1418 4.14531 6.53086 3.675 8.12774C3.52187 8.66367 3.5 10.8074 3.5 28.9309C3.5 48.4543 3.51094 49.1543 3.70781 49.7996C4.21094 51.4074 5.58906 52.7746 7.20781 53.2449C7.73281 53.398 9.12187 53.4199 19.25 53.4199C29.3781 53.4199 30.7672 53.398 31.2922 53.2449C32.9109 52.7746 34.2891 51.4074 34.7922 49.7996C34.9781 49.209 35 48.7605 35 45.5996V42.0668L34.4422 41.6184C34.0812 41.334 33.7641 41.1699 33.5672 41.1699H33.2719L33.2172 44.9652C33.1953 47.4918 33.1297 48.9027 33.0422 49.1762C32.725 50.1715 31.6969 51.1777 30.7016 51.473C30.0344 51.6809 8.46562 51.6809 7.79844 51.473C6.77031 51.1668 5.75312 50.1496 5.44687 49.1215C5.23906 48.4543 5.23906 9.38555 5.44687 8.71836C5.76406 7.65742 6.7375 6.68399 7.79844 6.3668C8.46562 6.15899 30.0344 6.15899 30.7016 6.3668C31.6969 6.66211 32.725 7.66836 33.0422 8.66367C33.1297 8.93711 33.1953 10.348 33.2172 12.8637L33.2719 16.6699H34.1359H35V12.6887C35 9.09024 34.9781 8.6418 34.7922 8.04023C34.2891 6.43242 32.9109 5.06524 31.2922 4.59492C30.7672 4.4418 29.3781 4.41992 19.2063 4.43086C8.36719 4.43086 7.67812 4.4418 7.0875 4.62774Z"
                fill="#091542"
            />
            <path
                d="M7.7875 8.13867C7.59062 8.25898 7.33906 8.51055 7.21875 8.70742C7 9.06836 7 9.30899 7 28.9199C7 48.5309 7 48.7715 7.21875 49.1324C7.33906 49.3293 7.59062 49.5809 7.7875 49.7012C8.1375 49.9199 8.36719 49.9199 19.25 49.9199C30.1328 49.9199 30.3625 49.9199 30.7125 49.7012C30.9094 49.5809 31.1609 49.3293 31.2812 49.1324C31.4891 48.7934 31.5 48.5746 31.5 44.9871V41.2027L29.2359 41.1371C26.6109 41.0715 25.7906 40.9293 24.3906 40.273C22.4875 39.398 21.1531 38.0855 20.2344 36.1934C19.3484 34.3668 19.3047 34.0387 19.3047 28.9199C19.3047 23.8012 19.3484 23.473 20.2344 21.6465C21.1531 19.7652 22.3453 18.573 24.2266 17.6543C25.7578 16.9105 26.5125 16.7684 29.2359 16.7027L31.5 16.6371V12.8527C31.5 9.26524 31.4891 9.04649 31.2812 8.70742C31.1609 8.51055 30.9094 8.25898 30.7125 8.13867C30.3734 7.93086 30.1547 7.91992 26.5125 7.91992H22.6734L22.6078 8.4668C22.4875 9.50586 21.7656 10.4684 20.6719 11.048C20.3766 11.2121 20.0594 11.2559 19.25 11.2559C18.3094 11.2559 18.1562 11.223 17.6312 10.9387C16.6578 10.3918 16.0125 9.48399 15.8922 8.45586L15.8266 7.91992H11.9875C8.34531 7.91992 8.12656 7.93086 7.7875 8.13867Z"
                fill="#FFF6F6"
            />
            <path
                d="M26.0863 18.6274C23.7238 19.3056 21.777 21.2852 21.1754 23.6259C20.9129 24.6212 20.9129 33.2181 21.1754 34.2134C21.6457 36.0399 23.0129 37.7681 24.6754 38.6321C26.1082 39.3759 26.4582 39.4196 30.6145 39.4196H34.2566L38.4895 42.7993C40.8082 44.6477 42.8207 46.2227 42.952 46.2993C43.291 46.4743 44.2535 46.4524 44.6145 46.2556C44.7895 46.1681 45.052 45.9493 45.2051 45.7743C45.6973 45.1837 45.6754 44.8446 45.0082 42.2087C44.6691 40.8852 44.3738 39.7149 44.341 39.6056C44.2863 39.4415 44.3848 39.4196 45.4129 39.4196C46.8238 39.4196 47.6879 39.2118 48.8254 38.6321C50.4879 37.7681 51.8551 36.0399 52.3254 34.2134C52.5879 33.2181 52.5879 24.6212 52.3254 23.6259C51.8551 21.7993 50.4879 20.0712 48.8363 19.2181C47.2176 18.3759 47.8082 18.4196 36.6848 18.4306C27.5957 18.4306 26.6879 18.4524 26.0863 18.6274ZM31.1723 23.9977C31.4238 24.2493 31.4566 24.7415 31.2598 25.004C31.1832 25.1024 30.1332 25.9993 28.9301 27.0056C27.727 28.0009 26.7426 28.8649 26.7426 28.9196C26.7426 28.9743 27.727 29.8384 28.9301 30.8337C30.1332 31.8399 31.1832 32.7368 31.2598 32.8352C31.4566 33.0977 31.4238 33.5899 31.1723 33.8415C30.691 34.3227 30.4941 34.2134 27.541 31.7634C25.9551 30.4509 24.7301 29.3462 24.6754 29.1931C24.6098 29.0181 24.6098 28.8212 24.6754 28.6462C24.7301 28.4931 25.9551 27.3884 27.541 26.0759C30.4941 23.6259 30.691 23.5165 31.1723 23.9977ZM39.9223 23.9977C40.0426 24.1181 40.141 24.3477 40.141 24.5118C40.141 24.829 34.9348 33.6665 34.5848 33.9181C34.1254 34.279 33.3598 33.9071 33.3598 33.3165C33.3598 33.0102 38.566 24.1727 38.916 23.9102C39.1785 23.7134 39.6707 23.7462 39.9223 23.9977ZM45.9598 26.0759C47.5457 27.3884 48.7707 28.4931 48.8254 28.6462C48.891 28.8212 48.891 29.0181 48.8254 29.1931C48.7707 29.3462 47.5457 30.4509 45.9598 31.7634C43.0066 34.2134 42.8098 34.3227 42.3285 33.8415C42.077 33.5899 42.0441 33.0977 42.252 32.8352C42.3176 32.7368 43.3676 31.8399 44.5707 30.8337C45.7738 29.8384 46.7582 28.9743 46.7582 28.9196C46.7582 28.8649 45.7738 28.0009 44.5707 27.0056C43.3676 25.9993 42.3176 25.1024 42.252 25.004C42.0441 24.7415 42.077 24.2493 42.3285 23.9977C42.8098 23.5165 43.0066 23.6259 45.9598 26.0759Z"
                fill="#B9B9B9"
            />
        </svg>
    );
};

export default Support;
