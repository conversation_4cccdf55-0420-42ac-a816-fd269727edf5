import React from 'react';

const angular = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="44"
            height="46"
            viewBox="0 0 44 46"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337770)">
                <mask
                    id="mask0_1749_337770"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="44"
                    height="46"
                >
                    <path d="M43.5 0.5H0.5V45.5H43.5V0.5Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337770)">
                    <path
                        d="M0.517578 8.03114L21.6489 0.615234L43.3492 7.89928L39.8356 35.437L21.6489 45.3578L3.7467 35.5688L0.517578 8.03114Z"
                        fill="#E23237"
                    />
                    <path
                        d="M43.3488 7.89928L21.6484 0.615234V45.3578L39.8351 35.4535L43.3488 7.89928Z"
                        fill="#B52E31"
                    />
                    <path
                        d="M21.6829 5.83984L8.51562 34.6959L13.4345 34.6135L16.0781 28.1039H27.8902L30.7847 34.6959L35.486 34.7783L21.6829 5.83984ZM21.7165 15.085L26.1669 24.2477H17.8012L21.7165 15.085Z"
                        fill="#252525"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_1749_337770">
                    <rect width="43" height="45" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default angular;
