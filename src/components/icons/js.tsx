import React from 'react';

const js = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="46"
            height="46"
            viewBox="0 0 46 46"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337740)">
                <mask
                    id="mask0_1749_337740"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="46"
                    height="46"
                >
                    <path d="M45.5 0.5H0.5V45.5H45.5V0.5Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337740)">
                    <path d="M0.5 0.5H45.5V45.5H0.5V0.5Z" fill="#F0DB4F" />
                    <path
                        d="M41.8158 34.7675C41.4864 32.7143 40.1475 30.9904 36.1822 29.3821C34.8049 28.749 33.2692 28.2956 32.8115 27.2518C32.649 26.6444 32.6276 26.3022 32.7302 25.9343C33.0254 24.7409 34.4498 24.3688 35.5791 24.711C36.3063 24.9548 36.995 25.5152 37.4099 26.4092C39.3519 25.1516 39.3476 25.1601 40.7036 24.2961C40.2074 23.5261 39.9422 23.171 39.6171 22.8417C38.4493 21.537 36.8581 20.8655 34.3129 20.9168C33.8723 20.9724 33.4275 21.0323 32.9869 21.0879C31.7165 21.4087 30.5059 22.076 29.7958 22.97C27.6656 25.3869 28.273 29.6173 30.8652 31.3583C33.4189 33.2747 37.1704 33.711 37.6494 35.5033C38.1157 37.6977 36.0368 38.4077 33.9707 38.1554C32.4479 37.8389 31.601 37.0646 30.6856 35.6573C29.0002 36.6326 29.0002 36.6326 27.2678 37.6293C27.6785 38.5275 28.1105 38.9339 28.7992 39.7124C32.0587 43.019 40.216 42.8564 41.6789 37.8517C41.7388 37.6805 42.1324 36.5342 41.8158 34.7675ZM24.9622 21.182H20.7531C20.7531 24.8179 20.736 28.4282 20.736 32.0641C20.736 34.3783 20.8558 36.4999 20.4793 37.1501C19.8634 38.4291 18.2678 38.2708 17.5406 38.0227C16.8006 37.6591 16.4242 37.1415 15.9879 36.4101C15.8681 36.2005 15.7783 36.038 15.7483 36.0251C14.6062 36.7224 13.4684 37.4238 12.3262 38.1211C12.8952 39.2889 13.7336 40.3027 14.8072 40.9614C16.4113 41.9238 18.5672 42.219 20.8215 41.7014C22.2887 41.2737 23.5549 40.3882 24.2179 39.0408C25.1761 37.2742 24.9707 35.1354 24.9622 32.7699C24.9836 28.9115 24.9622 25.0532 24.9622 21.182Z"
                        fill="var(--color-gray-800)"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_1749_337740">
                    <rect width="45" height="45" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default js;
