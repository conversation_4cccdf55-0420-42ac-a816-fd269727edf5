import React from 'react';

const html = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="46"
            viewBox="0 0 40 46"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337716)">
                <mask
                    id="mask0_1749_337716"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="40"
                    height="46"
                >
                    <path d="M39.4288 0.5H0.857422V45.5H39.4288V0.5Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337716)">
                    <path
                        d="M4.08716 40.7863L0.857422 0.575195L39.4288 0.656002L36.0137 40.7863L20.2887 45.4249L4.08716 40.7863Z"
                        fill="var(--color-html)"
                    />
                    <path
                        d="M20.2891 41.5409V4.4043L36.173 4.45817L33.3405 37.6841L20.2891 41.5409Z"
                        fill="#EF652A"
                    />
                    <path
                        d="M31.8052 13.682L32.255 8.77344H7.82031L9.17031 23.8224H26.0338L25.3725 30.241L19.9719 31.724L14.4919 30.106L14.2008 26.2764H9.35571L10.0177 34.0975L19.9716 36.9292L30.0048 34.0975L31.3548 18.833H13.6446L13.1416 13.682H31.8052Z"
                        fill="#252525"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_1749_337716">
                    <rect width="40" height="45" fill="white" transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default html;
