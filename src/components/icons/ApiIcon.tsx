import React from 'react';

const ApiIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M23.906 5.75105C22.8708 6.82527 22.5095 7.59675 22.9392 7.83113C23.2126 7.97761 23.7497 7.94831 23.9841 7.7823C24.1892 7.64558 24.1892 7.65535 24.2576 8.88581C24.4529 12.4991 24.4822 12.7432 24.7068 13.2218C24.9314 13.671 25.2634 13.8858 25.4001 13.671C25.5759 13.3878 25.7224 11.4932 25.7322 9.52058V7.36238L26.0251 7.53816C26.4646 7.81159 27.0798 7.88972 27.2361 7.69441C27.3923 7.50886 27.1872 7.13777 26.3865 6.14167C25.2244 4.70613 24.9704 4.6573 23.906 5.75105Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M9.08244 13.7885C8.73088 13.8764 8.49651 14.1108 8.49651 14.3647C8.49651 14.4526 8.54533 15.019 8.59416 15.6245C8.67229 16.601 8.71135 16.7768 8.93596 17.0795C9.31682 17.5678 9.57073 17.4995 9.73674 16.8647C9.8051 16.5913 9.86369 16.2299 9.86369 16.0444C9.86369 15.7319 9.92229 15.8002 10.6547 16.9233C12.8324 20.2729 13.7992 23.5151 13.6332 26.9233C13.4965 29.9604 12.4809 32.5776 10.1371 35.9467L9.76604 36.4741V35.8979C9.75627 34.6186 8.92619 33.7788 8.64299 34.7553C8.57463 34.9799 8.48674 35.6635 8.44768 36.2788C8.37932 37.4018 8.43791 37.7338 8.73088 37.9291C8.78948 37.9682 9.36565 38.0073 10.0102 37.9975C11.016 37.9975 11.2406 37.9682 11.6215 37.7827C12.1879 37.5092 12.2465 37.2358 11.8168 36.8354C11.5922 36.6303 11.4067 36.5424 11.1723 36.5424C10.9867 36.5424 10.8403 36.5131 10.8403 36.4838C10.8403 36.4545 11.1235 36.1225 11.4653 35.7514C12.7055 34.4135 13.6625 32.6948 14.3461 30.6342C14.8442 29.1108 15.0395 28.0756 15.1078 26.6206C15.3031 22.5385 13.9262 18.5151 11.4653 15.937C10.811 15.2534 10.7426 15.1557 10.9477 15.1557C11.1918 15.1557 11.768 14.9702 11.9926 14.8334C12.1977 14.6967 12.1196 14.3256 11.8071 14.0229C11.5239 13.7495 11.475 13.7397 10.4692 13.7202C9.89299 13.7104 9.26799 13.7397 9.08244 13.7885Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M38.4089 13.8955C37.9499 14.0713 37.8913 14.1299 37.8913 14.374C37.8913 14.7158 38.3796 15.1553 38.7605 15.1553C38.9069 15.1553 39.0534 15.1748 39.073 15.2041C39.1022 15.2236 38.8093 15.5654 38.4284 15.9561C36.944 17.4795 35.7722 19.8232 35.2546 22.2646C34.9909 23.4854 34.8933 25.9854 35.0495 27.3135C35.489 31.083 36.9831 34.3154 39.0925 36.0635L39.6589 36.542H39.239C38.7409 36.542 38.0769 36.7568 37.9499 36.9521C37.8034 37.1963 37.9694 37.5869 38.321 37.8018C38.5944 37.9775 38.7995 38.0068 39.6198 38.0068C41.5046 38.0068 41.5534 37.9678 41.4167 36.249C41.3288 35.2334 41.28 34.999 41.0749 34.6865C40.9089 34.4326 40.7722 34.3252 40.6159 34.335C40.3718 34.3447 40.2741 34.5791 40.1569 35.4189L40.0788 35.9072L39.4733 35.0869C37.3054 32.1865 36.319 28.9736 36.4655 25.2627C36.5534 22.8994 36.9636 21.3076 38.0476 19.0908C38.7019 17.7822 39.6589 16.2295 40.03 15.8975C40.2351 15.7119 40.2351 15.7119 40.2351 15.9951C40.2351 16.6787 40.6452 17.4014 41.0261 17.4014C41.2702 17.4014 41.3972 16.9033 41.5241 15.4678C41.6315 14.3252 41.6218 14.2568 41.4362 14.0029C41.2409 13.749 41.2116 13.7393 40.0788 13.7197C39.112 13.7002 38.8288 13.7295 38.4089 13.8955Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M23.8783 15.9756C21.6126 16.5225 20.0013 18.9541 20.3919 21.2197C20.4603 21.6006 20.6263 22.1475 20.7728 22.4404C21.0462 22.9971 21.7689 23.8174 22.2474 24.1006L22.5306 24.2666L21.6419 24.7061C20.9095 25.0674 20.597 25.3018 19.8743 26.0342C18.5462 27.3721 17.9993 28.5342 17.9017 30.2529C17.8333 31.3564 18.0091 33.0459 18.224 33.3877C18.3118 33.5342 18.3216 33.6123 18.2435 33.6318C18.0482 33.7002 18.097 34.5498 18.3216 34.8525C18.9076 35.6338 19.8255 35.751 26.1732 35.8291L30.9095 35.8779L31.4466 35.5947C31.8372 35.3896 32.0618 35.1846 32.2767 34.833L32.5697 34.3545V32.4014C32.5697 30.8584 32.5306 30.2725 32.3841 29.6768C32.0326 28.1338 30.9388 26.415 29.6888 25.458C29.0638 24.9697 27.9212 24.335 27.6771 24.335C27.599 24.335 27.5404 24.2861 27.5404 24.2373C27.5404 24.1787 27.5794 24.1396 27.6283 24.1396C27.6771 24.1396 27.9603 23.915 28.263 23.6416C30.3822 21.708 29.8451 17.9287 27.2376 16.3955C26.3587 15.8779 24.9818 15.7021 23.8783 15.9756ZM26.3978 17.626C27.0814 17.9385 27.6868 18.5439 28.0189 19.2471C28.2337 19.7061 28.2728 19.9307 28.2728 20.624C28.2728 21.3662 28.2337 21.5322 27.9505 22.1084C27.7357 22.5479 27.4427 22.9385 27.0228 23.3193L26.4173 23.8955L25.1673 23.876C23.9466 23.8564 23.9173 23.8564 23.5462 23.5635C22.9505 23.085 22.3646 22.4014 22.1497 21.9131C21.5443 20.5459 22.0423 18.7881 23.2826 17.8994C24.1908 17.2549 25.3626 17.1475 26.3978 17.626ZM26.3587 25.3604C28.3021 25.8193 29.972 27.3525 30.7435 29.3936C30.9779 29.999 30.9974 30.1846 31.0365 32.0205L31.0755 33.9834L30.8021 34.1689C30.5482 34.3252 30.1771 34.3545 26.3197 34.4033C24.015 34.4424 21.5638 34.501 20.89 34.54C20.2161 34.5791 19.5033 34.5889 19.3079 34.5498C18.8685 34.4717 18.3216 33.9541 18.4193 33.7197C18.4583 33.6318 18.5462 32.8115 18.6146 31.9033C18.7415 30.0186 18.9173 29.3838 19.5521 28.29C20.3724 26.9131 21.9544 25.7217 23.4583 25.3604C24.1712 25.1846 25.6068 25.1846 26.3587 25.3604Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.5703 38.0752C24.3945 38.7002 24.3066 39.7061 24.248 41.7471C24.1895 44.042 24.1895 44.0615 23.9844 44.0029C23.8672 43.9639 23.6914 43.876 23.584 43.8076C23.1738 43.5146 22.8516 43.7783 22.8516 44.3838C22.8516 44.8623 23.2227 45.3701 24.1113 46.1318C24.541 46.5029 24.7363 46.6006 25.0098 46.6006C25.3223 46.6006 25.4199 46.5225 26.0352 45.8193C26.9727 44.7158 27.2754 43.9834 26.9043 43.6709C26.7871 43.5732 26.6699 43.6123 26.2891 43.8467L25.8105 44.1396L25.7422 42.9482C25.5664 39.6279 25.5078 39.0029 25.2832 38.5049C25.166 38.2314 25.0391 38.0068 25 38.0068C24.9512 38.0068 24.8535 37.9775 24.7754 37.9482C24.6875 37.9189 24.6094 37.9678 24.5703 38.0752Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ApiIcon;
