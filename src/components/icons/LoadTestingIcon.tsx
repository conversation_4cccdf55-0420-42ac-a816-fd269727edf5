import React from 'react';

const LoadTestingIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.8643 2.5531L4.24907 2.60193L3.71196 2.8656C2.65727 3.38318 1.90532 4.32068 1.60259 5.48279C1.4561 6.05896 1.44634 6.89881 1.49516 15.1508C1.59282 29.223 1.71977 35.9711 1.90532 36.6254C2.23735 37.7972 3.29204 38.8324 4.39555 39.0668C4.74712 39.1449 6.65141 39.1351 11.1534 39.0277C14.6104 38.9496 17.8233 38.8812 18.3018 38.8812H19.1709L19.1026 39.1937C19.044 39.4769 18.6534 42.4261 18.6534 42.6019C18.6534 42.6312 17.9795 42.6898 17.169 42.7191C15.5479 42.7679 15.1377 42.8656 14.1319 43.3929C12.628 44.184 11.4561 46.0492 11.3975 47.7289C11.378 48.2465 11.4073 48.3832 11.5928 48.559L11.8077 48.7738L14.376 48.6566C19.4249 48.432 20.9776 48.3441 23.1944 48.1488C25.294 47.973 25.5577 47.973 27.4424 48.1097C30.3624 48.3148 32.8233 48.4418 35.6553 48.5101L38.1456 48.5785L38.3604 48.3051C38.5557 48.0609 38.5752 47.9535 38.5166 47.4359C38.3702 45.9808 37.8721 44.9066 36.9249 44.0472C35.7725 42.9926 34.542 42.5922 32.4131 42.5922C31.4659 42.5922 31.251 42.5629 31.251 42.4554C31.251 42.3773 31.1534 41.6156 31.0459 40.766C30.8311 39.184 30.8116 38.9008 30.8995 38.8031C30.9288 38.7738 32.8624 38.8031 35.1768 38.8715C37.501 38.9398 40.8116 38.9984 42.5303 39.0082L45.6553 39.0277L46.2315 38.7445C46.9737 38.3832 47.5987 37.7484 47.9307 37.0453C48.1846 36.4984 48.1846 36.4301 48.2627 33.7543C48.3799 29.0765 48.5362 16.1176 48.5362 10.8441C48.5362 5.38514 48.5264 5.26795 47.9795 4.34021C47.628 3.73475 46.7686 2.99256 46.1143 2.72889L45.5577 2.50428H30.5186C22.2471 2.50428 12.9502 2.52381 9.8643 2.5531ZM45.5577 4.52576C45.9874 4.76014 46.3877 5.18006 46.5928 5.62928C46.7491 5.97107 46.7784 6.3031 46.7784 8.34412V10.6683L46.4659 10.6195C45.4502 10.4535 41.7198 10.1996 37.3545 10.0043C30.8116 9.71131 15.792 9.7992 9.13188 10.1703C7.10063 10.2875 3.88774 10.5414 3.49712 10.6195L3.22368 10.6781V8.5199C3.22368 6.60584 3.24321 6.31287 3.41899 5.84412C3.63384 5.27771 4.06352 4.7992 4.6104 4.53553C4.94243 4.36951 6.01665 4.35975 25.1182 4.35975C43.4874 4.36951 45.294 4.37928 45.5577 4.52576ZM6.10454 11.2836C10.9385 11.6644 16.2413 11.8304 23.7999 11.8304C34.3077 11.8304 42.1006 11.5668 46.251 11.0883L46.7491 11.0297L46.8272 18.9203C46.8663 23.266 46.9249 28.4027 46.9639 30.3363C46.9932 32.2699 47.0127 33.8715 47.003 33.891C46.9932 33.9105 46.1338 33.8519 45.0987 33.764C39.2393 33.266 32.8526 33.0804 23.3897 33.1488C13.7217 33.2269 9.80571 33.3539 4.90337 33.764C3.86821 33.8519 3.00884 33.9105 2.99907 33.891C2.97954 33.8715 2.99907 31.6547 3.03813 28.9691C3.0772 26.2836 3.14555 21.1469 3.18462 17.5531L3.25298 11.0297L3.79985 11.0883C4.10259 11.1176 5.13774 11.2152 6.10454 11.2836ZM6.59282 34.3402C11.1534 34.682 17.5206 34.8773 24.0831 34.8773C33.3311 34.8773 41.3096 34.6136 46.4756 34.1351L47.169 34.0668V35.1117C47.169 35.7855 47.1202 36.2738 47.0323 36.4789C46.8174 36.9965 46.2315 37.5726 45.6749 37.807C45.1963 38.0219 44.9522 38.0316 41.212 38.1586C39.0342 38.2269 35.0596 38.3246 32.3741 38.3832C29.6885 38.4418 27.2178 38.5101 26.8858 38.5394C26.5538 38.5687 25.6749 38.5492 24.9327 38.4906C24.1905 38.4418 20.2647 38.3246 16.212 38.2465C5.95805 38.0316 5.11821 37.9926 4.56157 37.7191C3.99516 37.4457 3.37016 36.7621 3.23345 36.2836C3.17485 36.0687 3.12602 35.4926 3.12602 34.9847V34.0668L3.93657 34.1351C4.37602 34.1742 5.5772 34.2621 6.59282 34.3402ZM28.5948 38.7543C30.9288 38.8226 30.7725 38.7445 30.5186 39.6625C30.3819 40.1508 30.0889 42.016 30.0791 42.4164C30.0791 42.5824 29.7666 42.5922 25.001 42.5922C20.9385 42.5922 19.9229 42.5629 19.9229 42.4652C19.9229 42.1918 19.6202 39.8773 19.5225 39.389C19.4639 39.0961 19.4444 38.8324 19.4834 38.8031C19.5811 38.7054 23.419 38.598 25.0987 38.6469C25.8799 38.6664 27.4522 38.7152 28.5948 38.7543ZM35.0987 44.8285C35.8604 45.2191 36.4366 45.8148 36.7881 46.5765C36.9249 46.8597 37.0127 47.1136 37.003 47.1234C36.9834 47.1429 35.8018 47.2113 34.376 47.2699C30.792 47.4261 26.7198 47.6605 25.4795 47.7679C24.7178 47.8461 23.9463 47.8265 22.501 47.7289C20.3135 47.5629 17.3448 47.4164 14.6202 47.3187C12.8038 47.2601 12.7842 47.2601 12.8526 47.0453C13.087 46.264 13.8975 45.2679 14.6006 44.9066C15.4795 44.4574 15.2647 44.4672 25.1084 44.4769L34.4346 44.4965L35.0987 44.8285Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M13.1857 6.43005C13.0001 6.49841 12.4142 7.04529 12.297 7.2699C12.0822 7.66052 12.8927 8.41248 13.5275 8.41248C13.8888 8.41248 14.2697 8.18787 14.5822 7.78748C14.9044 7.36755 14.9044 7.32849 14.5822 6.93787C14.2013 6.48865 13.6154 6.2738 13.1857 6.43005Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M7.51108 6.69408C7.31577 6.8308 7.10093 7.07494 7.03257 7.24096C6.92515 7.5144 6.92515 7.57299 7.10093 7.80737C7.19858 7.95385 7.45249 8.14916 7.65757 8.25659C8.16538 8.51049 8.66343 8.36401 9.02476 7.84643C9.16147 7.64135 9.27866 7.42651 9.27866 7.37768C9.27866 7.25073 8.71226 6.71362 8.42905 6.56713C8.10679 6.40112 7.89194 6.43041 7.51108 6.69408Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M27.8817 6.56689C25.0985 6.66454 23.2332 6.80126 21.5535 7.05517C19.7469 7.31884 19.1707 7.44579 19.3465 7.53368C19.532 7.63134 22.032 7.97314 23.9266 8.15868C27.4715 8.51025 33.9656 8.52978 38.0867 8.21728C39.4735 8.10986 42.5203 7.68017 42.9012 7.54345C43.1063 7.46532 43.1063 7.45556 42.8231 7.39697C41.1727 7.05517 37.3836 6.66454 34.6199 6.55712C32.1004 6.4497 31.1238 6.45947 27.8817 6.56689Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M25.528 14.545C25.3229 14.6719 25.196 15.0528 25.2546 15.2969C25.3913 15.8242 26.0749 16.0098 26.446 15.6094C26.8854 15.1407 26.612 14.4668 25.9968 14.4668C25.8014 14.4668 25.5866 14.5059 25.528 14.545Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M22.0996 14.633C21.7676 14.7013 21.4844 15.1213 21.4844 15.5412C21.4844 15.8341 21.543 15.9709 21.7871 16.1662C22.1875 16.508 22.5977 16.4982 22.959 16.1369C23.4375 15.6486 23.3203 14.9552 22.7051 14.7013C22.5195 14.6232 22.3535 14.5744 22.3437 14.5744C22.3242 14.5841 22.2168 14.6134 22.0996 14.633Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M28.8685 16.1856C28.6438 16.4005 28.6731 16.7716 28.927 16.9474C29.22 17.1524 29.3567 17.1427 29.5911 16.9083C29.9427 16.5567 29.7474 16.0294 29.2493 16.0294C29.1224 16.0294 28.9466 16.0978 28.8685 16.1856Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M18.8105 17.0648C18.2441 17.641 18.6542 18.764 19.4355 18.764C19.8066 18.764 20.2753 18.5297 20.412 18.2855C20.539 18.0316 20.539 17.5336 20.3925 17.2699C20.2167 16.9281 20.0019 16.8207 19.5234 16.8109C19.162 16.8109 19.0156 16.8597 18.8105 17.0648Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M31.1825 19.4086C31.1239 19.5746 31.3192 19.7601 31.495 19.7113C31.5536 19.6918 31.6024 19.5843 31.6219 19.4672C31.6512 19.3011 31.6024 19.2523 31.4462 19.2523C31.3387 19.2523 31.2215 19.3207 31.1825 19.4086Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M17.8905 20.2481C17.4608 20.3653 17.1581 20.8438 17.2264 21.293C17.3729 22.2793 18.6327 22.4746 19.0721 21.5762C19.2284 21.2637 19.2382 21.1465 19.1405 20.8731C19.0819 20.6875 18.9745 20.4922 18.8964 20.4239C18.6718 20.2383 18.2225 20.1602 17.8905 20.2481Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M18.3405 23.9105C17.8229 24.2328 17.7253 24.9164 18.1256 25.3949C18.3405 25.6586 18.4381 25.6976 18.8483 25.6976C19.4635 25.6976 19.8249 25.3656 19.8249 24.7992C19.8249 24.3109 19.6979 24.0765 19.3366 23.891C18.9753 23.7054 18.6725 23.7054 18.3405 23.9105Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.5102 25.0531C30.4809 25.1312 30.4711 25.2972 30.4906 25.4242C30.5102 25.6097 30.5785 25.6488 30.8617 25.6488C31.1449 25.6488 31.2133 25.6097 31.2328 25.4242C31.2523 25.2972 31.2133 25.1312 31.1547 25.0531C30.9984 24.8773 30.5785 24.8773 30.5102 25.0531Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M20.8984 26.6449C20.4101 26.8304 20.1953 27.4261 20.4492 27.9242C20.625 28.2562 20.7715 28.3344 21.2793 28.3344C21.6797 28.3344 22.0019 28.0707 22.0996 27.6703C22.2558 27.0062 21.5527 26.4008 20.8984 26.6449Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M27.8037 27.436C27.6084 27.6508 27.5889 28.1489 27.7549 28.3149C27.9307 28.4906 28.4678 28.4516 28.6435 28.2563C28.7412 28.1586 28.8096 27.9633 28.8096 27.8266C28.8096 27.3285 28.1357 27.0649 27.8037 27.436Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.072 28.0899C23.3592 28.793 24.3455 29.8282 25.1365 29.2032C25.3318 29.0469 25.3904 28.92 25.3904 28.6368C25.3904 27.9141 24.5799 27.5723 24.072 28.0899Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default LoadTestingIcon;
