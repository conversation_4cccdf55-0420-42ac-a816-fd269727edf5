import React from 'react';

const ecomuiux = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M3.02706 2.37216C2.14815 2.72372 1.738 3.77841 1.54268 6.17099C1.45479 7.26474 1.46456 8.63193 1.55245 12.128C1.62081 14.628 1.66964 18.8468 1.66964 21.5128L1.65987 26.3565L1.88448 26.6886C2.21651 27.1964 2.73409 27.4015 3.65206 27.4112H4.41378L4.34542 28.6319C4.31612 29.3058 4.28682 31.1222 4.28682 32.6847C4.27706 35.6144 4.39425 36.8936 4.73604 37.9581C5.08761 39.0226 5.82979 39.7159 6.89425 39.9601C7.63643 40.1261 9.66768 40.1163 12.4704 39.921C17.031 39.5987 22.9392 39.6378 23.154 39.9796C23.3103 40.214 23.2419 41.2979 23.0368 41.962C22.9294 42.3233 22.5681 43.1925 22.2458 43.9054C21.3669 45.8097 21.113 46.4444 20.9958 47.0694C20.8005 48.046 21.1325 48.7296 21.9821 49.0811C22.2653 49.1983 22.5876 49.2179 23.5349 49.169C24.1892 49.1397 25.8884 49.169 27.3044 49.2374C30.2439 49.3839 30.7321 49.3351 31.2009 48.8468C31.8454 48.1729 31.4939 46.2882 30.0485 42.7042L29.7165 41.8839L30.7907 40.1944L31.8747 38.5147L32.4997 39.0616C32.8415 39.3644 33.9157 40.4874 34.8923 41.5519C36.7771 43.6319 37.3337 44.1397 38.0368 44.4034C38.6325 44.6378 39.3845 44.5499 40.0583 44.1886C40.7517 43.8272 42.197 42.421 43.32 41.0245L44.238 39.8917L45.2927 39.8429C46.5329 39.7843 47.1775 39.5597 47.695 38.9737C48.1833 38.4366 48.281 37.9972 48.1345 36.8546C48.0173 35.8683 48.0368 35.0772 48.2907 28.9249C48.7985 16.8741 48.3982 11.6202 46.8747 10.3312L46.5622 10.0772H33.3396C18.6716 10.0772 19.7653 10.1358 20.4489 9.44248C20.7907 9.10068 20.9958 8.60263 20.9958 8.08505C20.9958 7.69443 20.6345 6.97177 20.4001 6.90341C20.2536 6.85458 20.2146 6.74716 20.2146 6.33701C20.2146 4.44247 19.7849 3.08505 19.0329 2.58701L18.613 2.31357L10.947 2.29404C5.96651 2.28427 3.18331 2.31357 3.02706 2.37216ZM17.3825 4.04208C18.4567 4.27646 18.3689 4.04208 18.3982 6.71786C18.4177 7.99716 18.4665 11.7569 18.5056 15.0577C18.5935 22.0401 18.4958 24.3253 18.0564 25.4093C17.9587 25.6632 17.9489 25.6632 16.8161 25.6534C16.1911 25.6534 13.6325 25.6925 11.1325 25.7413C6.29854 25.8292 4.44307 25.7511 3.49581 25.4288L3.01729 25.2628L2.988 22.6261C2.96846 21.171 2.91964 17.4601 2.88057 14.3741C2.79268 7.73349 2.90011 5.28232 3.32003 4.21786L3.41768 3.97372L6.22042 3.94443C7.76339 3.9249 9.05245 3.88583 9.08175 3.85654C9.11104 3.82724 10.8493 3.82724 12.9392 3.85654C15.6345 3.8956 16.9333 3.94443 17.3825 4.04208ZM44.1892 11.6886C45.3513 12.0108 46.113 12.7335 46.4353 13.8175C46.8357 15.1554 46.8454 16.8351 46.5817 27.753C46.5232 29.9796 46.5036 33.0851 46.5427 34.6378C46.5817 36.2003 46.572 37.5772 46.5427 37.6944C46.4157 38.0753 46.0056 38.2022 44.9021 38.2022H43.8864L43.2126 37.5089L42.529 36.8155L43.115 36.8741C44.7556 37.0401 45.5564 37.0499 45.7517 36.9034C46.279 36.4933 45.1853 35.9952 43.5935 35.9073C43.0075 35.878 42.2653 35.7999 41.9333 35.7511L41.3376 35.6436L39.6384 33.9444C38.5642 32.8702 37.9782 32.2159 38.0368 32.1573C38.0954 32.1085 38.7302 31.7081 39.4528 31.2687C41.1228 30.253 41.5622 29.9308 42.1775 29.2569C43.3298 27.9972 42.7439 26.8839 40.7517 26.5909C40.3708 26.5323 38.3005 25.9854 36.1618 25.3702C31.6501 24.0714 29.6872 23.5636 27.7829 23.212C24.3942 22.587 22.656 22.8604 22.656 24.0128C22.656 24.2179 23.2712 26.9327 24.0232 30.0382C24.7751 33.1534 25.3903 35.7218 25.3903 35.7511C25.3903 35.7804 24.8044 35.8194 24.0915 35.8292C23.3884 35.839 21.7966 35.9561 20.5564 36.1026L18.3103 36.3565L12.9392 36.2979C9.88253 36.2686 7.25557 36.2784 6.83565 36.3272C6.43526 36.3761 6.0837 36.3956 6.0544 36.3761C6.02511 36.3468 6.00557 35.2921 6.00557 34.0226C6.00557 32.753 6.02511 30.7218 6.0544 29.5304L6.10323 27.3429L8.00753 27.4112C9.05245 27.4503 11.2302 27.4894 12.8415 27.4894C16.0153 27.4894 17.2165 27.3722 18.3103 26.962C19.0524 26.6886 19.7067 26.0343 19.9509 25.3409C20.195 24.6476 20.3317 23.1339 20.2634 21.796C20.1169 18.9249 20.029 14.7159 20.0876 13.3194L20.1462 11.7862L27.1384 11.7765C32.1677 11.7667 34.8435 11.7179 36.6696 11.6104C39.8825 11.4151 43.3493 11.4542 44.1892 11.6886ZM32.7536 26.2882C37.2067 27.3624 40.8689 28.2511 40.8884 28.2706C40.947 28.3292 40.7614 28.4464 39.3064 29.3155C36.1423 31.2003 35.7419 31.7667 36.6306 33.1339C36.8942 33.544 38.0271 34.7354 39.8532 36.5421L42.6755 39.3253L40.7712 41.2296L38.8669 43.1241L36.113 40.3409C33.1247 37.3233 32.4704 36.796 31.8357 36.8155C30.9958 36.8546 30.4978 37.3526 29.2868 39.4034C28.5642 40.6144 28.32 40.9659 28.32 40.7901C28.32 40.7218 24.697 25.6437 24.4626 24.7452C24.3454 24.2765 24.3454 24.2374 24.4919 24.2765C24.5896 24.3058 28.3005 25.214 32.7536 26.2882ZM25.3317 36.63C25.5954 36.6788 25.6345 36.7472 25.8005 37.3722C25.8884 37.7433 25.9763 38.0851 25.9763 38.1339C25.9763 38.1729 21.5915 38.1925 16.2399 38.1827L6.49386 38.1534L6.3669 37.88C6.29854 37.7335 6.21065 37.4503 6.18136 37.255C6.12276 36.9034 6.12276 36.9034 6.40596 36.9815C6.56221 37.0206 8.73018 37.0694 11.2204 37.0987C15.9958 37.1476 19.6579 37.0304 21.0935 36.7862C21.5524 36.7081 22.0993 36.63 22.3142 36.6104C22.9685 36.5519 25.029 36.5616 25.3317 36.63ZM26.5622 40.5948C26.8552 41.8448 27.3728 42.5968 27.9489 42.5968C28.0661 42.5968 28.154 42.7237 28.2224 43.0069C28.613 44.5401 29.1306 45.8683 29.736 46.9034C29.9216 47.2257 30.0778 47.5089 30.0778 47.5382C30.0778 47.5577 28.3884 47.5772 26.3181 47.5772C24.2478 47.5772 22.5583 47.5577 22.5583 47.5382C22.5583 47.5089 22.7146 47.2257 22.9001 46.9034C23.9255 45.1554 24.5993 42.9288 24.7653 40.7511L24.8337 39.8526L25.6149 39.8819L26.3962 39.9112L26.5622 40.5948Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M10.3026 5.69251C8.63267 5.73157 6.88462 5.8097 6.39634 5.85852C5.82994 5.90735 5.45884 5.90735 5.36119 5.84876C5.10728 5.69251 4.92173 5.82923 4.68736 6.31751C4.48228 6.73743 4.45298 7.02063 4.33579 9.48157C4.25767 10.9659 4.16978 14.4034 4.14048 17.1281C4.07212 21.6398 4.08189 22.0988 4.22837 22.3234C4.52134 22.7726 4.96079 22.8214 7.25572 22.6847C8.39829 22.6163 11.035 22.5382 13.1346 22.5089L16.9335 22.4503L17.1678 22.2159C17.3143 22.0695 17.3924 21.8937 17.3729 21.7179C17.3534 21.5714 17.412 21.2101 17.4901 20.9171C17.7049 20.087 17.6756 11.337 17.451 9.40345C17.3631 8.63196 17.285 7.5968 17.285 7.09876C17.285 6.36634 17.2557 6.17102 17.119 6.05384C16.6991 5.68274 14.5799 5.56556 10.3026 5.69251ZM11.2499 7.19641H16.0057L16.0545 7.53821C16.201 8.48548 16.3182 9.57923 16.4061 10.8585C16.5135 12.3722 16.5428 20.6046 16.4452 20.8585C16.4159 20.9464 16.3475 20.9855 16.2987 20.9562C16.1424 20.8585 9.44322 20.8976 7.76353 21.005C6.85533 21.0636 5.95689 21.1417 5.79087 21.171L5.46861 21.2199V18.8468C5.46861 16.2101 5.64439 8.11438 5.72251 7.46009L5.77134 7.04016L6.13267 7.11829C6.32798 7.15735 8.63267 7.19641 11.2499 7.19641Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M10.302 23.3781C9.2473 23.5148 8.9641 23.593 8.84691 23.7688C8.68089 24.0422 8.88597 24.4621 9.28636 24.6574C10.0676 25.0383 11.6887 25.0285 12.2063 24.6477C12.6946 24.2863 12.7922 23.759 12.4407 23.4074C12.2746 23.2414 11.4543 23.2316 10.302 23.3781Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M26.2012 17.4502C25.8692 17.6846 25.3906 18.5244 25.0879 19.3838C24.6387 20.6533 24.7266 21.3076 25.3321 21.3076C25.8496 21.3076 26.1524 20.8584 26.8164 19.1494C27.2071 18.124 27.2461 17.6748 26.9336 17.46C26.6602 17.2646 26.4649 17.2646 26.2012 17.4502Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M22.3923 17.9679C22.1872 18.0948 22.0602 18.6026 22.1188 19.0128C22.1872 19.4425 22.7927 21.0343 23.027 21.3858C23.2028 21.6593 23.5837 21.7667 23.7887 21.5909C24.1696 21.2784 23.6325 18.5733 23.0954 18.1144C22.8317 17.88 22.5973 17.8311 22.3923 17.9679Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M28.4571 19.4425C27.9103 19.7745 27.1095 21.0245 27.1681 21.464C27.2364 22.0792 27.7345 22.0206 28.545 21.298C29.4239 20.5167 29.7267 19.7257 29.2775 19.4132C28.9943 19.2081 28.8282 19.2179 28.4571 19.4425Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M33.4175 27.382C33.2124 27.5871 33.2808 28.0265 33.564 28.2707C33.7104 28.3878 34.1011 28.7394 34.4429 29.0421C35.0972 29.6476 35.3218 29.716 35.5952 29.4132C35.8491 29.1398 35.7417 28.505 35.3804 28.1242C35.0581 27.7824 34.0815 27.2648 33.7593 27.2648C33.6323 27.2648 33.4858 27.3136 33.4175 27.382Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M35.7619 29.8233C35.5861 29.9991 35.6154 30.5264 35.8205 30.712C36.299 31.1514 36.9826 30.6046 36.6994 30.0186C36.6017 29.8135 36.4943 29.7452 36.2307 29.7256C36.0451 29.7061 35.8303 29.7549 35.7619 29.8233Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default ecomuiux;
