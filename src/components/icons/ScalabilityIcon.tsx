import React from 'react';

const ScalabilityIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.4039 3.17251C19.2727 3.2272 19.1086 3.43501 19.0539 3.63189C18.9883 3.82876 18.6383 4.95532 18.2664 6.1147C17.8945 7.30689 17.6102 8.3897 17.632 8.57564C17.6867 9.13345 18.4524 9.33032 18.7039 8.86001C18.8461 8.59751 20.3445 3.86157 20.3445 3.67564C20.3445 3.29282 19.8086 3.00845 19.4039 3.17251Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M11.8127 3.81746C11.6158 4.18934 11.6814 4.3534 12.4689 5.38152C13.3439 6.52996 14.197 8.19246 14.558 9.40652C14.8861 10.555 15.1705 10.8065 15.7611 10.5003C16.5158 10.0846 14.8752 6.11434 13.1142 4.12371C12.7533 3.69715 12.6002 3.60965 12.3049 3.60965C12.0424 3.60965 11.8892 3.67527 11.8127 3.81746Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M9.69103 9.25331C9.46135 9.483 9.47228 9.98612 9.71291 10.1939C10.0738 10.5002 13.3223 12.5674 13.4645 12.5783C14.0223 12.6549 14.416 12.2611 14.2301 11.8017C14.1098 11.4955 10.4348 9.07831 10.0848 9.07831C9.96447 9.07831 9.77853 9.15487 9.69103 9.25331Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M39.3203 11.9438C18.9547 12.1406 15.8703 12.1953 15.6625 12.4031C15.4875 12.5672 15.4766 13.0266 15.4766 17.7516C15.4875 23.8438 15.6734 26.6984 16.0891 27.0375C16.3844 27.2891 16.5375 27.2891 16.8547 27.0266L17.1062 26.8297L16.8875 24.1609C16.7453 22.225 16.6797 20.3984 16.6797 17.5328C16.6797 15.3563 16.7125 13.5516 16.7453 13.5188C16.7781 13.4859 20.9562 13.4094 26.0422 13.3438C31.1172 13.2891 37.9422 13.2125 41.2125 13.1688L47.1406 13.1031V15.7609C47.1406 17.2266 47.2172 22.1703 47.3047 26.7422C47.3922 31.3141 47.4688 36.1375 47.4688 37.4719V39.8891L46.0797 40.0203C42.9953 40.2938 40.5344 40.3813 36.2031 40.3375L31.675 40.3047L31.5109 40.6C31.3797 40.8297 31.3687 40.9391 31.4672 41.1469C31.5328 41.2891 31.6203 41.5297 31.6531 41.6828C31.6859 41.8359 31.8062 42.1859 31.9266 42.4594L32.1562 42.9516L33.5453 43.0172C34.3219 43.0609 35.7875 43.0938 36.8047 43.0938C38.9594 43.0938 49.4813 42.7766 49.8531 42.7C49.9953 42.6672 50.2359 42.525 50.3781 42.3828C50.6406 42.1203 50.6406 42.1094 50.6406 39.7906C50.6406 37.6469 50.4109 28.7656 50.2578 25.0469C50.225 24.1719 50.1266 21.2188 50.0391 18.4734C49.9406 15.225 49.8312 13.3766 49.7547 13.2016C49.6453 12.95 49.0656 12.5016 48.2344 12.0203C47.9937 11.8781 46.8016 11.8672 39.3203 11.9438Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M37.7891 17.8938C35.4484 18.2109 33.1625 18.6594 32.9656 18.8234C32.5938 19.1297 32.7797 19.5344 33.9062 20.8578L35 22.1375L34.65 22.4875C29.8922 27.0266 28.5469 28.3828 28.5469 28.6125C28.5469 28.875 28.8859 29.3125 29.1047 29.3125C29.3891 29.3125 29.8813 28.875 32.7687 26.075C35.6344 23.2859 35.8641 23.0891 35.9953 23.2859C36.0828 23.3953 36.6516 24.0844 37.275 24.8172C38.3141 26.0312 38.4344 26.1406 38.7734 26.1406C39.2328 26.1406 39.3641 25.9656 39.8125 24.7406C40.2391 23.5594 40.4141 22.7609 40.7422 20.4969C41.0484 18.3422 41.0703 17.9266 40.8844 17.7406C40.6984 17.5547 39.9437 17.5984 37.7891 17.8938Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M16.5711 30.0456C8.24762 30.1768 5.69919 30.2643 5.51325 30.4175C5.2945 30.5925 5.31637 31.5768 5.68825 38.0628C6.02731 43.969 6.12575 46.4628 6.12575 49.0112V51.3847L6.63981 51.9206C7.657 52.9706 6.92419 52.8831 14.3836 52.8503C22.7289 52.8065 28.6132 52.5659 29.3023 52.2378C29.4445 52.1722 29.6632 51.9862 29.7836 51.8222C30.0023 51.5268 30.0023 51.505 29.8273 49.0878C29.4445 43.9909 29.2039 38.4347 29.2039 34.4862C29.2039 31.7628 29.1711 31.6643 28.0336 30.68L27.2242 29.969L22.882 29.9909C20.4976 30.0018 17.6539 30.0347 16.5711 30.0456ZM26.207 34.705C26.2289 38.3253 26.382 41.0925 26.8523 46.3643C27.0054 48.0159 27.1257 49.4925 27.1257 49.6565V49.9518L26.0101 50.0284C25.3867 50.0722 21.6351 50.1487 17.6648 50.2034C13.6945 50.2581 9.74606 50.3347 8.882 50.3784L7.32887 50.455V47.6768C7.32887 46.1456 7.27419 43.9581 7.2195 42.8206C7.07731 40.0862 6.68356 33.0534 6.62887 32.2003L6.58512 31.5222L9.01325 31.4565C11.682 31.38 20.7164 31.2159 24.0632 31.194L26.1961 31.1722L26.207 34.705Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default ScalabilityIcon;
