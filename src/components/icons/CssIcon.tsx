import React from 'react';

const CssIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131094)"
                transform="translate(-0.00500488 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131094"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131094" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131094"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default CssIcon;
