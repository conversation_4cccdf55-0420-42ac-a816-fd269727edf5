import React from 'react';

const ContainerIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M32.5202 4.00324C31.4949 4.52082 31.1238 5.11652 31.114 6.22004C31.1042 7.90949 32.2468 8.99348 33.907 8.88606C34.6687 8.83723 35.0593 8.68098 35.5671 8.23176L35.8894 7.95832L36.4753 8.31965C36.7976 8.52473 37.3738 8.87629 37.7449 9.09114C38.1257 9.31575 38.9167 9.79426 39.5027 10.1654C40.0984 10.5364 41.3191 11.2689 42.2273 11.806L43.8777 12.763L43.8093 16.3275C43.78 18.2904 43.7507 24.0618 43.7507 29.1693V38.4466L43.2331 38.7298C42.9597 38.8958 42.071 39.4329 41.2605 39.9505C40.4597 40.4583 39.0925 41.3177 38.2234 41.8548C37.3542 42.4017 36.4656 42.9681 36.2507 43.1243L35.8406 43.3978L35.3523 43.0853C34.8933 42.7923 34.7761 42.763 33.907 42.7337C32.9011 42.6946 32.6472 42.7532 32.0027 43.1732C30.8406 43.9251 30.8015 46.1028 31.9343 47.1282C32.4909 47.6263 33.0671 47.8118 33.9265 47.7532C35.5183 47.6458 36.5242 46.4739 36.3972 44.8528C36.3777 44.5013 36.3191 44.14 36.2898 44.0618C36.241 43.9446 36.4363 43.8275 37.1785 43.5345C38.907 42.8607 41.7292 41.4349 44.7956 39.7064C45.0886 39.5306 45.2351 39.3743 45.323 39.1107C45.4206 38.8177 45.4206 36.4446 45.3327 27.4603C45.196 14.5404 45.1472 12.138 45.0007 11.9622C44.7175 11.6204 40.0495 9.19856 37.5202 8.06575L36.2117 7.48957L36.2995 7.1966C36.3386 7.04035 36.3777 6.55207 36.3777 6.11262C36.3777 5.41926 36.3484 5.28254 36.114 4.93098C35.9773 4.71613 35.6355 4.39387 35.362 4.21809C34.8835 3.91535 34.7956 3.89582 33.8581 3.86652C33.0574 3.83723 32.7937 3.86652 32.5202 4.00324ZM34.405 5.12629C34.8054 5.6634 34.8933 5.98566 34.7663 6.44465C34.3952 7.84113 32.4226 7.5091 32.4226 6.05402C32.4226 5.6341 32.9988 4.6966 33.4187 4.43293L33.7214 4.23762L33.9265 4.50129C34.0437 4.63801 34.2585 4.92121 34.405 5.12629ZM34.405 43.9935C34.8054 44.5306 34.8933 44.8528 34.7663 45.3118C34.3952 46.6986 32.4226 46.3763 32.4226 44.9407C32.4226 44.4329 32.9206 43.6224 33.4382 43.2903L33.7214 43.1048L33.9265 43.3587C34.0437 43.5052 34.2585 43.7884 34.405 43.9935Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M14.6979 4.08105C14.2389 4.31543 13.9948 4.58887 13.7799 5.10645C13.5163 5.71191 13.4284 6.32715 13.5553 6.77637L13.653 7.12793L12.7545 7.58691C12.2663 7.84082 11.8268 8.08496 11.7682 8.12402C11.7194 8.16309 10.8405 8.62207 9.81509 9.14941C7.65689 10.2627 5.09829 11.6885 4.88345 11.8936C4.7565 12.0205 4.7272 12.8115 4.65884 17.2354C4.58072 22.3623 4.65884 39.0713 4.7565 39.335C4.81509 39.4912 6.43618 40.4092 8.40884 41.3857C9.20962 41.7861 10.5182 42.46 11.3092 42.8701C12.1002 43.29 12.9596 43.71 13.2135 43.8076C13.6335 43.9639 13.6823 44.0127 13.6335 44.2178C13.3698 45.2432 13.5358 46.0928 14.1315 46.7861C14.6393 47.3818 15.3034 47.6748 16.1627 47.6748C17.2565 47.665 18.1256 47.1475 18.5651 46.2295C19.0924 45.1553 18.7018 43.6709 17.7741 43.2021C17.3346 42.9873 16.6608 42.9189 16.4167 43.0752C16.3288 43.1338 16.2213 43.0947 16.0944 42.9678C15.9284 42.8018 15.8014 42.7725 15.3229 42.8115C14.8639 42.8408 14.6784 42.9092 14.3659 43.1631L13.985 43.4658L13.6042 43.1729C12.4713 42.2646 10.6256 41.0244 8.59439 39.8037L6.29947 38.4268L6.24087 32.7236C6.21158 29.5889 6.14322 23.7881 6.10415 19.8428L6.03579 12.665L6.45572 12.4307C7.94009 11.6201 10.4108 10.0771 12.0221 8.9541L13.8874 7.65527L14.1999 7.99707C14.6588 8.52441 15.3424 8.80762 16.1627 8.80762C17.2565 8.79785 18.1256 8.28027 18.5651 7.3623C19.0827 6.29785 18.692 4.80371 17.7741 4.33496C17.3346 4.12012 16.6608 4.05176 16.4167 4.20801C16.3288 4.2666 16.2213 4.22754 16.1042 4.11035C15.8698 3.87598 15.1178 3.85645 14.6979 4.08105ZM16.651 4.84277C17.1002 5.09668 17.4811 5.69238 17.4811 6.1416C17.4811 6.85449 16.4459 7.51855 15.8307 7.20605C15.1667 6.85449 14.9811 6.26855 15.2936 5.47754C15.4303 5.14551 15.987 4.62793 16.2116 4.61816C16.2409 4.61816 16.4362 4.71582 16.651 4.84277ZM16.651 43.71C17.1002 43.9639 17.4811 44.5596 17.4811 45.0088C17.4811 45.7119 16.4362 46.3857 15.8307 46.0732C15.1667 45.7217 14.9811 45.1357 15.2936 44.3447C15.4303 44.0127 15.987 43.4951 16.2116 43.4854C16.2409 43.4854 16.4362 43.583 16.651 43.71Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.3647 11.835C23.6225 11.9521 19.8432 13.4463 17.1382 14.6963C13.7104 16.2881 9.60886 18.2998 9.50143 18.4463C9.29636 18.7197 9.34518 19.2959 9.58932 19.5205C9.71628 19.6279 11.103 20.3506 12.6753 21.1123C14.2475 21.8838 15.4975 22.5478 15.4585 22.5869C15.4096 22.626 14.8139 22.8603 14.1108 23.1143C12.6655 23.6416 11.8842 23.9541 10.5366 24.5986C9.80417 24.9404 9.56003 25.1064 9.47214 25.3115C9.35495 25.5947 9.42331 26.0342 9.59909 26.1709C9.70651 26.249 13.2807 28.0947 14.9311 28.9248L15.5561 29.2373L13.8081 29.8916C11.9233 30.6045 9.91159 31.4932 9.64792 31.7275C9.39401 31.9521 9.33542 32.4111 9.52097 32.6943C9.62839 32.8604 10.9663 33.583 14.0424 35.1064C21.2592 38.7002 24.6186 40.2822 24.9995 40.2822C25.2534 40.2822 27.0307 39.4229 32.0307 36.8936C40.8882 32.3916 40.3315 32.7041 40.3315 32.3037C40.3315 32.1182 40.2729 31.9326 40.2143 31.8838C39.9507 31.6787 37.9585 30.8193 35.976 30.0576L33.9155 29.2666L37.0112 27.6748C38.7202 26.8057 40.1655 26.0342 40.2241 25.9561C40.2924 25.8779 40.3315 25.6924 40.312 25.5361C40.2827 25.3018 40.1753 25.2041 39.4526 24.8525C38.7299 24.501 37.2944 23.9053 34.6186 22.8701L33.935 22.5967L37.0893 21.0244C40.4975 19.3154 40.6245 19.2275 40.3217 18.6904C40.0874 18.2705 33.2221 15.0186 28.1928 12.9385C26.4936 12.2451 25.4096 11.8447 25.3022 11.8838C25.1948 11.9131 25.0874 11.8936 25.0483 11.835C25.019 11.7861 24.9604 11.7471 24.9214 11.7471C24.8823 11.7568 24.6382 11.7959 24.3647 11.835ZM25.0874 12.0693C25.2436 12.2158 26.6792 12.9971 28.5639 13.9736C29.1596 14.2764 30.6538 15.0576 31.8842 15.7119C33.1245 16.3662 35.0288 17.3623 36.1225 17.9189L38.1147 18.9443L37.7241 19.1396C37.519 19.2471 35.5464 20.2041 33.3393 21.2588C31.1421 22.3135 28.3589 23.6611 27.1479 24.2471L24.9507 25.3213L19.726 22.7432C16.8549 21.3271 13.9643 19.9111 13.31 19.5986C12.646 19.2861 12.1089 19.0029 12.1089 18.9736C12.1089 18.9443 13.0659 18.417 14.2378 17.8115C15.3999 17.2158 16.8452 16.4443 17.4311 16.1221C18.0268 15.79 19.521 14.9893 20.7514 14.3252C22.9096 13.1826 23.6225 12.7822 24.4624 12.2061C24.9214 11.8936 24.8921 11.8936 25.0874 12.0693ZM20.5854 24.9697C22.8706 26.0732 24.853 26.9717 24.9897 26.9717C25.1264 26.9717 27.0503 26.0635 29.2671 24.9502C32.7339 23.1924 33.31 22.9385 33.476 23.0361C33.5835 23.0947 34.2573 23.4561 34.9702 23.8271C35.6928 24.208 36.7378 24.7549 37.2944 25.0478L38.31 25.585L37.3921 26.0244C35.6538 26.8447 29.4428 29.8232 27.2553 30.8877C26.0542 31.4736 25.0288 31.9521 24.9799 31.9521C24.9311 31.9521 23.8471 31.4443 22.5776 30.8193C21.3081 30.1943 19.521 29.3154 18.603 28.876C17.6948 28.4268 15.7905 27.499 14.3842 26.8057L11.8354 25.5361L13.7007 24.5303C14.7358 23.9639 15.7514 23.3877 15.9565 23.2412C16.1616 23.0947 16.3569 22.9678 16.3862 22.9678C16.4057 22.9678 18.3003 23.8662 20.5854 24.9697ZM33.9643 29.9502C36.8061 31.4346 38.1831 32.1865 38.1831 32.2549C38.1831 32.2842 36.9428 32.9092 35.4292 33.6416C32.685 34.96 25.0971 38.583 24.9897 38.6221C24.9409 38.6416 19.2768 35.8877 13.8862 33.2217L11.8061 32.1963L13.6421 31.2295C14.6479 30.6924 15.6538 30.1357 15.8589 29.9795C16.0639 29.833 16.3178 29.7061 16.4057 29.7061C16.5034 29.7061 17.1186 29.9697 17.7827 30.3018C21.2202 31.9912 24.7846 33.6416 25.0092 33.6318C25.1557 33.6318 27.0405 32.7236 29.2085 31.6201C31.3764 30.5166 33.1831 29.6084 33.2319 29.6084C33.271 29.6084 33.603 29.7646 33.9643 29.9502Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default ContainerIcon;
