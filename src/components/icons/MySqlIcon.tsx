import React from 'react';

const MySqlIcon = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect
                height="56"
                width="56"
                fill="url(#pattern0_470_131082)"
                transform="translate(-0.00500488 0.405029)"
            />
            <defs>
                <pattern
                    height="1"
                    id="pattern0_470_131082"
                    width="1"
                    patternContentUnits="objectBoundingBox"
                >
                    <use transform="scale(0.015625)" xlinkHref="#image0_470_131082" />
                </pattern>
                <image
                    height="64"
                    id="image0_470_131082"
                    width="64"
                    preserveAspectRatio="none"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    );
};

export default MySqlIcon;
