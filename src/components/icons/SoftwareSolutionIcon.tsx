import React from 'react';

const SoftwareSolutionIcon = ({ fill = 'white' }) => {
    return (
        <svg
            height="21"
            width="20"
            fill="none"
            viewBox="0 0 20 21"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_964_647379)">
                <path
                    d="M20 6.33333V13C20 15.2975 18.1308 17.1667 15.8333 17.1667H10.8333V18.8333H14.1667C14.6267 18.8333 15 19.2058 15 19.6667C15 20.1275 14.6267 20.5 14.1667 20.5H5.83333C5.37333 20.5 5 20.1275 5 19.6667C5 19.2058 5.37333 18.8333 5.83333 18.8333H9.16667V17.1667H4.16667C1.86917 17.1667 0 15.2975 0 13V6.33333C0 5.8725 0.373333 5.5 0.833333 5.5C1.29333 5.5 1.66667 5.8725 1.66667 6.33333V13C1.66667 14.3783 2.78833 15.5 4.16667 15.5H15.8333C17.2117 15.5 18.3333 14.3783 18.3333 13V6.33333C18.3333 5.8725 18.7067 5.5 19.1667 5.5C19.6267 5.5 20 5.8725 20 6.33333ZM14.1667 8.83333C14.6267 8.83333 15 8.46083 15 8V3.83333C15 3.37333 15.3742 3 15.8333 3H16.8975C17.1858 3.49583 17.7175 3.83333 18.3333 3.83333C19.2542 3.83333 20 3.0875 20 2.16667C20 1.24583 19.2542 0.5 18.3333 0.5C17.7183 0.5 17.1867 0.8375 16.8975 1.33333H15.8333C14.455 1.33333 13.3333 2.455 13.3333 3.83333V8C13.3333 8.46083 13.7067 8.83333 14.1667 8.83333ZM1.66667 3.83333C2.28167 3.83333 2.81333 3.49583 3.1025 3H4.16667C4.62583 3 5 3.37333 5 3.83333V8C5 8.46083 5.37333 8.83333 5.83333 8.83333C6.29333 8.83333 6.66667 8.46083 6.66667 8V3.83333C6.66667 2.455 5.545 1.33333 4.16667 1.33333H3.1025C2.81417 0.8375 2.2825 0.5 1.66667 0.5C0.745833 0.5 0 1.24583 0 2.16667C0 3.0875 0.745833 3.83333 1.66667 3.83333ZM9.16667 3.6025V8C9.16667 8.46083 9.54 8.83333 10 8.83333C10.46 8.83333 10.8333 8.46083 10.8333 8V3.6025C11.3292 3.31417 11.6667 2.7825 11.6667 2.16667C11.6667 1.24583 10.9208 0.5 10 0.5C9.07917 0.5 8.33333 1.24583 8.33333 2.16667C8.33333 2.78167 8.67083 3.31333 9.16667 3.6025Z"
                    fill={fill}
                />
            </g>
            <defs>
                <clipPath id="clip0_964_647379">
                    <rect height="20" width="20" fill={fill} transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default SoftwareSolutionIcon;
