import React from 'react';

const PerformanceDevopsIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.49319 6.95695C5.27248 7.30851 4.53029 9.24211 5.07717 10.6777C5.44826 11.6835 6.39553 12.4257 7.45998 12.5722C7.70412 12.6112 7.88967 12.6698 7.86037 12.7089C7.77248 12.8456 7.52834 15.8144 7.43069 17.8359C7.27444 21.0878 7.20608 29.203 7.2842 35.6288C7.34279 40.9804 7.37209 41.5566 7.51858 41.7421C7.68459 41.9472 7.68459 41.9472 14.0713 41.9179C28.3291 41.8593 34.374 41.7226 38.4072 41.371L39.0615 41.3124V41.6933C39.0615 42.2206 39.5694 43.1972 40.0576 43.6171C40.6826 44.1444 41.4053 44.3788 42.2842 44.33C43.4658 44.2616 44.2959 43.7148 44.8233 42.6698C45.2334 41.8398 45.2432 40.9413 44.833 40.1015C44.3643 39.1054 43.7686 38.7441 42.7139 38.8027C42.3135 38.8222 42.0498 38.7831 41.9717 38.705C41.8252 38.5585 41.1905 38.5487 40.6924 38.6952C40.0186 38.8808 39.335 39.7011 39.1201 40.5702L39.0518 40.8437L36.8838 40.7948C33.4756 40.7069 18.1826 40.5507 13.2608 40.5409H8.78811L8.79787 38.998L8.80764 37.4648L9.64748 36.6933C10.1065 36.2636 11.6006 34.7792 12.958 33.4023L15.4287 30.8925L15.7315 31.2538C15.9072 31.4491 16.503 32.0741 17.0596 32.6503C18.3682 33.998 18.4561 34.0175 19.3838 33.0312C19.7354 32.6601 20.2432 32.0839 20.5166 31.7421C20.7803 31.4003 21.21 30.8534 21.4639 30.5409C21.7178 30.2284 21.9522 29.9257 21.9815 29.8671C22.0205 29.8183 22.3428 29.955 22.7139 30.1796C23.6514 30.746 24.4717 30.9902 25.6143 31.0487C26.8447 31.1073 27.5772 30.9609 28.6123 30.453C30.1065 29.7304 31.3174 28.2655 31.7959 26.6249C32.001 25.912 32.0205 24.4081 31.835 23.6464C31.6592 22.8945 31.249 22.0253 30.7803 21.4101L30.3701 20.873L31.0244 20.287C31.376 19.955 31.9815 19.3691 32.3526 18.9784C32.7334 18.5878 33.0655 18.2851 33.1045 18.2948C33.1338 18.3144 33.6319 18.8515 34.1983 19.496C35.5264 21.0292 35.7705 21.2245 36.21 21.1855C36.5225 21.1562 36.7276 20.9706 38.2022 19.3984C39.1104 18.4316 40.1553 17.2304 40.5362 16.7226L41.2295 15.8144L41.4444 16.4296C41.6983 17.1816 41.8838 17.3183 42.2549 17.0448C42.7334 16.6933 42.7822 16.4882 42.753 15.1112C42.7041 13.3437 42.7725 13.3827 40.2627 13.6464C39.628 13.7148 39.1104 14.037 39.1104 14.3691C39.1104 14.5937 39.169 14.623 39.9405 14.7695C40.585 14.8866 40.7217 14.9355 40.5459 14.9941C40.2432 15.0917 38.4365 16.8398 37.1865 18.246C36.6397 18.8515 36.1709 19.3495 36.1221 19.3495C36.083 19.3495 35.4385 18.7343 34.6963 17.9823C33.3975 16.6835 33.1436 16.498 32.8506 16.6933C32.792 16.7323 32.3526 17.2011 31.8838 17.7382C31.1612 18.5585 30.5069 19.457 30.0283 20.2773C29.9405 20.4335 29.8721 20.414 29.2471 20.0917C28.8662 19.8964 28.2998 19.6718 27.9776 19.5937C27.333 19.4277 26.1709 19.3984 26.1709 19.5448C26.1709 19.5937 25.9854 19.5448 25.7608 19.4472C25.2822 19.2323 24.833 19.203 24.1006 19.3593C21.9912 19.8085 20.4385 21.498 19.96 23.871C19.6182 25.5409 20.0869 27.4843 21.1514 28.7831L21.4737 29.1933L20.917 29.5546C20.6143 29.7597 19.9209 30.3554 19.3838 30.8827L18.3975 31.8398L17.0401 30.4823C15.3701 28.8124 15.3604 28.8027 14.4912 29.7304C12.5088 31.8495 9.41311 35.3945 9.07131 35.9316L8.78811 36.3808L8.76858 31.8886C8.75881 29.4277 8.74904 27.2109 8.74904 26.9667C8.78811 24.9062 8.44631 13.2167 8.33889 12.8359C8.29006 12.6601 8.32912 12.621 8.61233 12.5624C9.51076 12.3671 10.4971 11.5175 10.7803 10.6874C10.9561 10.1698 10.9854 9.22258 10.8389 8.68547C10.6826 8.14836 10.1651 7.51359 9.66701 7.25969C9.33498 7.09367 9.09084 7.04484 8.64162 7.05461C8.31936 7.06437 7.95803 7.02531 7.84084 6.96672C7.5967 6.83 6.92287 6.83 6.49319 6.95695ZM9.02248 7.97258C9.35451 8.18742 9.76467 9.00773 9.76467 9.45695C9.76467 10.1015 9.42287 10.6288 8.76858 10.9609C7.58694 11.5663 6.18069 10.5702 6.38576 9.28117C6.51272 8.50969 6.88381 7.98234 7.5967 7.58195L7.94826 7.38664L8.34865 7.58195C8.5635 7.69914 8.86623 7.87492 9.02248 7.97258ZM26.5615 19.9355C26.8545 19.9941 27.1865 20.082 27.294 20.1308C27.4014 20.1796 27.5967 20.248 27.7334 20.287C28.1143 20.3847 29.1494 21.1659 29.5205 21.6249C30.0186 22.2304 30.4385 23.0312 30.6143 23.6952C31.3955 26.703 29.042 29.662 25.878 29.623C23.1924 29.6034 20.9854 27.0155 21.376 24.3495C21.503 23.5097 21.8252 22.6796 22.3135 21.9472C22.7237 21.332 23.1338 20.9413 23.7979 20.5605C24.4131 20.1894 25.9072 19.6327 25.9756 19.7402C26.0049 19.789 26.2686 19.8769 26.5615 19.9355ZM42.7334 39.4277C43.1729 39.6132 43.71 40.2773 43.8369 40.7948C44.1104 41.8691 43.2998 42.8749 42.1377 42.9042C41.3565 42.914 40.6436 42.2694 40.5459 41.4687C40.458 40.6191 40.917 39.7792 41.6983 39.3691C42.0401 39.1835 42.1572 39.1933 42.7334 39.4277Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.4395 21.2437C25.3027 21.3413 25.2734 21.4488 25.3223 21.732C25.3613 21.9663 25.3418 22.0835 25.2637 22.0835C25.1953 22.0835 24.9219 22.1909 24.6387 22.3179C23.9551 22.6402 23.6328 23.1187 23.6328 23.8413C23.6328 24.896 24.3945 25.5503 25.7715 25.687C26.2402 25.7359 26.6699 25.8433 26.8262 25.9409C27.0801 26.107 27.0801 26.1167 26.8945 26.2534C26.5625 26.4976 25.752 26.6734 25.0293 26.6538C24.6387 26.6441 24.2969 26.6636 24.2578 26.6929C24.1406 26.8199 24.4824 27.3081 24.8828 27.5816C25.2441 27.8257 25.293 27.9038 25.293 28.2261C25.293 28.8511 25.7227 29.0659 26.4551 28.812C26.7578 28.7046 26.7773 28.6753 26.7383 28.3238C26.709 27.9527 26.709 27.9429 27.1777 27.7866C28.0566 27.4937 28.5156 26.9273 28.5156 26.1363C28.5156 25.6187 28.3594 25.2769 27.959 24.896C27.5977 24.5542 27.0703 24.3687 26.1719 24.2808C25.4102 24.2027 24.9023 24.0171 24.9023 23.812C24.9121 23.4116 25.5371 23.1675 26.7676 23.0894C27.7051 23.0308 28.0273 22.9136 27.8809 22.6792C27.8223 22.5718 27.3438 22.3374 26.9824 22.23C26.7969 22.1812 26.7578 22.1031 26.7578 21.771C26.7578 21.5074 26.6992 21.3316 26.5918 21.2437C26.3672 21.0777 25.6738 21.0679 25.4395 21.2437Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.7423 23.8513C35.4493 24.3787 35.8497 24.701 36.6505 24.5838C37.1388 24.5056 37.1876 24.4177 37.0021 23.9978C36.8165 23.5974 35.9278 23.49 35.7423 23.8513Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.7617 26.6937C35.5956 26.8597 35.6152 27.3285 35.8007 27.3968C36.1132 27.514 37.0117 27.4652 37.0703 27.3187C37.1288 27.1625 36.9531 26.7718 36.7773 26.6547C36.6015 26.5375 35.8886 26.5668 35.7617 26.6937Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.8092 29.5746C35.6041 29.809 35.5943 30.2484 35.7994 30.3265C35.8873 30.3558 36.1412 30.3851 36.3756 30.3851C36.9225 30.3851 37.108 30.307 37.108 30.0922C37.108 29.5844 36.151 29.2035 35.8092 29.5746Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M26.8145 32.5525C26.6485 32.7966 26.6875 33.0993 26.8926 33.2361C27.1758 33.4314 28.2207 33.2458 28.2207 33.0017C28.211 32.6989 27.8594 32.4157 27.42 32.3669C27.0196 32.3181 26.9512 32.3376 26.8145 32.5525Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.7509 32.5137C35.4579 33.0703 35.7704 33.373 36.5517 33.2851C37.1279 33.2168 37.1474 33.1972 37.0693 32.9043C36.9716 32.5137 36.8642 32.4355 36.3564 32.3672C35.9169 32.3183 35.8486 32.3379 35.7509 32.5137Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M17.9292 35.424C17.7241 35.6193 17.7241 36.0099 17.9292 36.088C18.2222 36.1955 19.2183 36.1564 19.2671 36.0197C19.3257 35.8537 19.0815 35.4044 18.896 35.3263C18.603 35.2189 18.0854 35.2677 17.9292 35.424Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M26.8042 35.4624C26.6577 35.6284 26.648 35.6967 26.7359 35.9018C26.8433 36.1459 26.8921 36.1557 27.5073 36.1264C28.2691 36.0971 28.3765 35.9506 27.9859 35.5307C27.6734 35.1987 27.0777 35.1596 26.8042 35.4624Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.7511 35.4039C35.6828 35.482 35.6437 35.6676 35.6632 35.8238C35.6925 36.0875 35.7121 36.0973 36.3566 36.1266C36.8644 36.1461 37.0304 36.1266 37.0695 36.0094C37.1281 35.8629 36.9523 35.4625 36.7765 35.3453C36.591 35.2184 35.8683 35.2574 35.7511 35.4039Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M17.8692 38.3832C17.7227 38.6664 17.752 38.9496 17.9278 39.0179C18.0157 39.0472 18.3086 39.0765 18.6016 39.0765C19.2754 39.0765 19.4317 38.9105 19.1387 38.4906C18.9532 38.2367 18.875 38.1976 18.4551 38.1976C18.0743 38.1976 17.9473 38.2367 17.8692 38.3832Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M26.7576 38.3832C26.5232 38.8226 26.7869 39.0765 27.4705 39.0765C28.2029 39.0765 28.3103 38.9789 28.0955 38.5492C27.9392 38.2074 27.9099 38.1976 27.4021 38.1976C26.9724 38.1976 26.8455 38.2367 26.7576 38.3832Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M35.7617 38.3142C35.6934 38.3825 35.6445 38.5583 35.6445 38.7048C35.6445 39.0271 35.8496 39.1052 36.582 39.0564C37.1289 39.0271 37.2363 38.8415 36.9727 38.4411C36.8359 38.236 36.7285 38.197 36.3477 38.197C36.084 38.197 35.8301 38.2458 35.7617 38.3142Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default PerformanceDevopsIcon;
