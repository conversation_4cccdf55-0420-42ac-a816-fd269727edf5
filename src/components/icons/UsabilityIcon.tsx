import React from 'react';

const UsabilityIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M26.543 3.43201L25.6445 4.79919H24.1211C22.4023 4.79919 22.168 4.86755 22.168 5.40466C22.168 5.62927 22.3828 5.97107 23.1348 6.91833L24.1113 8.12927L23.9648 8.58826C23.2715 10.8929 23.2129 11.1469 23.3203 11.391C23.5156 11.8207 23.8086 11.7914 25.3711 11.2054L26.8359 10.6488L28.1055 11.5375C28.8379 12.0453 29.4727 12.4164 29.6094 12.4164C29.7559 12.4164 30 12.2601 30.2637 11.9867L30.6836 11.5668L30.6152 10.3558L30.5469 9.1449L31.1133 8.69568C31.4355 8.45154 31.8945 8.10974 32.1484 7.94373L32.6074 7.63123V6.9574C32.6074 6.44958 32.5586 6.24451 32.4414 6.13708C32.3535 6.05896 31.7383 5.84412 31.0938 5.6781L29.9121 5.3656L29.4434 4.07654C29.0137 2.8656 28.9648 2.77771 28.5156 2.42615C28.2031 2.18201 27.9492 2.06482 27.7344 2.06482C27.4512 2.06482 27.373 2.15271 26.543 3.43201ZM28.0176 5.07263C28.2031 5.59021 28.3887 6.06873 28.4375 6.12732C28.4766 6.18591 28.9844 6.36169 29.541 6.50818C30.1074 6.66443 30.5664 6.81091 30.5664 6.84998C30.5664 6.88904 30.1953 7.2113 29.7363 7.57263C29.2773 7.92419 28.8965 8.27576 28.8965 8.34412C28.8867 8.40271 28.8867 8.93982 28.9062 9.52576C28.9258 10.3949 28.9062 10.5707 28.7988 10.5023C28.7207 10.4633 28.3301 10.1898 27.9199 9.90662C27.5 9.61365 27.0605 9.38904 26.9238 9.38904C26.7871 9.38904 26.2793 9.54529 25.7812 9.7406C25.2832 9.92615 24.8633 10.0726 24.8438 10.0531C24.8242 10.0336 24.9414 9.58435 25.0977 9.04724C25.2637 8.5199 25.3906 8.01208 25.3906 7.92419C25.3906 7.82654 25.0781 7.36755 24.6973 6.88904L24.0039 6.0199L25.1074 5.9906C26.3867 5.9613 26.3281 5.9906 27.0605 4.88708C27.334 4.46716 27.5879 4.1156 27.627 4.1156C27.6562 4.1156 27.832 4.54529 28.0176 5.07263Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M13.8965 10.2875C13.789 10.4144 13.3593 11.0297 12.9492 11.6547L12.2168 12.807L11.455 12.8168C9.93161 12.8363 8.88669 12.9437 8.78903 13.1C8.56442 13.4515 8.70114 13.7445 9.66794 14.9554C10.205 15.6293 10.6347 16.2152 10.6152 16.2738C10.5957 16.3226 10.3906 17.016 10.1562 17.807C9.74606 19.184 9.7363 19.2523 9.90231 19.4867C10.1367 19.8578 10.4882 19.809 12.0117 19.2133L13.33 18.6957L14.6093 19.5746C15.4394 20.141 15.9863 20.4437 16.1621 20.4437C16.3281 20.4437 16.5625 20.3168 16.8164 20.0726L17.207 19.6918L17.1484 18.4515L17.0996 17.2113L17.6367 16.7719C17.9297 16.5277 18.3886 16.1957 18.6523 16.0199C19.1406 15.7074 19.1406 15.6976 19.1504 15.1605C19.1699 14.1644 19.1601 14.1449 18.1543 13.8715C17.7539 13.764 17.207 13.6078 16.9433 13.5394L16.4453 13.4027L16.0254 12.2015C15.6347 11.098 15.5566 10.9613 15.0976 10.5414C14.5312 10.014 14.209 9.95544 13.8965 10.2875ZM14.5507 13.1097C14.7656 13.725 14.9707 14.1449 15.0879 14.223C15.1953 14.2914 15.6543 14.4476 16.1132 14.5746C16.5722 14.6918 17.0019 14.8187 17.0703 14.848C17.1484 14.8969 16.875 15.1605 16.3086 15.5902L15.4199 16.2543L15.4492 17.4554L15.4785 18.6566L15.2343 18.5004C15.0976 18.4125 14.6875 18.1293 14.3164 17.8656C13.9453 17.6117 13.5742 17.3969 13.4863 17.3969C13.3984 17.3969 12.8906 17.5726 12.3535 17.7777C11.8164 17.9828 11.3672 18.1488 11.3574 18.139C11.3476 18.1293 11.4648 17.6898 11.6211 17.1527C11.7871 16.6254 11.914 16.098 11.914 15.9906C11.914 15.8734 11.6113 15.4047 11.2402 14.9457L10.5664 14.1058L11.2988 14.0375C11.709 14.0082 12.2363 13.9789 12.4609 13.9789H12.8906L13.4961 13.0511C13.8379 12.5433 14.1308 12.1234 14.1601 12.1234C14.1894 12.1234 14.3652 12.5726 14.5507 13.1097Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M38.457 10.6586C38.3691 10.7172 37.9199 11.3519 37.4512 12.0746L36.6016 13.3929H35.0684C33.3496 13.3929 33.1055 13.4613 33.1055 13.9789C33.1055 14.1937 33.3496 14.5648 34.082 15.4828L35.0684 16.7035L34.6191 18.1879C34.1309 19.7797 34.1211 20.0629 34.5215 20.2484C34.7363 20.3461 34.9805 20.2777 36.2695 19.7992L37.7832 19.2328L39.0723 20.1215C39.7852 20.6097 40.4688 21.0101 40.5859 21.0101C40.7129 21.0101 40.9668 20.8441 41.2109 20.5902L41.6211 20.1801L41.5723 18.9301L41.5234 17.6898L42.2754 17.1234C42.6953 16.8207 43.1543 16.4789 43.291 16.3715C43.5254 16.1957 43.5547 16.1078 43.5742 15.5023C43.5938 14.9652 43.5645 14.809 43.4277 14.7113C43.3301 14.6429 42.7441 14.4476 42.1094 14.2719C41.4844 14.1058 40.9375 13.9301 40.8887 13.891C40.8496 13.8519 40.6348 13.2855 40.4004 12.6312C40 11.5082 39.9512 11.4203 39.4629 11.0004C38.9453 10.5511 38.75 10.4926 38.457 10.6586ZM39.4336 14.682C39.5215 14.7797 40.0391 14.9652 40.5762 15.1117C41.1133 15.2484 41.5625 15.3754 41.582 15.3949C41.6016 15.4047 41.2109 15.7367 40.7227 16.1273L39.834 16.8207L39.8633 17.9926C39.8828 18.6273 39.8828 19.1547 39.8633 19.1547C39.8438 19.1547 39.4531 18.891 38.9941 18.5687C38.457 18.1976 38.0469 17.9828 37.8809 17.9828C37.7344 17.9828 37.207 18.139 36.709 18.3246C36.2207 18.5199 35.8008 18.6566 35.791 18.6469C35.7715 18.6273 35.8887 18.1976 36.0449 17.6898C36.2012 17.182 36.3281 16.6644 36.3281 16.5375C36.3281 16.4105 36.0449 15.9711 35.6445 15.4633L34.9609 14.6136L36.0547 14.5648C36.6602 14.5355 37.2168 14.4769 37.2852 14.4379C37.3535 14.389 37.6758 13.9594 38.0078 13.4613L38.6035 12.5726L38.9355 13.5492C39.1211 14.0765 39.3457 14.5941 39.4336 14.682Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.6155 13.7738C25.0882 14.0179 24.4827 14.6527 24.18 15.2777C24.0433 15.5609 23.7796 16.3519 23.5941 17.0551C22.6175 20.6488 20.7816 23.9008 17.3831 28.0023C16.9437 28.5297 16.3968 29.2133 16.182 29.5258L15.7913 30.0726L15.5765 29.6722C15.1859 28.9105 14.0726 28.5394 13.1839 28.8715C12.4027 29.1547 12.0706 29.2035 9.12141 29.4574C5.54719 29.7601 5.58625 29.7504 5.44953 30.0433C5.19563 30.6 5.61555 31.0199 6.32844 30.9222C6.86555 30.8441 11.1722 30.4926 11.2015 30.5219C11.221 30.5414 11.0355 30.9613 10.8011 31.4691C8.98469 35.3656 8.555 38.598 9.48273 41.3715C9.81477 42.348 10.1761 43.0316 10.7523 43.764C11.0062 44.0961 11.0941 44.2719 11.0159 44.2914C10.9476 44.3207 9.85383 44.4281 8.59406 44.5453C6.89484 44.7015 6.25031 44.7992 6.13313 44.9164C5.89875 45.1215 5.90852 45.5511 6.16242 45.7758C6.35773 45.9515 6.41633 45.9515 8.13508 45.7855C13.5355 45.2875 14.3167 45.1996 14.5609 45.0726C14.8148 44.9457 14.8636 44.9652 15.3519 45.2972C16.0062 45.7367 18.262 46.85 19.5609 47.3676C22.4515 48.5199 25.5863 49.1254 28.555 49.1351C31.7288 49.1351 34.4339 48.5004 37.1097 47.1234C39.1605 46.059 40.3909 44.9359 41.182 43.3832C41.5042 42.7484 41.553 42.5531 41.5823 41.8597C41.6312 40.8929 41.4163 40.3461 40.762 39.7797L40.3421 39.3988L40.7523 39.2035C41.3284 38.9203 43.0472 37.6215 43.4476 37.1722C43.9945 36.5472 44.1898 36.0101 44.1898 35.1215C44.1898 34.3207 43.9847 33.7347 43.6038 33.4125C43.4671 33.2953 43.4769 33.2562 43.7308 32.9828C44.6585 32.0062 44.7659 30.4144 43.9945 29.2719L43.7405 28.9008L44.0335 28.5687C44.346 28.2172 44.6292 27.4261 44.6292 26.9183C44.6292 25.9515 43.9554 24.9359 43.0179 24.4769C41.6702 23.8129 39.8929 24.1156 34.9515 25.8441C30.9866 27.2406 29.5902 27.6215 29.5902 27.3285C29.5902 27.2504 29.7269 26.1371 29.9027 24.8578C30.1859 22.7777 30.2152 22.2699 30.2152 20.1312C30.2152 17.9828 30.1956 17.6605 30.0101 16.9867C29.5511 15.3461 28.6917 14.1644 27.6761 13.7836C27.0023 13.5394 26.1624 13.5297 25.6155 13.7738ZM27.2464 14.9945C27.6859 15.2875 28.0765 16.0004 28.3304 16.9769C28.4866 17.5629 28.5355 18.0511 28.5452 19.1058C28.555 21.1664 28.223 23.1976 27.305 26.7133C26.7581 28.7836 26.7874 28.9203 27.803 28.9203C29.3167 28.9203 31.5628 28.3539 35.1566 27.0746C38.0765 26.0297 39.2581 25.6781 40.6155 25.4535C41.4652 25.307 41.68 25.307 42.0609 25.4144C42.6468 25.5707 43.2327 26.098 43.3597 26.5668C43.7308 27.9437 42.1585 28.7836 38.4964 29.1937C36.8558 29.3793 36.5238 29.4965 36.5238 29.9066C36.5238 30.5511 36.7874 30.6097 38.7113 30.3851C40.4886 30.1703 40.9476 30.0824 41.9144 29.7797C42.5687 29.5746 42.5784 29.5648 42.7835 29.7601C43.057 30.0043 43.223 30.3461 43.3011 30.8246C43.3597 31.1371 43.3109 31.3226 43.096 31.7523C42.6468 32.641 42.4027 32.7386 38.721 33.5101C37.6468 33.7347 36.6702 33.9789 36.553 34.057C36.387 34.1547 36.3284 34.2914 36.3284 34.5258C36.3284 34.8969 36.514 35.1019 36.846 35.0922C37.0804 35.0922 40.4886 34.389 41.6898 34.0961L42.4027 33.9105L42.637 34.1644C43.1741 34.7504 43.1448 35.5707 42.5589 36.2836C42.2659 36.6449 41.1038 37.5629 40.3812 38.0219C39.9905 38.266 37.3734 39.0765 36.9827 39.0765C36.5335 39.0765 36.221 39.809 36.5433 40.1312C36.6995 40.2875 37.3343 40.2777 37.9886 40.1019C38.6331 39.9261 39.0433 40.014 39.639 40.4437C40.1566 40.8246 40.4495 41.2933 40.3714 41.6156C40.2738 42.016 39.6683 42.7582 39.0433 43.2269C37.5687 44.3402 35.1468 45.1117 31.4749 45.6683C28.5745 46.1078 23.3304 45.4242 20.0101 44.1742C18.3206 43.5394 16.6214 42.6117 15.557 41.7523C15.1077 41.3812 14.7171 41.3226 14.4339 41.5863C14.1898 41.8011 14.2093 42.0355 14.512 42.5531C14.8343 43.1 14.7562 43.5004 14.2679 43.8324C12.9788 44.7015 11.0745 42.9633 10.4495 40.3265C10.1956 39.2621 10.1761 37.5336 10.4007 36.3519C10.7523 34.5453 12.0023 31.4398 12.7054 30.639C13.3792 29.8773 14.2581 29.7406 14.5413 30.3461C14.6976 30.6976 14.6683 32.0355 14.5023 32.3383C14.2777 32.7386 14.3167 33.1879 14.5999 33.3734C15.0589 33.6761 15.2542 33.5101 15.9183 32.3187C16.7288 30.8441 17.2073 30.141 18.223 28.9203C21.6995 24.7406 23.6624 21.3226 24.6585 17.7386C25.2249 15.7269 25.4202 15.307 26.0159 14.9457C26.4261 14.6918 26.8363 14.7113 27.2464 14.9945Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M6.53369 32.6211C6.25049 32.9043 6.15283 33.3437 6.34815 33.4609C6.40674 33.4902 6.4458 33.6074 6.4458 33.7148C6.4458 34.3594 7.62744 34.6523 8.12549 34.1152C8.53565 33.6953 8.52588 33.041 8.11572 32.6211C7.87158 32.3769 7.7544 32.3379 7.32471 32.3379C6.89502 32.3379 6.77783 32.3769 6.53369 32.6211Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default UsabilityIcon;
