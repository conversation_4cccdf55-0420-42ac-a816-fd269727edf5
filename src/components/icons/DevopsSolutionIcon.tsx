import React from 'react';

const DevopsSolutionIcon = ({ fill = 'white' }) => {
    return (
        <svg
            height="21"
            width="20"
            fill="none"
            viewBox="0 0 20 21"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_964_647383)">
                <path
                    d="M19.9998 10.0833C19.9978 9.5215 19.8689 8.96737 19.6227 8.46232C19.3765 7.95728 19.0194 7.51437 18.5781 7.16667C19.1887 6.68535 19.6338 6.02545 19.8513 5.27904C20.0689 4.53262 20.048 3.73691 19.7917 3.00291C19.5353 2.26891 19.0563 1.63323 18.4213 1.18455C17.7864 0.735863 17.0272 0.496555 16.2498 0.500006H3.74977C2.9723 0.496555 2.21314 0.735863 1.5782 1.18455C0.943252 1.63323 0.464191 2.26891 0.207842 3.00291C-0.0485068 3.73691 -0.0693587 4.53262 0.148195 5.27904C0.36575 6.02545 0.81086 6.68535 1.42143 7.16667C0.978552 7.51298 0.620354 7.9556 0.374004 8.46096C0.127655 8.96631 -0.000375238 9.52114 -0.000375238 10.0833C-0.000375238 10.6455 0.127655 11.2004 0.374004 11.7057C0.620354 12.2111 0.978552 12.6537 1.42143 13C0.81086 13.4813 0.36575 14.1412 0.148195 14.8876C-0.0693587 15.6341 -0.0485068 16.4298 0.207842 17.1638C0.464191 17.8978 0.943252 18.5335 1.5782 18.9821C2.21314 19.4308 2.9723 19.6701 3.74977 19.6667H16.2498C17.0272 19.6701 17.7864 19.4308 18.4213 18.9821C19.0563 18.5335 19.5353 17.8978 19.7917 17.1638C20.048 16.4298 20.0689 15.6341 19.8513 14.8876C19.6338 14.1412 19.1887 13.4813 18.5781 13C19.0194 12.6523 19.3765 12.2094 19.6227 11.7044C19.8689 11.1993 19.9978 10.6452 19.9998 10.0833ZM1.66643 4.25001C1.66643 3.69747 1.88593 3.16757 2.27663 2.77687C2.66733 2.38617 3.19723 2.16667 3.74977 2.16667H4.16643V3.00001C4.16643 3.22102 4.25423 3.43298 4.41051 3.58926C4.56679 3.74554 4.77875 3.83334 4.99977 3.83334C5.22078 3.83334 5.43274 3.74554 5.58902 3.58926C5.7453 3.43298 5.8331 3.22102 5.8331 3.00001V2.16667H7.49977V3.00001C7.49977 3.22102 7.58756 3.43298 7.74384 3.58926C7.90012 3.74554 8.11209 3.83334 8.3331 3.83334C8.55411 3.83334 8.76607 3.74554 8.92235 3.58926C9.07863 3.43298 9.16643 3.22102 9.16643 3.00001V2.16667H16.2498C16.8023 2.16667 17.3322 2.38617 17.7229 2.77687C18.1136 3.16757 18.3331 3.69747 18.3331 4.25001C18.3331 4.80254 18.1136 5.33245 17.7229 5.72315C17.3322 6.11385 16.8023 6.33334 16.2498 6.33334H3.74977C3.19723 6.33334 2.66733 6.11385 2.27663 5.72315C1.88593 5.33245 1.66643 4.80254 1.66643 4.25001ZM18.3331 15.9167C18.3331 16.4692 18.1136 16.9991 17.7229 17.3898C17.3322 17.7805 16.8023 18 16.2498 18H3.74977C3.19723 18 2.66733 17.7805 2.27663 17.3898C1.88593 16.9991 1.66643 16.4692 1.66643 15.9167C1.66643 15.3641 1.88593 14.8342 2.27663 14.4435C2.66733 14.0528 3.19723 13.8333 3.74977 13.8333H4.16643V14.6667C4.16643 14.8877 4.25423 15.0996 4.41051 15.2559C4.56679 15.4122 4.77875 15.5 4.99977 15.5C5.22078 15.5 5.43274 15.4122 5.58902 15.2559C5.7453 15.0996 5.8331 14.8877 5.8331 14.6667V13.8333H7.49977V14.6667C7.49977 14.8877 7.58756 15.0996 7.74384 15.2559C7.90012 15.4122 8.11209 15.5 8.3331 15.5C8.55411 15.5 8.76607 15.4122 8.92235 15.2559C9.07863 15.0996 9.16643 14.8877 9.16643 14.6667V13.8333H16.2498C16.8023 13.8333 17.3322 14.0528 17.7229 14.4435C18.1136 14.8342 18.3331 15.3641 18.3331 15.9167ZM3.74977 12.1667C3.19723 12.1667 2.66733 11.9472 2.27663 11.5565C1.88593 11.1658 1.66643 10.6359 1.66643 10.0833C1.66643 9.53081 1.88593 9.0009 2.27663 8.6102C2.66733 8.2195 3.19723 8.00001 3.74977 8.00001H4.16643V8.83334C4.16643 9.05435 4.25423 9.26631 4.41051 9.4226C4.56679 9.57888 4.77875 9.66667 4.99977 9.66667C5.22078 9.66667 5.43274 9.57888 5.58902 9.4226C5.7453 9.26631 5.8331 9.05435 5.8331 8.83334V8.00001H7.49977V8.83334C7.49977 9.05435 7.58756 9.26631 7.74384 9.4226C7.90012 9.57888 8.11209 9.66667 8.3331 9.66667C8.55411 9.66667 8.76607 9.57888 8.92235 9.4226C9.07863 9.26631 9.16643 9.05435 9.16643 8.83334V8.00001H16.2498C16.8023 8.00001 17.3322 8.2195 17.7229 8.6102C18.1136 9.0009 18.3331 9.53081 18.3331 10.0833C18.3331 10.6359 18.1136 11.1658 17.7229 11.5565C17.3322 11.9472 16.8023 12.1667 16.2498 12.1667H3.74977Z"
                    fill={fill}
                />
            </g>
            <defs>
                <clipPath id="clip0_964_647383">
                    <rect height="20" width="20" fill={fill} transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default DevopsSolutionIcon;
