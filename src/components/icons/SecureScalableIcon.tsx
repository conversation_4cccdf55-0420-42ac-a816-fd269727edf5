import React from 'react';

const SecureScalableIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M26.9609 9.49342C26.7749 9.59186 25.889 10.1715 24.9921 10.784C22.7171 12.3153 21.4921 13.0153 19.939 13.6606C17.3249 14.7653 15.2468 15.3122 13.7265 15.3122C12.8624 15.3122 12.753 15.4215 12.7093 16.4715C12.6765 16.92 12.6218 17.2809 12.578 17.2809C12.3812 17.2809 8.23585 16.395 8.02804 16.3075C7.90773 16.2528 7.42648 15.6184 6.9671 14.8965L6.11398 13.584L6.3546 13.1247C7.27335 11.3747 6.03742 9.50436 4.05773 9.64655C3.21554 9.70124 2.23117 10.1934 1.93585 10.6965C1.24679 11.8669 1.65148 13.3981 2.83273 14.0872C3.25929 14.3387 3.44523 14.3715 4.15617 14.3606L4.98742 14.3497L5.89523 15.7606C6.39835 16.5372 6.94523 17.27 7.10929 17.3903C7.3171 17.5325 8.29054 17.795 9.99679 18.1559C11.4296 18.4512 12.6327 18.7247 12.6765 18.7465C12.7202 18.7794 12.7968 19.534 12.8296 20.42C12.9499 23.1653 13.3765 25.8778 14.0655 28.2403C14.1202 28.4262 13.9562 28.4372 11.7359 28.4372L9.35148 28.4262L9.0671 28.0106C8.23585 26.8184 6.12492 26.6762 5.11867 27.7372C4.48429 28.4153 4.30929 29.509 4.6921 30.3622C5.05304 31.1497 6.11398 31.7622 7.10929 31.7512C8.1921 31.7403 9.02335 31.2153 9.36242 30.3512C9.53742 29.8919 9.53742 29.8919 10.1937 29.8153C10.5655 29.7825 11.6812 29.7497 12.6874 29.7497H14.514L15.0937 31.2481C15.7718 33.02 16.8984 35.284 17.8171 36.7497L18.4734 37.7887L17.9593 37.8653C17.6749 37.8981 16.7015 37.9419 15.7827 37.9419C14.8749 37.9528 13.989 38.0075 13.814 38.0731C13.6499 38.1278 12.928 38.7512 12.228 39.4512C10.9921 40.6762 10.9484 40.72 10.6421 40.5997C9.30773 40.0856 7.99523 40.6215 7.43742 41.9122C7.24054 42.3606 7.20773 42.6122 7.25148 43.0825C7.32804 43.7825 7.52492 44.0887 8.26867 44.6465C9.38429 45.4997 10.664 45.4559 11.5937 44.5262C12.3374 43.7825 12.5015 42.9622 12.1187 41.9669C11.9984 41.6606 12.0421 41.6169 13.1905 40.4575L14.3827 39.2653H16.964H19.5562L20.3327 40.1731C22.4765 42.7106 25.7796 45.4231 27.2999 45.9153C29.214 46.5387 31.0405 46.7575 33.2499 46.659C35.5905 46.5606 37.078 46.2106 37.078 45.784C37.078 45.259 35.6562 44.9419 32.9874 44.8544L30.9312 44.7887L31.7296 44.2419C33.1405 43.2794 34.7921 41.8575 36.203 40.3919L37.5702 38.97L40.0093 39.2762C41.3655 39.4403 42.514 39.6262 42.6015 39.7028C42.6999 39.7794 43.1812 40.37 43.6843 41.0153L44.603 42.1965L44.3843 42.6559C43.7499 43.9356 44.4937 45.5434 45.8937 45.9372C47.064 46.2653 48.7155 45.5106 49.0984 44.4825C49.514 43.3997 48.9015 41.9122 47.8624 41.4419C47.3812 41.2231 46.4515 41.1684 46.003 41.3325C45.7515 41.4309 45.6859 41.3762 44.8874 40.3262C43.8374 38.9481 43.564 38.6309 43.1702 38.434C42.9405 38.3137 39.1562 37.734 38.5765 37.734C38.5327 37.734 38.7843 37.3075 39.1452 36.7715C40.5671 34.6497 41.628 32.4512 42.3827 30.045L42.7765 28.7653H45.653H48.5405L48.6171 29.0606C48.978 30.5153 50.6843 31.1169 52.2265 30.3403C53.5718 29.6622 53.9546 28.2403 53.1452 26.9715C52.2265 25.5059 49.6234 25.6153 48.8249 27.1465L48.6609 27.4528H45.8718H43.0718L43.1265 27.2012C43.564 25.4512 43.7937 22.7387 43.728 19.8403C43.6843 17.6637 43.6952 17.3903 43.8484 17.3356C43.9468 17.3028 44.8327 17.2919 45.828 17.3137C47.1843 17.3247 47.6984 17.3028 47.9062 17.1825C48.0593 17.1059 48.7265 16.5044 49.3937 15.8481L50.5968 14.645L51.253 14.82C51.6468 14.9184 52.0734 14.9622 52.3359 14.9184C53.703 14.7106 54.6327 13.3434 54.3374 11.9653C54.2062 11.3419 53.6484 10.7622 52.7843 10.3356C52.2812 10.095 52.0624 10.0403 51.5812 10.084C50.8374 10.1497 50.0718 10.5872 49.689 11.1669C49.328 11.7247 49.2296 12.6653 49.4702 13.2559L49.6343 13.6387L48.464 14.809L47.3046 15.9684H45.128C43.0827 15.9684 42.9405 15.9575 42.6234 15.7278C42.4265 15.5965 42.1859 15.3997 42.0874 15.3012C41.978 15.1919 41.7046 15.0715 41.4859 15.0387C36.914 14.295 32.4405 12.5231 28.5249 9.90905C28.0218 9.56998 27.5405 9.29655 27.453 9.29655C27.3655 9.30749 27.1359 9.39499 26.9609 9.49342ZM27.8249 11.0794C28.0109 11.2215 28.6562 11.6372 29.2577 11.9981C32.4405 13.9122 36.3015 15.4215 39.7359 16.0887L40.9937 16.3403L41.0812 17.3794C41.1905 18.8559 41.0593 22.6184 40.8624 23.8762C40.3155 27.3106 39.178 30.8544 37.8109 33.359C35.7109 37.2309 32.3093 40.895 28.3827 43.5309C27.7593 43.9575 27.1796 44.2965 27.114 44.2965C27.0484 44.2965 26.6655 44.0669 26.2609 43.7825C19.8296 39.2325 15.5093 31.6637 14.3718 22.9575C14.2843 22.2137 14.1749 20.4856 14.1421 19.1294L14.0765 16.6575L14.9187 16.5919C16.3734 16.4606 17.9812 15.9903 20.4312 14.9403C22.2468 14.1747 23.4062 13.5184 25.6374 12.02C26.6109 11.3637 27.4202 10.8278 27.4421 10.8278C27.464 10.8278 27.639 10.9481 27.8249 11.0794ZM4.82335 11.1231C5.67648 11.6262 5.37023 12.8075 4.3421 12.9825C4.00304 13.0372 3.9046 12.9934 3.60929 12.709C3.16085 12.2497 3.14992 11.7028 3.59835 11.2544C3.97023 10.8934 4.35304 10.8497 4.82335 11.1231ZM52.4452 11.7575C52.9265 12.2278 52.7515 13.0919 52.1171 13.3872C51.439 13.7153 50.739 13.2122 50.7171 12.3919C50.6952 11.5715 51.8546 11.1559 52.4452 11.7575ZM51.428 27.4637C51.5484 27.5294 51.7124 27.7481 51.7999 27.9669C51.953 28.3059 51.9421 28.3825 51.7671 28.7434C51.5046 29.2794 51.089 29.4762 50.5859 29.3122C49.8421 29.0715 49.6452 28.295 50.1593 27.6825C50.4655 27.3215 50.9796 27.2231 51.428 27.4637ZM7.63429 28.5465C8.26867 28.9294 8.37804 29.5965 7.88585 30.0887C7.42648 30.5481 6.80304 30.5372 6.40929 30.0669C6.25617 29.8809 6.12492 29.5965 6.12492 29.4325C6.12492 28.9512 6.68273 28.3606 7.16398 28.3387C7.2296 28.3278 7.43742 28.4262 7.63429 28.5465ZM10.4015 42.0325C11.2984 42.634 10.9702 43.859 9.92023 43.859C9.42804 43.859 8.85929 43.3887 8.85929 42.9622C8.85929 42.6122 9.2421 42.0106 9.54835 41.8903C9.90929 41.7481 9.99679 41.759 10.4015 42.0325ZM47.3593 43.0606C48.1687 44.0997 46.6155 45.2809 45.7734 44.2747C45.4562 43.9028 45.4343 43.6512 45.6749 43.1919C46.014 42.5137 46.878 42.459 47.3593 43.0606Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M25.7356 15.4655C23.0668 16.3405 22.2137 19.7967 24.1606 21.9077C25.6262 23.4936 28.5574 23.4061 30.2418 21.7108C31.4012 20.5623 31.6637 18.9217 30.9418 17.4233C30.6356 16.7998 29.8809 16.1217 29.0059 15.717C28.3715 15.4217 28.1309 15.367 27.234 15.3452C26.5778 15.3233 26.0309 15.367 25.7356 15.4655ZM28.4153 16.953C29.0059 17.2045 29.684 17.8498 29.859 18.3311C30.1653 19.1186 29.8371 20.278 29.1699 20.8467C27.1684 22.542 24.4778 21.5577 24.4778 19.1295C24.4778 18.3858 24.8715 17.5655 25.4074 17.1717C26.1949 16.5811 27.3653 16.5045 28.4153 16.953Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M26.326 23.8547C22.8479 24.3141 20.1026 26.6656 18.4073 30.625C17.9151 31.7734 17.926 31.8828 18.6807 32.6266L19.3151 33.25H27.912C35.8198 33.25 36.5307 33.2391 36.6948 33.0641C36.9573 32.8125 36.8698 32.3422 36.3229 30.9313C35.7979 29.6078 35.0214 28.2516 34.2776 27.3766C33.5448 26.5125 31.576 24.9703 30.6792 24.5437C29.3339 23.8984 27.7917 23.6578 26.326 23.8547ZM28.9292 25.3203C31.0839 25.8891 32.7245 27.6391 33.9167 30.6469L34.1354 31.2047L30.176 31.1391C27.9995 31.0953 24.7401 31.0625 22.9464 31.0625H19.676L20.1682 30.1C21.437 27.6609 23.4495 25.9219 25.7026 25.3313C26.512 25.1125 28.1198 25.1125 28.9292 25.3203Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default SecureScalableIcon;
