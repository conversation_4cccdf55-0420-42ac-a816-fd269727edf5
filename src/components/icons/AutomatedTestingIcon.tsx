import React from 'react';

const AutomatedTestingIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M23.584 4.06248C18.8379 4.55076 14.8633 6.13279 11.6699 8.80857C7.12891 12.5976 4.58984 18.2324 4.58984 24.5117C4.58984 31.9043 8.23242 38.291 14.1309 41.25L14.834 41.6015L14.6777 42.041C14.4824 42.6172 14.541 43.8574 14.7852 44.3457C15.0195 44.834 15.5273 45.3222 16.0742 45.5957C16.4355 45.7812 16.7285 45.8301 17.5391 45.8691C18.4668 45.8984 18.5938 45.8789 19.043 45.6543C19.7461 45.3027 20.1758 44.8828 20.4785 44.2578C21.1719 42.8418 20.4688 41.0058 18.9258 40.2441C17.7344 39.6484 16.4258 39.8926 15.4395 40.9082L14.9805 41.3867L13.9941 40.6836C6.41602 35.332 3.75977 26.6113 7.09961 18.0664C10.1758 10.2051 17.5586 5.05857 25.2441 5.40037C30.5957 5.64451 35.8398 8.12498 39.2676 12.041C44.4531 17.959 45.4004 26.5625 41.6504 33.6719C39.6973 37.3633 36.7481 40.1855 32.8906 42.041C31.2793 42.8125 30.2344 43.1738 28.6914 43.5058C27.1289 43.8476 26.2891 43.8672 25.9082 43.5937C25.7617 43.4863 25.5859 43.2812 25.5273 43.1543C25.459 43.0176 25.3613 41.8261 25.3027 40.2832C25.2441 38.8281 25.166 37.4121 25.1367 37.1484L25.0781 36.6504L25.8398 36.5918C28.5059 36.3769 31.0352 34.5215 32.2754 31.8847C33.0762 30.2051 33.1641 29.3066 32.8809 25.9765C32.6758 23.5644 32.627 23.1445 32.5 23.1445C32.4512 23.1445 32.4414 22.998 32.4707 22.8125C32.6172 22.0508 32.2754 21.8066 30.957 21.709C30.4785 21.6797 30.0781 21.6211 30.0781 21.5918C30.0781 21.5625 30.1465 21.289 30.2246 20.9961C30.5957 19.6289 30.6738 14.3066 30.332 13.5254C29.9707 12.6855 29.248 12.3144 28.1738 12.4316C27.2559 12.5293 26.6699 12.9394 26.4648 13.6621C26.2793 14.3164 26.5625 20.3711 26.8164 21.2207C26.875 21.3965 26.7773 21.4062 24.873 21.3672C23.7695 21.3574 22.8418 21.3183 22.8223 21.289C22.793 21.2597 22.8418 20.9277 22.9297 20.5566C23.0469 20.0195 23.0859 19.2187 23.0859 16.8457L23.0957 13.8183L22.8516 13.3398C22.5098 12.666 22.0703 12.4511 21.0449 12.4511C20.0391 12.4511 19.5801 12.666 19.2578 13.291C19.0234 13.7207 19.0234 13.7207 19.082 16.6992C19.1113 18.7695 19.1797 19.9121 19.2773 20.459C19.3652 20.8886 19.4336 21.25 19.4336 21.2597C19.4336 21.2793 19.082 21.289 18.6426 21.289C17.1973 21.289 16.5625 21.6211 16.3965 22.4804C16.2793 23.0859 16.2891 26.8945 16.416 28.0078C16.6211 29.9219 17.168 31.7578 17.8809 32.9785C18.0664 33.291 18.584 33.916 19.043 34.375C19.7363 35.0781 20.0293 35.2929 20.8008 35.6738C21.8262 36.1719 22.9883 36.4941 24.1504 36.5918C24.7852 36.6406 24.9023 36.6797 24.8633 36.8066C24.668 37.4414 24.3555 42.1191 24.4531 42.9101C24.5313 43.5937 24.6875 43.9258 25.1367 44.3066C25.6543 44.7656 26.0742 44.873 27.3438 44.8633C29.0332 44.8437 31.1621 44.2969 33.457 43.291C39.7656 40.5371 44.3945 34.375 45.3516 27.4511C45.5371 26.1816 45.5566 23.3008 45.4004 22.0703C45.2539 20.8301 44.9414 19.4043 44.5801 18.3105C42.1777 10.9668 35.6055 5.41014 27.9492 4.24803C27.002 4.10154 24.3164 3.98436 23.584 4.06248ZM21.4648 13.9844C21.543 14.0625 21.582 14.5703 21.582 15.6152C21.582 17.1777 21.7285 20.1269 21.8359 20.8691L21.8945 21.289H20.9961H20.0977L20.1563 20.8691C20.1855 20.6445 20.2637 19.0332 20.3125 17.2851C20.3711 15.5371 20.4492 14.082 20.4883 14.0429C20.5566 13.9844 20.8789 13.9062 21.2012 13.8769C21.2793 13.8769 21.3965 13.916 21.4648 13.9844ZM28.8379 14.0039C28.9453 14.1211 28.9844 14.7168 29.0332 17.0605C29.0723 18.6719 29.1504 20.3222 29.2285 20.7715L29.3555 21.5625L28.6621 21.5234C27.4219 21.4355 27.5098 21.5136 27.5781 20.4785C27.6172 19.9804 27.6855 18.3594 27.7344 16.8554C27.7734 15.3613 27.8516 14.1015 27.9004 14.0625C27.9883 13.9746 28.2715 13.8965 28.5352 13.8769C28.623 13.8769 28.7598 13.9258 28.8379 14.0039ZM32.1875 22.539C32.2656 22.5879 32.2754 22.7636 32.2266 23.0957C32.1777 23.3691 32.1191 24.6484 32.0801 25.9277C32.0508 27.2168 31.9727 28.6035 31.9141 29.0039C31.5137 31.9336 29.6875 34.1211 26.9531 34.9707C25.8984 35.3027 24.0918 35.3027 23.0469 34.9707C21.1426 34.3847 19.8926 33.4277 18.916 31.8164C18.0762 30.4297 17.8418 28.9258 17.8906 25.2734L17.9199 22.8515H20.2148C22.6172 22.8515 28.0566 22.6953 30.2246 22.5683C32.0313 22.4609 32.0703 22.4609 32.1875 22.539ZM18.6426 41.7285C19.3555 42.1972 19.6191 43.1836 19.248 43.9941C19.0527 44.4336 18.3691 45.1562 17.9395 45.3808C17.6563 45.5273 17.6465 45.5176 17.1191 45C16.5625 44.4531 16.1816 43.8867 16.0742 43.4277C15.9277 42.8027 16.4746 41.8359 17.1387 41.5332C17.5488 41.3476 18.1934 41.4355 18.6426 41.7285Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default AutomatedTestingIcon;
