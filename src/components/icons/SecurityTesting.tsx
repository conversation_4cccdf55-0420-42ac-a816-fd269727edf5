import React from 'react';

const SecurityTesting = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M24.3162 4.84277C23.8767 5.02832 20.0486 6.92285 14.5799 9.65723C13.8474 10.0283 13.6619 10.165 13.574 10.3994C13.4373 10.7998 13.4373 21.7178 13.5838 22.7334C13.7205 23.7295 14.0818 24.9697 14.5017 25.8291C15.4588 27.8311 16.5428 28.9443 19.7752 31.2393C24.4041 34.5205 24.658 34.6865 24.9998 34.6865C25.2439 34.6865 25.615 34.4814 26.6014 33.7783C27.3045 33.2803 28.8963 32.1572 30.1267 31.2979C31.367 30.4287 32.6267 29.501 32.9392 29.2275C35.0389 27.3916 36.2205 25.0674 36.533 22.1865C36.6111 21.4834 36.6306 19.4814 36.6014 15.7607C36.5525 10.585 36.5428 10.3408 36.3572 10.165C36.2596 10.0576 35.1853 9.50098 33.9842 8.91504C32.7732 8.33887 30.9373 7.43066 29.8924 6.90332C28.115 6.02441 26.6697 5.37012 25.5564 4.9502C25.3025 4.85254 25.0681 4.73535 25.0389 4.69629C24.9705 4.5791 24.9217 4.58887 24.3162 4.84277ZM25.5467 5.28223C26.5428 6.08301 30.2537 8.3877 33.3982 10.1357L35.0096 11.0439L35.0389 15.7119C35.0486 18.2803 35.0193 20.9463 34.9705 21.6494C34.7654 24.2666 33.8963 26.1318 32.0994 27.8213C31.7381 28.1533 29.9998 29.4619 28.2322 30.7314L24.9998 33.0361L23.1248 31.7275C22.0896 31.0146 20.6053 29.9893 19.824 29.4521C17.0408 27.5479 15.8299 26.0146 15.1365 23.5439C14.824 22.4111 14.7459 20.9268 14.7459 15.7412V10.9072L15.5564 10.4775C15.9959 10.2432 17.2556 9.52051 18.3592 8.87598C22.7342 6.32715 24.6092 5.1748 24.6092 5.04785C24.6092 4.94043 24.7264 4.84277 24.9021 4.81348C24.9314 4.80371 25.2244 5.01855 25.5467 5.28223Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M23.1439 11.8641C21.806 12.1864 20.6146 12.8798 19.5404 13.9735C18.681 14.8427 18.3783 15.37 18.0658 16.5223C17.9486 16.9813 17.8314 17.411 17.8119 17.4696C17.7924 17.5477 17.6166 17.5966 17.392 17.5966C17.0795 17.5966 16.9428 17.6649 16.6107 17.9969C16.1029 18.5145 15.9467 19.0126 16.1029 19.7059C16.3178 20.6337 17.1088 21.3075 18.0072 21.3075C19.4623 21.3075 20.4291 19.9794 19.9994 18.5536C19.8627 18.0946 19.3939 17.6063 19.0131 17.5282L18.7396 17.4696L18.974 17.2157C19.101 17.079 19.4037 16.6786 19.6381 16.327C20.1654 15.536 21.2885 14.4325 22.0111 13.993C23.5736 13.0555 25.0092 12.8895 27.4017 13.3876C28.8373 13.6903 29.1986 13.7001 29.1986 13.4559C29.1986 13.1239 27.7728 12.2548 26.806 11.9813C26.015 11.7665 23.8568 11.6981 23.1439 11.8641ZM18.349 18.7489C18.8568 19.4813 18.1342 20.2137 17.5092 19.5887C17.2943 19.3739 17.2748 19.2958 17.3334 18.8856C17.3627 18.6317 17.4603 18.329 17.5385 18.202L17.685 17.9774L17.9096 18.202C18.0267 18.329 18.2221 18.5731 18.349 18.7489Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M31.1621 13.8174C30.3027 14.4229 28.916 16.0146 26.709 18.9639C26.123 19.7451 25.4688 20.6045 25.2441 20.8779L24.8438 21.376L24.2285 20.8096C23.5645 20.2139 22.041 19.2861 21.9922 19.4521C21.9727 19.5107 21.9141 19.5303 21.8652 19.4912C21.8164 19.4619 21.7773 19.5107 21.7773 19.6084C21.7773 19.7939 22.9004 21.2002 23.9062 22.2549C24.4922 22.8799 24.6094 22.9678 24.9219 22.9678C25.2441 22.9678 25.3418 22.8799 26.3379 21.7666C29.2676 18.4756 32.4219 14.208 32.4219 13.5244C32.4219 13.2412 31.7871 13.3877 31.1621 13.8174Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M31.748 17.9287C31.6797 18.0068 31.5527 18.0459 31.4551 18.0166C31.1719 17.9482 30.6055 18.251 30.3125 18.6416C29.9121 19.1689 29.8828 19.9307 30.2441 20.624C30.3906 20.917 30.6543 21.2588 30.8301 21.3662L31.1426 21.5713L30.4688 22.46C28.8086 24.6182 26.9141 25.6924 24.7656 25.7021C22.9688 25.7021 21.4648 24.96 19.5605 23.1338C19.0137 22.6064 18.6523 22.4697 18.6523 22.8018C18.6523 23.085 19.1895 24.1201 19.668 24.7256C20.7617 26.1416 22.998 27.167 25.0098 27.167C27.041 27.167 28.7305 26.4932 30.2637 25.0479C31.0938 24.2764 31.4453 23.71 31.8457 22.5283C32.0801 21.835 32.1387 21.7471 32.3828 21.6885C33.584 21.4443 34.2969 20.2432 33.9551 19.0518C33.8672 18.7588 33.6914 18.417 33.5742 18.2998C33.1152 17.8701 31.9824 17.6455 31.748 17.9287ZM32.4219 19.6182C32.4219 20.2041 31.9043 20.4287 31.4746 20.0186C31.2012 19.7646 31.1816 19.335 31.416 18.749L31.582 18.3389L32.002 18.8174C32.3145 19.1689 32.4219 19.3838 32.4219 19.6182Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M39.8438 15.6237C39.1016 15.6823 37.6367 15.9557 37.3926 16.0827C37.0996 16.2389 37.0117 16.4049 37.0117 16.8053C37.0117 17.3327 38.9258 18.9635 39.541 18.9635C39.7754 18.9635 39.9414 18.6901 39.9414 18.3385C39.9414 18.2018 39.9512 18.0846 39.9707 18.0846C39.9902 18.0846 40.2832 18.2018 40.6348 18.3385C42.4805 19.0807 43.6621 19.9401 44.0332 20.819L44.248 21.317L44.043 21.7565C43.7207 22.4206 42.9688 22.9967 41.5527 23.6901C39.8145 24.5397 39.0723 24.8717 38.6523 25.028C38.4473 25.0963 38.1641 25.2526 38.0176 25.3698C37.7637 25.5748 37.7539 25.6041 37.9004 25.7506C38.2812 26.1413 39.7461 26.0338 41.2598 25.5065C44.5117 24.3834 46.1719 22.4694 45.5957 20.526C45.332 19.6373 44.1797 18.5631 42.793 17.8795C41.9336 17.4596 40.4199 16.9127 40.1172 16.9127C39.8633 16.9127 39.9023 16.7956 40.2344 16.4733C40.4199 16.2975 40.5273 16.0924 40.5273 15.9264C40.5273 15.6334 40.3906 15.5748 39.8438 15.6237Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M10.2539 16.6201C9.03324 16.7763 7.10941 17.5771 5.98637 18.4072C4.53129 19.4912 3.94535 21.0635 4.54105 22.3135C4.81449 22.8799 5.54691 23.6416 6.3184 24.1396C7.04106 24.6181 8.94535 25.4385 9.61918 25.5654L9.99027 25.6338L9.66801 25.8974C9.40434 26.1123 9.35551 26.2002 9.4141 26.415C9.57035 27.0303 10.6543 27.1279 11.9141 26.6299C13.0274 26.2002 13.1739 25.751 12.4317 25.0576C11.1719 23.8857 10.3907 23.5244 10.0098 23.9443C9.86332 24.1103 9.85356 24.1592 9.97074 24.2959C10.0879 24.4424 10.0782 24.4521 9.86332 24.3935C9.7266 24.3545 9.09184 24.0615 8.44731 23.749C7.04106 23.0654 6.2891 22.4404 5.96684 21.6885L5.74223 21.1904L5.9766 20.7314C6.26957 20.1748 7.05082 19.5303 8.01762 19.042C9.30668 18.4072 10.7813 17.7236 11.2403 17.5576C11.4844 17.4599 11.8164 17.2842 11.9727 17.1572C12.2266 16.9521 12.2364 16.9131 12.1094 16.7763C11.9239 16.5908 11.0743 16.5127 10.2539 16.6201Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M37.4513 33.954C33.5743 36.1611 30.547 37.6161 29.1114 37.9482L28.5353 38.0849L28.4767 37.7138C28.3985 37.2353 27.881 36.5517 27.3927 36.2782C27.1778 36.1708 25.5958 35.6728 23.8771 35.1747C20.7521 34.2763 20.7521 34.2763 19.8243 34.3153C18.7696 34.3642 18.4083 34.4911 16.6505 35.4579C15.4298 36.1318 14.756 36.4052 13.047 36.9228L11.836 37.2939L11.7286 36.9228C11.6114 36.5321 11.2501 36.1806 10.8595 36.0829C10.5763 36.0146 9.23839 36.2196 9.19933 36.3466C9.1798 36.3954 9.07238 36.3759 8.95519 36.2978C8.67199 36.122 8.28136 36.2099 7.97863 36.5224C7.50988 37.0107 7.41222 37.8603 7.35363 41.6786C7.32433 43.5732 7.3341 45.3212 7.36339 45.5751C7.45128 46.1415 7.9591 46.747 8.4962 46.9032C8.70128 46.9716 9.28722 47.0009 9.87316 46.9814C10.7228 46.9423 10.9474 46.9032 11.2501 46.7177C11.631 46.4833 12.0118 45.829 12.0118 45.4091C12.0118 45.165 12.0314 45.165 12.3829 45.2333C12.5782 45.2821 13.6036 45.3896 14.6485 45.4775C17.4513 45.7021 20.1954 46.0634 21.7384 46.4052C24.4044 47.0009 25.3907 47.0888 26.7677 46.8446C27.9396 46.6396 28.6818 46.3857 30.1271 45.6728C32.0509 44.7353 34.2189 43.2997 36.6212 41.3759C38.0568 40.2138 41.3185 37.3134 41.9044 36.6689C42.7735 35.6923 42.8126 34.4423 42.0021 33.5048C41.5431 32.9579 41.0255 32.7333 40.2345 32.7333C39.5997 32.7333 39.5997 32.7333 37.4513 33.954ZM40.6349 34.2958C40.7325 34.3544 40.8888 34.5302 40.9767 34.6962C41.211 35.165 41.0353 35.4482 39.6583 36.6689C35.1759 40.6825 32.3048 42.8017 29.336 44.2861C27.129 45.3896 25.7325 45.6142 23.4864 45.2333C21.006 44.8036 20.6349 44.7548 18.8478 44.6376C17.1485 44.5302 13.4278 44.5693 12.4415 44.706L12.0314 44.7646L11.9728 42.9677C11.9435 41.9911 11.9044 40.4091 11.8751 39.4716L11.8458 37.7626L12.9786 37.5282C13.6036 37.4013 14.3165 37.2646 14.5607 37.2157C14.9708 37.1474 15.7521 36.8544 18.213 35.7997C19.5997 35.2138 19.922 35.2431 23.2911 36.2782C24.795 36.747 26.211 37.1962 26.4357 37.2841C27.2071 37.5673 27.4513 38.2021 27.0509 38.8564C26.7872 39.2958 26.4845 39.3446 25.6446 39.0907C24.6583 38.7978 23.0177 38.495 22.3927 38.495C21.7579 38.495 21.045 38.6611 20.8888 38.8466C20.7228 39.0419 20.879 39.3544 21.2696 39.6181C21.6505 39.8622 22.4025 40.0282 24.5021 40.3505C26.0743 40.5849 26.4552 40.5946 26.9239 40.3993C27.3243 40.2333 28.2814 39.6181 28.3693 39.4716C28.3985 39.413 28.5743 39.3739 28.7403 39.3739C29.1798 39.3739 30.3224 39.0419 31.5431 38.5732C32.6564 38.1337 36.4161 36.2489 38.672 34.9892C40.1173 34.1884 40.3028 34.12 40.6349 34.2958ZM9.98058 36.7861C10.2833 36.8153 10.5763 36.9032 10.6251 36.9618C10.7325 37.1083 10.5665 45.2431 10.4591 45.3603C10.3517 45.4677 9.16027 45.4872 9.00402 45.3896C8.91613 45.3407 8.88683 44.6181 8.88683 42.7626C8.87706 41.3564 8.81847 39.5302 8.74035 38.7001C8.67199 37.87 8.63292 37.1278 8.66222 37.04C8.77941 36.7372 8.91613 36.6298 9.17003 36.6786C9.30675 36.7079 9.67785 36.7568 9.98058 36.7861Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default SecurityTesting;
