import React from 'react';

const ApiTestingIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_1961_346423)">
                <path
                    d="M20.4605 0.981331C18.8492 1.03992 18.7808 1.04969 18.1363 1.35242C17.2672 1.76258 16.4957 2.53407 16.0855 3.40321L15.773 4.06727L15.7437 6.53797L15.7144 9.00867L10.4703 8.94032C6.71051 8.89149 5.02106 8.91102 4.49371 8.98914C2.98004 9.22352 1.5445 10.2294 0.890199 11.5184C0.235902 12.8173 0.265199 12.1044 0.382386 25.747C0.44098 32.5145 0.519105 38.286 0.548402 38.5692C0.743714 40.4247 1.92535 41.8993 3.71246 42.5048C4.34723 42.7196 4.357 42.7196 9.86481 42.6513C12.9019 42.622 16.7105 42.5634 18.3414 42.5243L21.2906 42.4657V44.4188C21.2906 45.493 21.3199 46.704 21.3492 47.1044L21.4175 47.8466L20.4507 47.9149C18.9371 48.0126 16.9547 48.2372 15.6754 48.452C14.0347 48.7255 13.9175 48.7645 13.9175 49.038C13.9175 49.243 13.9859 49.2919 14.4547 49.3993C15.3238 49.6044 18.2047 49.995 19.7769 50.1317C21.9449 50.3173 28.5953 50.288 30.7242 50.0829C32.6187 49.8973 34.1617 49.6825 35.3043 49.4481C36.0171 49.2919 36.0855 49.2626 36.0855 49.0477C36.0855 48.7548 35.9781 48.7157 33.9859 48.4032C32.7066 48.1981 29.523 47.8661 28.8492 47.8661C28.6441 47.8661 28.6441 47.8563 28.6929 45.161C28.7125 43.6669 28.7613 42.4266 28.7808 42.3973C28.8101 42.368 32.6089 42.2802 37.2379 42.1825C45.3629 42.0262 45.6754 42.0165 46.2027 41.8212C47.3941 41.372 48.3511 40.4149 48.6929 39.3407C48.9078 38.6571 49.0347 34.3114 49.3179 18.6669C49.4254 12.9149 49.4058 12.622 48.8589 11.6063C48.3023 10.5712 47.1207 9.63367 45.9976 9.34071L45.5582 9.23328L45.5093 6.64539L45.4605 4.06727L45.1578 3.4325C44.7183 2.50477 44.0738 1.85047 43.1949 1.41102C42.4722 1.05946 42.4429 1.04969 40.9293 0.981331C39.1421 0.89344 22.8824 0.903206 20.4605 0.981331ZM42.1695 3.56922C42.5504 3.76453 43.0289 4.32117 43.1461 4.72157C43.2437 5.01453 43.3023 7.22157 43.5269 17.6122L43.5855 20.6981L43.3218 21.245C43.0679 21.7626 42.4136 22.3778 42.1207 22.3778C42.023 22.3778 41.9937 20.6884 41.9742 13.413L41.9449 4.45789H41.7007H41.4566L41.4273 13.5106L41.4078 22.5731H41.1832C41.066 22.5731 38.6441 22.661 35.7925 22.7684C31.5054 22.9345 30.2066 22.954 28.2242 22.8661C26.9058 22.8173 24.4742 22.7294 22.8043 22.6708C21.1148 22.6122 19.5816 22.5243 19.3375 22.4559C18.732 22.2899 17.9312 21.5087 17.775 20.913C17.6871 20.5809 17.6871 19.5067 17.7652 16.3231C17.8238 14.038 17.8922 10.5907 17.9312 8.65711C17.9605 6.72352 18.0289 4.97547 18.0875 4.76063C18.2047 4.29188 18.7906 3.65711 19.2398 3.50086C19.5035 3.41297 22.2379 3.38367 30.7339 3.40321C40.4605 3.42274 41.9156 3.44227 42.1695 3.56922ZM46.1636 11.6356C46.7007 11.9481 46.9742 12.2606 47.2671 12.9345C47.4527 13.3446 47.4722 13.8329 47.6089 21.3134C47.6968 25.6884 47.7945 30.6688 47.8433 32.3876C47.8824 34.1063 47.9117 35.5126 47.9117 35.5223C47.9019 35.5223 47.0035 35.454 45.9 35.3563C43.9175 35.1903 39.7574 34.9462 36.5933 34.8192L34.9429 34.7509L35.0113 34.204C35.1773 32.8954 35.4117 32.6903 38.3902 31.3524C40.9097 30.2196 41.3004 29.9559 41.73 29.1649C41.9351 28.8036 41.9449 28.618 41.9742 26.3036C42.0035 23.8427 42.0035 23.8427 42.2183 23.8427C42.5992 23.8427 43.6343 23.2763 44.0933 22.8173C44.3472 22.5731 44.6793 22.0848 44.8453 21.7235C45.148 21.0888 45.148 21.0399 45.2261 19.0184C45.275 17.8856 45.3336 15.6981 45.3726 14.1454C45.4117 12.1532 45.4703 11.3427 45.5484 11.3427C45.607 11.3427 45.8804 11.4794 46.1636 11.6356ZM15.8218 12.4852C15.8218 13.0712 15.8707 15.1415 15.9195 17.1044C16.0367 21.0399 16.0855 21.3622 16.6714 22.2509C17.0816 22.8661 17.5601 23.2665 18.3316 23.6278C18.8882 23.8817 18.9273 23.8915 20.607 23.872C21.5445 23.8622 24.2105 23.7841 26.5347 23.6962C30.4019 23.5497 31.0855 23.5497 34.8843 23.6864C37.1597 23.7743 39.5523 23.8622 40.2164 23.9013L41.4078 23.9598V26.1766C41.398 29.37 41.4371 29.3114 38.3316 30.7079C35.6461 31.9091 35.5484 31.9677 35.1285 32.4266C34.6695 32.9345 34.5132 33.3348 34.4254 34.1356L34.357 34.7899L33.7515 34.7118C32.7457 34.5946 17.8726 34.6337 14.6011 34.7606C10.6168 34.9169 6.73004 35.1317 4.50348 35.3173C3.51715 35.4052 2.69684 35.454 2.67731 35.4345C2.61871 35.3856 2.82379 13.9891 2.88239 13.5009C2.94098 12.954 3.32184 12.3095 3.77106 11.9677C4.41559 11.4696 4.6109 11.4501 10.4312 11.4501L15.8218 11.4403V12.4852ZM5.27496 36.4891C12.2867 36.9872 18.8589 37.163 27.5211 37.0946C31.232 37.0751 34.2789 37.0653 34.2789 37.0946C34.2789 37.2313 33.9664 37.7001 33.7222 37.9149C33.1363 38.4618 33.4 38.4423 27.0914 38.4911L21.232 38.5399L20.6558 38.8329C19.9722 39.1845 19.5035 39.7509 19.2886 40.4833L19.1324 41.0009L16.8668 40.9618C15.6168 40.9423 12.3843 40.8837 9.66949 40.8446C4.86481 40.7762 4.72809 40.7665 4.24957 40.5516C3.61481 40.2684 3.15582 39.829 2.87262 39.2235C2.66754 38.7938 2.63825 38.5985 2.63825 37.4852V36.2255L2.91168 36.2841C3.05817 36.3134 4.12262 36.4012 5.27496 36.4891ZM47.9507 36.9481V37.6512L47.316 37.0262C46.7203 36.4305 46.691 36.3915 46.8961 36.3231C47.023 36.2938 47.3062 36.2645 47.5406 36.2548L47.9507 36.245V36.9481ZM46.9156 37.5048C47.941 38.5302 47.9703 38.579 47.9019 38.8915C47.8629 39.077 47.7554 39.37 47.6773 39.5458L47.5211 39.868L45.8804 38.2274C44.0445 36.3915 44.064 36.4794 45.314 36.4501L45.8511 36.4403L46.9156 37.5048ZM45.2652 38.4911L47.1109 40.3368L46.8765 40.6005C46.7398 40.7372 46.5054 40.9227 46.3492 41.0009L46.066 41.1473L43.8882 38.9696C42.6871 37.7782 41.73 36.7626 41.7593 36.7137C41.7886 36.6747 42.1695 36.6356 42.6089 36.6356H43.4097L45.2652 38.4911ZM43.1168 39.077C45.7535 41.7137 45.6461 41.4891 44.191 41.4305L43.605 41.4012L41.3296 39.1454L39.064 36.8798L39.523 36.8212C40.8218 36.6649 40.4996 36.4598 43.1168 39.077ZM40.5289 39.1259L42.8238 41.4208H41.8961H40.9683L38.7222 39.1747L36.4859 36.9384L37.2183 36.8993C37.6187 36.8895 38.0093 36.8602 38.0875 36.8505C38.1656 36.8407 39.2691 37.8661 40.5289 39.1259ZM37.8238 39.1552L40.0504 41.3817L39.0933 41.3427L38.1363 41.3036L36.398 39.5751L34.6597 37.8466L34.7769 37.5145C34.9039 37.1239 35.1089 36.9286 35.3921 36.9286C35.5289 36.9286 36.3589 37.6903 37.8238 39.1552ZM36.3589 41.3036L35.5093 41.3329L34.3375 40.161L33.1754 38.9989L33.566 38.8036C33.7711 38.6962 34.0054 38.5302 34.0836 38.4423C34.2007 38.3055 34.3765 38.4423 35.7144 39.7802L37.2086 41.2743L36.3589 41.3036ZM25.2945 40.1024L26.3199 41.1278H25.3921H24.4644L23.439 40.1024L22.4136 39.077H23.3414H24.2691L25.2945 40.1024ZM28.0289 40.1024L29.0543 41.1278H28.1265H27.1988L26.1734 40.1024L25.148 39.077H26.0757H27.0035L28.0289 40.1024ZM30.7144 40.1512L31.7886 41.2255H30.9097H30.0308L28.9566 40.1512L27.8824 39.077H28.7613H29.6402L30.7144 40.1512ZM33.4488 40.1512L34.523 41.2255H33.5953H32.6675L31.5933 40.1512L30.5191 39.077H31.4468H32.3746L33.4488 40.1512ZM22.6578 40.1024L23.5855 41.0302H22.6578H21.7203L21.0562 40.3563L20.3921 39.6825L20.8121 39.4286C21.5054 39.0184 21.6226 39.0575 22.6578 40.1024ZM20.4605 40.6395L20.8414 41.0302H20.2847C19.8843 41.0302 19.7476 41.0009 19.7769 40.9032C19.9429 40.4442 20.0211 40.2489 20.0504 40.2489C20.0601 40.2489 20.2554 40.4247 20.4605 40.6395ZM26.5152 42.6122C26.5347 42.7391 26.6714 43.4716 26.8179 44.2528C26.9546 45.0341 27.1793 46.1278 27.3062 46.6747C27.4332 47.2313 27.5406 47.7098 27.5406 47.7294C27.5406 47.7489 26.398 47.7684 25.0015 47.7684C23.605 47.7684 22.4625 47.7489 22.4625 47.7294C22.4625 47.7098 22.5699 47.2313 22.6968 46.6747C22.8336 46.1278 23.0484 45.0341 23.1949 44.2528C23.3316 43.4716 23.4683 42.7391 23.4879 42.6122C23.5367 42.3973 23.566 42.3973 25.0015 42.3973C26.4371 42.3973 26.4664 42.3973 26.5152 42.6122Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M31.7877 5.94213C31.2896 6.73315 30.5572 8.28588 30.1471 9.43823C29.0924 12.3484 28.5748 15.1706 28.6334 17.6414C28.6627 18.8718 28.6724 18.9109 28.8775 18.9402C29.0533 18.9695 29.1705 18.8621 29.4244 18.4519C31.065 15.8933 32.3541 11.6062 32.608 7.87573C32.7252 6.16674 32.6568 5.44409 32.3638 5.40503C32.1978 5.38549 32.0709 5.50268 31.7877 5.94213Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M26.3989 8.31528C24.6509 8.94028 21.5942 10.8543 21.2427 11.5379C21.0767 11.8602 21.0571 12.3875 21.1841 12.7391C21.3696 13.2079 23.7231 14.8387 25.2466 15.5516C26.7407 16.245 27.2485 16.3231 27.2485 15.8739C27.2485 15.62 26.0669 13.9989 25.2173 13.1004L24.3384 12.1727L25.1001 11.3817C25.8423 10.6102 26.731 9.47739 27.1118 8.82309C27.2485 8.58872 27.2681 8.46176 27.1899 8.34457C27.0435 8.1102 26.9653 8.1102 26.3989 8.31528Z"
                    fill="var(--color-black-300)"
                />
                <path
                    d="M34.0742 8.27598C33.9668 8.40293 33.9766 8.49082 34.1621 8.83262C34.4941 9.45762 35.3047 10.4928 36.1348 11.3814L36.8965 12.1725L36.1348 12.9732C35.2949 13.8619 34.4941 14.8971 34.1621 15.5123C33.957 15.9029 33.9473 15.942 34.1035 16.0982C34.2598 16.2545 34.3281 16.235 35.1875 15.9029C36.6621 15.317 38.8203 13.9986 39.7578 13.1002C40.1191 12.7584 40.1387 12.6998 40.1387 12.1725C40.1387 11.6451 40.1191 11.5865 39.7578 11.2447C38.8594 10.3854 36.623 8.99864 35.3535 8.52012C34.2695 8.1002 34.2207 8.1002 34.0742 8.27598Z"
                    fill="var(--color-black-300)"
                />
            </g>
            <defs>
                <clipPath id="clip0_1961_346423">
                    <rect height="50" width="50" fill="white" transform="translate(0 0.599976)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default ApiTestingIcon;
