// Generated from SVG to Code Figma Plugin
import React from 'react';

export const Black = () => (
    <svg height="38" width="44" fill="none" viewBox="0 0 44 38" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M20.7788 1.11921C20.1831 1.42195 18.601 2.75007 18.4448 3.0821C18.269 3.4532 18.5522 3.78523 19.6362 4.50788C20.935 5.37703 21.2085 5.52351 21.8139 5.6114C22.3022 5.68953 22.4878 5.6114 22.4878 5.3282C22.4878 5.18171 21.687 4.32234 21.1206 3.87312L20.8764 3.67781L21.228 3.66804C21.9702 3.66804 23.6499 3.88288 24.7241 4.11726C26.7846 4.57624 28.4741 5.2989 30.1538 6.43171C31.3354 7.23249 33.103 9.0196 33.894 10.211C36.9214 14.7618 37.3608 20.6212 35.0366 25.5919C33.3471 29.2247 30.3882 31.9493 26.4917 33.4825C25.4272 33.9024 23.6303 34.4298 23.2495 34.4298C23.1518 34.4298 23.0737 34.4688 23.0737 34.5177C23.0737 34.8302 26.1303 34.7618 27.2632 34.4298C29.6069 33.7364 31.7553 32.3692 33.855 30.2598C36.101 27.9942 37.3803 25.5821 37.9858 22.4669C38.2007 21.3634 38.2202 18.2384 38.0249 17.1446C37.6538 14.9962 37.0874 13.3165 36.1499 11.4805C35.2319 9.7032 34.3823 8.53132 32.9858 7.13484C30.3686 4.51765 27.4389 3.15046 24.1284 3.01374C22.8393 2.95515 21.3257 3.0821 20.73 3.28718C20.5542 3.35554 20.603 3.27742 20.9253 3.00398C22.0776 2.04695 22.5854 1.35359 22.3901 1.03132C22.351 0.962963 22.0971 0.884838 21.8432 0.865307C21.4526 0.826244 21.2573 0.875072 20.7788 1.11921Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M14.9478 4.44922C11.8911 4.9668 8.95169 7.45704 7.15481 11.0508C6.00247 13.375 5.40676 15.6895 5.32864 18.2383C5.27005 20.0059 5.38723 21.1777 5.78762 22.7598C6.34426 24.957 7.22317 26.6465 8.522 28.0039C8.98098 28.4727 9.35208 28.8633 9.34231 28.8633C9.34231 28.8633 9.09817 28.7852 8.8052 28.6777C8.18997 28.4727 7.05715 28.3262 6.75442 28.4238C6.5884 28.4824 6.55911 28.541 6.60794 28.7852C6.63723 28.9414 6.76419 29.2051 6.88137 29.3711C7.35012 30.0352 10.231 31.002 10.7583 30.6797C10.9536 30.5527 11.022 29.9863 11.0122 28.4727C11.0122 27.0859 10.9927 26.9199 10.7876 26.5488C10.6216 26.2363 10.5142 26.1289 10.3384 26.1289C10.1236 26.1289 10.0845 26.1973 9.94778 26.832C9.85989 27.2227 9.75247 27.7402 9.7134 27.9844L9.64504 28.4141L9.16653 27.6914C8.09231 26.0117 7.29153 23.9316 6.94973 21.9199C6.68606 20.3672 6.72512 17.8379 7.05715 16.334C8.03372 11.7539 10.9829 7.59375 15.0161 5.09375C15.5044 4.79102 15.9634 4.50781 16.0415 4.45899C16.2271 4.3418 15.6216 4.3418 14.9478 4.44922Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M23.6491 8.52153C23.4245 8.72661 23.0827 9.13676 22.8678 9.4395L22.4967 9.98637L21.6862 10.0352C21.237 10.0645 20.7487 10.1426 20.5827 10.211C20.2995 10.3282 20.3288 10.3379 21.2663 10.5137C22.8581 10.7969 22.8092 10.8067 23.6589 9.97661L24.3913 9.24418L25.2604 9.65434L26.1198 10.0547L26.0612 11.1387C26.0319 11.7247 26.0319 12.2618 26.071 12.3106C26.1198 12.3692 26.6276 12.8672 27.2038 13.4239L28.2585 14.4297L29.1471 14.3614C29.6354 14.3223 30.0846 14.2735 30.153 14.254C30.2311 14.2247 30.3874 14.4786 30.5827 14.9375C30.7487 15.3379 30.8854 15.709 30.8854 15.7579C30.8854 15.8067 30.5827 16.0997 30.2018 16.4219C29.694 16.8516 29.4889 17.086 29.4206 17.3692C29.362 17.5645 29.3425 17.7793 29.3717 17.8282C29.4108 17.8868 29.4303 18.4727 29.4303 19.1465L29.4206 20.3575L30.1237 20.9629C30.5143 21.295 30.8757 21.5977 30.9245 21.6465C30.9733 21.6954 30.8952 22.0372 30.7389 22.4668L30.4753 23.209L29.4108 23.1797L28.3464 23.1602L27.3698 24.1856C26.2565 25.3575 26.1686 25.5528 26.2956 26.5684C26.3444 26.9395 26.3835 27.3204 26.3932 27.4082C26.3932 27.5059 26.1393 27.6719 25.6901 27.8672C25.2995 28.043 24.9479 28.1797 24.8991 28.1797C24.86 28.1797 24.5085 27.8477 24.1178 27.4473L23.4147 26.7247L21.9401 26.754C21.1198 26.7735 20.3678 26.8418 20.2604 26.9004C20.153 26.959 19.8405 27.3106 19.5573 27.6915C19.2839 28.0625 19.0007 28.375 18.9421 28.375C18.8835 28.375 18.4831 28.2383 18.0534 28.0821L17.2624 27.7793L17.2917 26.7149L17.321 25.6407L16.2663 24.6055L15.2116 23.5704L14.4303 23.629C14.0007 23.6582 13.5124 23.7071 13.3561 23.7364C13.0534 23.8047 13.0534 23.795 12.6921 22.9649C12.4967 22.5059 12.3307 22.1055 12.3307 22.0567C12.3307 22.0176 12.6628 21.6758 13.0729 21.295L13.8249 20.6114L13.8053 19.2637C13.7956 18.5215 13.7467 17.8086 13.6979 17.6915C13.6491 17.5645 13.2878 17.2032 12.8776 16.8907C12.4772 16.5782 12.1452 16.2461 12.1452 16.168C12.1452 16.0899 12.2917 15.6504 12.4675 15.2012L12.7897 14.3711L13.8835 14.4004L14.9772 14.4297L15.2409 14.1075C15.3874 13.9415 15.7975 13.4825 16.1686 13.0918C16.9108 12.3008 16.9108 12.2715 16.6764 11.0215C16.5983 10.6016 16.53 10.2012 16.53 10.1329C16.53 10.0743 17.0378 9.76176 17.653 9.45903L18.776 8.89262L19.4108 9.52739C19.7624 9.86918 20.0456 10.1231 20.0456 10.0743C20.0456 10.0254 20.1139 10.045 20.1921 10.1133C20.28 10.1817 20.3678 10.211 20.3971 10.1817C20.485 10.1036 20.2995 9.80083 19.655 9.00004C19.2448 8.48247 19.0007 8.25786 18.8542 8.25786C18.4147 8.25786 15.9733 9.30278 15.7878 9.57622C15.6901 9.73247 15.6803 9.99614 15.7389 10.7969L15.8268 11.8223L15.1725 12.5157L14.528 13.2188L13.3561 13.17C12.4675 13.1309 12.1452 13.1504 12.0182 13.2481C11.8717 13.3653 11.237 14.8106 10.7975 16.002C10.612 16.5293 10.7975 16.8614 11.6862 17.584L12.4186 18.17V19.1075L12.4284 20.045L11.5885 20.8067C10.8268 21.5 10.7585 21.5977 10.7975 21.8711C10.8757 22.3887 11.9303 24.8008 12.155 24.9961C12.36 25.1622 12.4967 25.1719 13.5026 25.1133L14.6354 25.0547L15.2507 25.6797L15.8757 26.3047L15.7975 27.2422C15.6901 28.7852 15.7292 28.834 17.5065 29.5079C18.0143 29.6934 18.61 29.918 18.8346 30.0059C19.3815 30.2207 19.6354 30.0743 20.4362 29.1075L21.071 28.3262H21.9303H22.78L23.5417 29.1368C24.1374 29.7715 24.3522 29.9375 24.5671 29.9375C24.8796 29.9375 27.5163 28.834 27.78 28.5997C28.0339 28.3653 28.0925 27.8575 27.9655 26.9395L27.8483 26.1094L28.4635 25.4454L29.0788 24.7813L30.0749 24.8301C31.4225 24.9082 31.5788 24.8399 31.8913 24C32.028 23.6387 32.3014 22.9747 32.487 22.5157C32.6823 22.0567 32.8385 21.5977 32.8385 21.5C32.8385 21.1973 32.4772 20.7481 31.7546 20.1719L31.0807 19.6348V18.8829C31.0807 18.4727 31.0612 18.0821 31.0417 18.004C31.0221 17.9356 31.3444 17.584 31.7643 17.2227C32.9069 16.2364 32.9069 16.2071 32.1061 14.4297C31.7448 13.6387 31.3737 12.9063 31.2663 12.8086C31.0905 12.6231 31.0319 12.6231 29.9577 12.7403L28.8249 12.8575L28.4147 12.4864C28.1901 12.2813 27.8581 11.9981 27.6725 11.8711L27.3503 11.627L27.4186 10.5821C27.4577 9.90825 27.4479 9.47856 27.3796 9.35161C27.321 9.23442 27.0475 9.07817 26.7643 8.98051C26.4811 8.89262 25.8854 8.66801 25.446 8.49223C24.401 8.06254 24.1569 8.06254 23.6491 8.52153Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M19.8493 13.1797C17.8767 13.7461 16.0993 15.6016 15.6892 17.5254C15.406 18.8731 15.5817 20.3965 16.1677 21.6367C16.6071 22.5547 17.8083 23.7364 18.7751 24.2051C19.8493 24.7324 20.6013 24.8985 21.8024 24.8887C22.6228 24.8887 22.9841 24.8301 23.6091 24.625C25.1228 24.1367 26.2653 23.1406 26.9685 21.6953C27.447 20.6992 27.6032 20.0449 27.5935 19C27.5935 16.9492 26.5974 15.3086 24.8396 14.4297C24.0583 14.0293 23.6872 13.9219 23.8044 14.1074C23.8337 14.1563 23.7849 14.2149 23.6872 14.2344C23.57 14.2539 23.6384 14.3418 23.9411 14.5469C24.5857 14.9961 25.4646 16.002 25.7966 16.6758C26.2556 17.6035 26.4411 18.6582 26.3044 19.5274C26.0603 21.0801 25.1521 22.2031 23.4821 23.0332C22.6716 23.4336 22.6228 23.4434 21.656 23.4434C20.7087 23.4434 20.6403 23.4239 19.9274 23.0723C19.3903 22.8086 19.029 22.5449 18.6384 22.1153C16.7829 20.1133 16.7439 17.2324 18.5505 15.5821C19.6247 14.5957 20.9528 14.1172 22.613 14.1172C23.6872 14.1172 23.6872 14.1172 23.5114 13.9219C23.4138 13.8145 23.0427 13.5801 22.6911 13.4043C22.1345 13.1309 21.9392 13.0918 21.1677 13.0723C20.6696 13.0625 20.0935 13.1114 19.8493 13.1797Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M22.8203 17.7207C22.498 17.9356 21.9219 18.4434 21.5215 18.8438L20.7988 19.5762L20.3984 19.2344C19.9199 18.8242 19.041 18.5313 18.8262 18.7071C18.748 18.7754 18.6797 18.8731 18.6797 18.9317C18.6797 19.1172 19.3633 20.0645 19.9297 20.6602C20.7891 21.5586 20.8672 21.5391 22.9277 19.752C24.2363 18.6289 24.9004 17.6621 24.5293 17.4278C24.4512 17.3789 24.168 17.3399 23.8945 17.3399C23.4648 17.3399 23.2988 17.3985 22.8203 17.7207Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M14.9965 32.4473C14.7621 32.5352 14.8695 32.7305 15.5629 33.4434C16.266 34.1465 16.266 34.1465 15.934 34.0977C15.0648 33.961 9.01989 33.8242 6.47107 33.8828C2.27185 33.9805 0.426144 34.1953 0.377316 34.5762C0.357785 34.7422 1.12927 35.1035 1.88122 35.2793C2.50622 35.4258 3.0531 35.4551 5.64099 35.4453C8.95153 35.4258 15.3968 35.2403 15.9437 35.1426L16.2855 35.084L15.68 35.7383C14.9965 36.4707 14.8402 36.8321 15.1136 37.0371C15.641 37.4082 16.5785 37.0469 17.9847 35.9043C18.8246 35.2403 19.059 34.8692 18.9125 34.4492C18.8148 34.1758 17.2133 33.0137 16.3832 32.6133C15.934 32.3985 15.2992 32.3203 14.9965 32.4473Z"
            fill="var(--color-black-200)"
        />
        <path
            d="M39.6562 32.4375C39.4902 32.5938 39.5977 32.8086 40.125 33.3555L40.6719 33.9316L38.1426 33.9121C34.8027 33.8828 33.6211 34.0781 33.6211 34.625C33.6211 34.8398 33.875 35.0645 34.3535 35.2695C35.0566 35.5625 37.6348 35.5527 40.4082 35.25C40.8672 35.2012 40.8867 35.2012 40.6914 35.3477C40.3594 35.5918 39.6758 36.5293 39.6758 36.7539C39.6758 37.0176 39.9492 37.1641 40.4277 37.1641C40.877 37.1641 41.5508 36.7832 42.5664 35.9727C43.4844 35.2305 43.6211 35.0645 43.6211 34.6543C43.6211 34.4199 43.5625 34.2539 43.4355 34.166C42.1758 33.2773 41.5996 32.8965 41.209 32.6914C40.7402 32.4473 39.793 32.291 39.6562 32.4375Z"
            fill="var(--color-black-200)"
        />
    </svg>
);
