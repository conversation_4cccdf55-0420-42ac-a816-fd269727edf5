import React from 'react';

const RightIcon = ({
    bgColor = 'var(--color-primary-900)',
    iconColor = 'var(--color-white)',
    size = 18,
}) => {
    return (
        <svg height={size} width={size} viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M17.7188 9.15002C17.7188 13.9664 13.7812 17.8688 9 17.8688C4.18359 17.8688 0.28125 13.9664 0.28125 9.15002C0.28125 4.36877 4.18359 0.431274 9 0.431274C13.7812 0.431274 17.7188 4.36877 17.7188 9.15002Z"
                fill={bgColor} // Background color
            />
            <path
                d="M7.98047 13.7906L14.4492 7.3219C14.6602 7.11096 14.6602 6.72424 14.4492 6.51331L13.6406 5.73987C13.4297 5.49377 13.0781 5.49377 12.8672 5.73987L7.59375 11.0133L5.09766 8.55237C4.88672 8.30627 4.53516 8.30627 4.32422 8.55237L3.51562 9.32581C3.30469 9.53674 3.30469 9.92346 3.51562 10.1344L7.17188 13.7906C7.38281 14.0016 7.76953 14.0016 7.98047 13.7906Z"
                fill={iconColor} // Icon (tick) color
            />
        </svg>
    );
};

export default RightIcon;
