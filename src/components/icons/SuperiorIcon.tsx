import React from 'react';

const SuperiorIcon = () => {
    return (
        <svg
            height="48"
            width="48"
            fill="none"
            viewBox="0 0 48 48"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_645_891308)">
                <mask
                    height="48"
                    id="mask0_645_891308"
                    style={{ maskType: 'luminance' }}
                    width="48"
                    x="0"
                    y="0"
                    maskUnits="userSpaceOnUse"
                >
                    <path d="M47.4668 0H0.533447V48H47.4668V0Z" fill="white" />
                </mask>
                <g mask="url(#mask0_645_891308)">
                    <path
                        d="M8.07109 31.2762V24.738C8.07109 20.1363 5.37244 18.489 1.24438 18.489V24.738V34.2822C1.2444 36.442 2.03568 38.5375 3.48809 40.2242L8.07109 45.5447V46.9333"
                        stroke="var(--color-primary-900)"
                        strokeLinecap="square"
                        strokeLinejoin="round"
                        strokeWidth="1.06667"
                    />
                    <path
                        d="M11.4853 37.6171L8.41321 32.8609C8.06303 32.2153 7.97938 31.4729 8.1781 30.7737C8.37682 30.0747 8.84413 29.4674 9.49184 29.0665C9.85059 28.8445 10.2542 28.6923 10.678 28.6193C11.102 28.5464 11.5375 28.5542 11.958 28.642C12.3787 28.7299 12.7757 28.8963 13.1251 29.131C13.4744 29.3657 13.769 29.6638 13.9906 30.0073L16.5893 33.6303C17.7134 35.1949 18.3129 37.0339 18.3119 38.915V45.2566V46.9327"
                        stroke="var(--color-primary-900)"
                        strokeLinecap="square"
                        strokeLinejoin="round"
                        strokeWidth="1.06667"
                    />
                    <path
                        d="M39.9287 31.2762V24.738C39.9287 20.1363 42.6274 18.489 46.7555 18.489V24.738V34.2822C46.7555 36.442 45.9642 38.5375 44.5119 40.2242L39.9287 45.5447V46.9333"
                        stroke="var(--color-primary-900)"
                        strokeLinecap="square"
                        strokeLinejoin="round"
                        strokeWidth="1.06667"
                    />
                    <path
                        d="M36.5143 37.6171L39.5864 32.8609C39.9366 32.2153 40.0202 31.4729 39.8215 30.7737C39.6229 30.0747 39.1555 29.4674 38.5077 29.0665C38.149 28.8445 37.7453 28.6923 37.3216 28.6193C36.8976 28.5464 36.4621 28.5542 36.0416 28.642C35.6209 28.7299 35.224 28.8963 34.8745 29.131C34.5252 29.3657 34.2307 29.6638 34.0089 30.0073L31.4103 33.6303C30.2861 35.1949 29.6867 37.0339 29.6877 38.915V45.2566V46.9327"
                        stroke="var(--color-primary-900)"
                        strokeLinecap="square"
                        strokeLinejoin="round"
                        strokeWidth="1.06667"
                    />
                    <path
                        d="M37.1994 2.43728C36.4247 1.66519 35.5049 1.05275 34.4926 0.634905C33.4804 0.217058 32.3954 0.00199109 31.2997 0.00199109C30.2041 0.00199109 29.1192 0.217058 28.1069 0.634905C27.0946 1.05275 26.1749 1.66519 25.4001 2.43728C24.8161 3.01927 24.3759 3.68023 24.0007 4.36822C23.6303 3.65931 23.1581 3.0081 22.5989 2.4352C21.0344 0.875969 18.9121 0 16.6993 0C14.4866 0 12.3644 0.875969 10.7996 2.4352C9.23499 3.99444 8.35596 6.10922 8.35596 8.3143C8.35596 10.5194 9.23499 12.6342 10.7996 14.1934L24.0007 27.0221L37.1994 14.1934C37.9746 13.4219 38.5896 12.5055 39.0091 11.4969C39.4287 10.4883 39.6447 9.40723 39.6447 8.31534C39.6447 7.22351 39.4287 6.14237 39.0091 5.13376C38.5896 4.12516 37.9746 3.20886 37.1994 2.43728Z"
                        fill="var(--color-primary-900)"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_645_891308">
                    <rect
                        height="48"
                        width="46.9333"
                        fill="white"
                        transform="translate(0.533447)"
                    />
                </clipPath>
            </defs>
        </svg>
    );
};

export default SuperiorIcon;
