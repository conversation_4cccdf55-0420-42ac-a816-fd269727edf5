import React from 'react';

const CostEffectiveIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M26.0746 2.24219C24.7949 3.56562 24.2043 6.14688 24.6746 8.38906C24.9699 9.82188 25.484 10.7625 26.709 12.1188C27.0699 12.5125 27.5074 13.125 27.6824 13.4859C27.9668 14.0328 27.9996 14.2188 27.9777 14.8531L27.9449 15.5859L27.0809 14.3937C26.5996 13.7375 26.0199 13.0703 25.7793 12.8953C24.8277 12.2062 23.4605 11.9328 21.8637 12.1406C19.2168 12.4688 17.3137 13.3656 16.4277 14.7219C15.848 15.5859 16.0012 16.0453 16.909 16.2859C17.8059 16.5266 18.9652 17.0734 20.2668 17.8719C21.984 18.9219 23.0449 19.2938 24.1387 19.2281C24.6855 19.1844 25.0902 19.0859 25.4512 18.9C25.7355 18.7578 26.2715 18.4844 26.6324 18.2984C26.9934 18.1125 27.4199 17.9375 27.5949 17.8937L27.8902 17.8281V19.6109V21.4047L26.7746 21.525C25.1668 21.7 24.2152 22.0172 23.7121 22.5422C23.1871 23.0891 23.0996 23.5375 23.3949 24.2375C23.7012 24.9813 23.8762 25.9438 23.8871 26.9062C23.898 28.2953 23.4387 29.0281 21.5246 30.6578C17.609 33.9828 16.5809 35.0984 15.673 36.9141C14.9512 38.3578 14.623 39.7906 14.623 41.5625C14.623 44.9641 15.8918 47.9062 18.5277 50.6297C20.4965 52.6422 21.5574 53.2547 23.8543 53.6922C25.1559 53.9328 28.3496 54.25 29.4762 54.25C31.3465 54.25 33.3262 53.8344 34.8355 53.1125C40.5012 50.4219 43.0059 43.1266 40.2496 37.3188C39.5168 35.7766 38.084 33.8188 36.8918 32.7359C33.9934 30.1 33.523 29.5312 33.1949 28.2734C32.9652 27.3656 32.9652 25.2328 33.2059 25.0469C33.523 24.7734 33.8949 24.0078 33.9605 23.4609C34.0262 23.0016 34.0043 22.8703 33.7965 22.6078C33.3043 21.9844 32.1559 21.6234 30.2199 21.4813L29.2027 21.4047V20.3875V19.3594H29.5527C29.7496 19.3594 30.4387 19.6109 31.1168 19.9281L32.3199 20.5078H33.4902H34.6496L35.7105 19.9609C37.1543 19.2281 38.073 18.4297 38.8934 17.2156C39.5934 16.1875 40.4684 14.1641 40.4684 13.5953C40.4684 13.0484 40.348 13.0156 38.423 13.0156C36.8809 13.0156 36.4324 13.0594 35.0324 13.3438C33.3699 13.6828 32.5605 13.9125 32.0027 14.1859C31.4559 14.4703 30.723 15.2359 30.4059 15.8703C30.2309 16.2094 29.9246 16.6469 29.7277 16.8438L29.3668 17.2156L29.334 15.8047C29.3121 14.9188 29.3449 14.3719 29.4215 14.3281C29.4762 14.2953 29.7715 13.7922 30.0668 13.2234C30.3512 12.6547 30.8434 11.7141 31.1496 11.1234C32.0027 9.47187 32.2105 8.78281 32.2105 7.49219C32.2105 6.45312 32.1887 6.35469 31.7949 5.53437C31.1934 4.26562 29.9465 3.15 27.9559 2.08906C27.0262 1.59687 26.6543 1.62969 26.0746 2.24219ZM27.4855 3.34687C28.2402 3.74063 29.3887 4.59375 29.8918 5.12969C31.2152 6.54063 31.248 7.82031 30.0559 10.3141L29.4871 11.4844L29.1262 10.3141C28.8309 9.31875 28.7652 8.95781 28.7652 8.01719C28.7652 6.72656 28.6668 6.52969 28.0105 6.58438C27.6605 6.61719 27.6059 6.66094 27.4746 7.05469C27.2449 7.73281 27.3652 9.15469 27.7699 10.3906C27.9559 10.9594 28.109 11.4734 28.098 11.5391C28.098 11.5938 27.8355 11.3531 27.5074 10.9922C25.6699 8.97969 25.309 6.26719 26.534 3.92656C26.7527 3.51094 26.9824 3.17188 27.0371 3.17188C27.0918 3.17188 27.2887 3.24844 27.4855 3.34687ZM24.434 13.6609C24.948 13.8906 25.3637 14.2734 25.834 14.9734L26.173 15.4438L25.0355 15.4C24.0512 15.3563 23.7559 15.2906 22.8262 14.9297C21.623 14.4594 21.2402 14.4813 21.109 14.9844C20.9559 15.5312 21.1309 15.6953 22.3887 16.1875C23.4715 16.6141 23.6684 16.6578 24.8277 16.7125L26.0746 16.7672L25.3746 17.1281C24.1059 17.7734 23.3402 17.6531 20.6715 16.4062C19.6762 15.9359 18.6371 15.4766 18.3418 15.3672C17.8387 15.1813 17.8277 15.1703 18.0137 14.9516C18.5496 14.3609 19.884 13.8031 21.3387 13.5625C21.7871 13.4969 22.2246 13.4203 22.3121 13.3984C22.684 13.3109 24.0184 13.475 24.434 13.6609ZM38.7621 14.6234C37.9309 16.6359 36.8152 17.9375 35.3387 18.6266C34.3543 19.0969 33.6434 19.075 32.2652 18.5609C31.6746 18.3422 31.3027 18.1562 31.3902 18.1234C31.4777 18.0906 32.0465 18.0141 32.648 17.9484C33.6762 17.8391 35.7652 17.2812 36.0715 17.0406C36.2355 16.8984 36.2465 16.275 36.0715 16.1C35.8637 15.8922 35.3277 15.9469 34.223 16.2859C33.1512 16.6141 31.4996 16.8328 31.4996 16.6469C31.4996 16.4719 32.1121 15.6953 32.3855 15.5203C33.2824 14.9516 36.2902 14.3172 38.5215 14.2297L38.9262 14.2188L38.7621 14.6234ZM27.7809 23.1328V23.5266L27.2121 23.4609C26.8949 23.4281 26.3152 23.3406 25.9215 23.275C25.2324 23.1547 25.2215 23.1438 25.5715 23.0562C25.998 22.9469 27.048 22.7828 27.4855 22.7609C27.759 22.75 27.7809 22.7828 27.7809 23.1328ZM31.0402 22.9031C31.609 22.9906 32.0902 23.0781 32.1121 23.1109C32.1887 23.1875 30.9855 23.3953 30.0012 23.4719L29.0934 23.5484V23.1438C29.0934 22.75 29.0934 22.75 29.5637 22.75C29.8152 22.75 30.4824 22.8156 31.0402 22.9031ZM28.8855 25.1016H30.9199L30.9965 26.3594C31.1934 29.75 31.6746 30.6031 34.7262 32.9656C36.4105 34.2672 37.3621 35.1969 38.1496 36.3453C39.6152 38.4563 40.2059 40.6766 40.009 43.3125C39.7355 46.9984 37.2746 50.5859 34.0262 52.0297C31.5762 53.1234 28.0434 53.2219 25.0902 52.2703C22.0059 51.2859 19.7637 49.4813 18.5168 46.9766C17.6527 45.2484 17.3465 43.7281 17.423 41.5734C17.4887 39.6922 17.7184 38.7406 18.4402 37.1984C19.348 35.2625 20.1902 34.3328 22.8371 32.3313C25.7902 30.1 26.0309 29.6625 26.0309 26.6V24.9266L26.4465 25.0141C26.6652 25.0578 27.7699 25.1016 28.8855 25.1016Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M28.4375 33.7639C28.1094 33.8842 28 34.1686 28 34.9561V35.7326L27.5953 35.8092C26.075 36.0607 24.6094 37.4936 24.6094 38.7186C24.6094 39.9107 25.5062 40.6982 27.2562 41.0482L27.9453 41.1904L27.9781 43.017C27.9891 44.0232 27.9781 44.8436 27.9562 44.8436C27.9234 44.8436 27.6281 44.7451 27.2891 44.6139C26.8516 44.4498 26.5672 44.2529 26.2281 43.8482C25.8891 43.4436 25.7031 43.3123 25.4734 43.3123C24.7625 43.3123 24.6422 44.0889 25.2437 44.8326C25.6266 45.3029 26.6656 45.8936 27.4203 46.0576L27.9891 46.1889L28.0219 47.1186C28.0547 47.9936 28.0766 48.0592 28.3609 48.2779C28.6453 48.4857 28.6891 48.4857 28.9625 48.3326C29.2469 48.1795 29.2578 48.1248 29.2906 47.1732L29.3234 46.1779L29.7719 46.1123C30.9312 45.9264 32.2766 44.8982 32.6484 43.9248C32.9328 43.1811 32.8344 42.2404 32.4187 41.6498C31.9047 40.9498 31.3359 40.5998 30.2641 40.3373L29.3125 40.1076V38.5873C29.3125 37.7561 29.3344 37.0779 29.3672 37.0779C29.4 37.0779 29.6625 37.1982 29.9469 37.3404C30.4719 37.6139 30.9422 38.0514 31.3797 38.6857C31.5656 38.9701 31.6969 39.0467 31.9812 39.0467C33.5453 39.0467 31.8937 36.4654 29.9578 35.8967L29.3125 35.7107V34.8795C29.3125 34.3436 29.2687 34.0045 29.1812 33.9279C28.9844 33.7748 28.6344 33.6982 28.4375 33.7639ZM28 38.4561V39.8451L27.5406 39.7686C26.9828 39.6811 26.1406 39.2873 25.9984 39.0357C25.725 38.5873 26.3812 37.6904 27.2891 37.2857C28.0109 36.9576 28 36.9248 28 38.4561ZM30.1875 41.6717C30.4937 41.7592 30.8219 41.9561 31.0844 42.2186C31.6531 42.7873 31.6531 43.2467 31.0953 43.8811C30.7125 44.2857 29.7828 44.8436 29.4547 44.8436C29.3453 44.8436 29.3125 44.4279 29.3125 43.1373C29.3125 41.5732 29.3234 41.442 29.5094 41.4967C29.6078 41.5295 29.9141 41.6061 30.1875 41.6717Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M35.6237 37.4935C35.3394 37.7997 35.405 38.1497 35.8315 38.7294C36.4331 39.5497 36.6409 40.2825 36.6409 41.5513C36.6409 42.5466 36.6628 42.656 36.8597 42.7653C37.2206 42.9622 37.6581 42.8528 37.8112 42.5466C38.03 42.1091 37.9862 40.3919 37.7347 39.5716C37.494 38.7841 36.7722 37.5919 36.4331 37.4169C36.1159 37.2419 35.8206 37.2747 35.6237 37.4935Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M19.2928 41.6172C18.8991 42.2188 19.3038 45.0844 19.8178 45.4125C20.2116 45.6532 20.8897 45.3579 20.8897 44.9422C20.8897 44.8547 20.7913 44.5047 20.671 44.1547C20.5179 43.7172 20.4522 43.2579 20.4522 42.5688C20.4522 41.4969 20.3757 41.3438 19.8288 41.3438C19.5663 41.3438 19.4241 41.4204 19.2928 41.6172Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default CostEffectiveIcon;
