import React from 'react';

const appmobile = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M17.3349 3.02734C11.8173 3.08594 9.60052 3.13477 9.27825 3.22266C7.93059 3.58398 6.97356 4.54102 6.53411 5.95703C6.18255 7.08984 6.15325 7.91993 5.75286 28.7109C5.63567 34.541 5.56731 39.8535 5.59661 40.5273C5.72356 43.2031 6.41692 44.4727 8.24309 45.3613C9.91302 46.1621 11.3095 46.6211 12.9111 46.8848C13.3993 46.9629 15.8212 47.0117 20.8017 47.041C30.2743 47.0996 33.1845 46.9727 36.2802 46.3379C37.8818 46.0059 38.1357 45.9277 38.4775 45.6348C38.8779 45.3027 38.8583 44.8047 38.4482 44.4922C38.1943 44.3066 38.038 44.2871 36.7392 44.2871C35.9482 44.2871 35.04 44.3262 34.7079 44.3652L34.1122 44.4434L34.3954 43.877C34.747 43.1641 34.874 42.3633 34.8056 41.3574C34.7568 40.6055 34.5712 29.2676 34.6103 29.2285C34.62 29.2188 35.1669 30.127 35.831 31.2598C37.1884 33.5645 37.4033 33.7988 38.2236 33.8672C38.8779 33.916 39.2099 33.7695 40.1279 32.998C40.9677 32.2949 41.1435 31.9727 41.1435 31.2207C41.1337 30.6836 41.0849 30.5762 39.8642 28.4863L38.6044 26.3184L40.9091 26.2695C42.8036 26.2305 43.2626 26.1914 43.5068 26.0645C44.249 25.6543 44.4345 25.1563 44.4345 23.6133C44.4345 22.1973 44.2685 21.7383 43.624 21.3086C43.2333 21.0449 43.2236 21.0449 40.9579 20.9961L38.6825 20.9473L39.8935 19.0039C41.124 17.0508 41.1728 16.9238 41.1142 16.1133C41.0849 15.6152 40.8896 15.3027 40.2743 14.7754C39.3368 13.9648 38.9853 13.7695 38.4677 13.7695C37.9208 13.7695 37.54 13.8965 37.2372 14.1797C37.12 14.2969 36.4755 15.2441 35.8212 16.2988L34.62 18.2031L34.5615 17.6758C34.5224 17.3828 34.4638 14.9219 34.4345 12.207C34.3564 6.31836 34.3075 5.61523 33.9658 4.87305C33.6533 4.19922 33.1259 3.59375 32.5302 3.24219L32.081 2.97852L28.5165 2.96875C26.5536 2.95898 21.5243 2.98828 17.3349 3.02734ZM32.0712 4.28711C32.4814 4.55078 32.9599 5.27344 33.0966 5.85938C33.1552 6.10352 33.2138 6.9043 33.2236 7.64649L33.2529 8.98438H20.87H8.47747L8.51653 7.19727C8.54583 5.43946 8.55559 5.40039 8.8095 5.00977C8.96575 4.77539 9.23919 4.53125 9.49309 4.41406C9.90325 4.21875 10.1083 4.20898 20.2158 4.12109C25.8798 4.0625 30.7919 4.0332 31.124 4.05273C31.5341 4.07227 31.8368 4.14062 32.0712 4.28711ZM33.3505 13.4961C33.3896 15.3906 33.3993 17.1484 33.3798 17.4023L33.3505 17.8516L32.2372 16.2402C31.622 15.3613 30.9872 14.541 30.8212 14.4238C30.6357 14.2871 30.3329 14.209 29.9716 14.1797C29.3173 14.1309 29.0048 14.2773 27.9306 15.1563C26.8466 16.0547 26.4853 17.1387 26.954 18.1055C27.0615 18.3301 27.5888 19.0625 28.1259 19.7461L29.1025 20.9961H27.4716C25.5966 20.9961 25.2255 21.0742 24.7568 21.5918C24.3075 22.0898 24.1904 22.5879 24.2392 23.877C24.2685 24.8438 24.2978 25 24.5419 25.4102C24.913 26.0449 25.245 26.3281 25.8798 26.582C26.3486 26.7676 26.5927 26.7969 27.8134 26.7773L29.2197 26.7676L29.0732 26.9824C27.8915 28.8086 26.915 30.4492 26.8466 30.7422C26.749 31.1523 26.8173 31.6602 27.0126 32.0508C27.2568 32.5195 28.8486 33.8477 29.3466 33.9844C29.9814 34.1602 30.6845 34.0723 31.1044 33.75C31.5634 33.3984 32.4033 32.2754 33.0087 31.1914L33.497 30.3223V31.6504C33.497 32.3828 33.5263 34.2969 33.5654 35.918L33.624 38.8672H21.0165H8.39934V31.9531C8.39934 28.1445 8.42864 21.6602 8.4677 17.5488L8.5263 10.0586H20.8993H33.2822L33.3505 13.4961ZM39.2001 15.3906C39.8642 15.9473 40.04 16.1719 40.04 16.4551C40.04 16.543 39.3564 17.6953 38.5263 19.0137C37.6962 20.3418 37.0126 21.4941 37.0126 21.6016C37.0126 21.6992 37.1005 21.8652 37.2079 21.9727C37.3935 22.1582 37.5302 22.168 40.1669 22.168C43.5458 22.168 43.3115 22.0605 43.3115 23.6328C43.3115 25.2051 43.5361 25.0977 40.245 25.0977C38.2724 25.0977 37.4814 25.1367 37.3056 25.2246C36.8173 25.4688 36.9052 25.7031 38.5165 28.4668C39.3564 29.8926 40.04 31.1621 40.04 31.2793C40.0302 31.543 39.9228 31.6797 39.3075 32.1875C38.5751 32.7832 38.4091 32.8516 38.1454 32.6758C38.0185 32.5977 37.1591 31.2305 36.2216 29.6289C35.1279 27.7637 34.4443 26.6992 34.3075 26.6309C34.1513 26.5625 34.0146 26.5625 33.8681 26.6309C33.7314 26.6992 32.9599 27.8223 31.9345 29.4531C30.0595 32.4316 29.8544 32.7148 29.5908 32.7148C29.4052 32.7148 28.4286 31.9922 28.1064 31.6211C28.0087 31.5137 27.9306 31.3184 27.9306 31.1914C27.9306 31.0449 28.6044 29.8828 29.5419 28.3984C30.4306 26.9922 31.1533 25.7715 31.1533 25.6836C31.1533 25.5957 31.0947 25.4395 31.0165 25.332C30.8896 25.1563 30.704 25.1465 28.2724 25.0977C25.8408 25.0488 25.6552 25.0391 25.5283 24.8633C25.3329 24.5898 25.3329 22.6758 25.5283 22.4023C25.6552 22.2266 25.8408 22.2168 28.3603 22.168C30.2841 22.1289 31.0751 22.0898 31.163 22.002C31.4658 21.6992 31.29 21.3477 29.7861 19.2383C28.9658 18.0859 28.2724 17.0605 28.2431 16.9727C28.1454 16.6016 28.3408 16.3086 29.0243 15.7813C29.4052 15.4785 29.7568 15.2344 29.8056 15.2344C30.079 15.2344 30.4208 15.6543 32.0224 17.959C33.0575 19.4434 33.8681 20.5176 33.9853 20.5566C34.1025 20.5957 34.288 20.5566 34.415 20.4688C34.5419 20.3906 35.3818 19.1602 36.2802 17.7246C37.8232 15.2832 38.1552 14.8438 38.4482 14.8438C38.5068 14.8438 38.8486 15.0879 39.2001 15.3906ZM33.6923 41.2402C33.6923 42.6074 33.5556 43.2129 33.1064 43.8281C32.7743 44.2871 32.2275 44.6875 31.7294 44.8242C30.2548 45.2539 17.0322 45.4395 12.7158 45.0879C9.33684 44.8145 9.14153 44.7559 8.64349 43.9063C8.40911 43.5059 8.39934 43.4277 8.39934 41.7676V40.0391H21.0458H33.6923V41.2402Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M16.3086 6.44531C16.2012 6.55273 16.1133 6.72852 16.1133 6.83594C16.1133 6.94336 16.2012 7.11914 16.3086 7.22656C16.4941 7.41211 16.6309 7.42188 19.1895 7.42188C21.748 7.42188 21.8848 7.41211 22.0703 7.22656C22.1777 7.11914 22.2656 6.94336 22.2656 6.83594C22.2656 6.72852 22.1777 6.55273 22.0703 6.44531C21.8848 6.25977 21.748 6.25 19.1895 6.25C16.6309 6.25 16.4941 6.25977 16.3086 6.44531Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M23.0103 6.40625C22.6587 6.74805 22.9224 7.32422 23.4302 7.32422C23.772 7.32422 23.9283 7.1582 23.9283 6.78711C23.9283 6.62109 23.8794 6.43555 23.8111 6.36719C23.6451 6.20117 23.1861 6.2207 23.0103 6.40625Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.8468 18.9062C11.7003 19.1406 11.7003 19.1894 11.8273 19.4238L11.9738 19.6777L18.0675 19.707C23.6242 19.7265 24.171 19.7168 24.337 19.5703C24.5617 19.3555 24.5617 19.0234 24.337 18.8183C24.171 18.6719 23.5949 18.6523 18.087 18.6523H12.0128L11.8468 18.9062Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.1324 25.0684C10.732 25.2148 10.6344 25.6738 10.9371 25.9766C11.1227 26.1621 11.2594 26.1719 15.4098 26.1719C19.1207 26.1719 19.7066 26.1523 19.902 26.0156C20.1754 25.8301 20.1852 25.459 19.9215 25.1953C19.7359 25.0098 19.5992 25 15.5074 25.0098C13.1832 25.0098 11.2106 25.0391 11.1324 25.0684Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M11.8468 30.625C11.7003 30.8594 11.7003 30.9082 11.8273 31.1426L11.9738 31.3965H17.1886H22.4035L22.5499 31.1426C22.6769 30.9082 22.6769 30.8594 22.5304 30.625L22.3644 30.3711H17.1886H12.0128L11.8468 30.625Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M19.6003 41.25C18.5847 42.2559 19.1218 44.4336 20.3815 44.4336C21.1042 44.4336 21.6804 43.6621 21.6804 42.6758C21.6804 41.6406 21.1335 40.918 20.3522 40.918C20.0007 40.918 19.8737 40.9766 19.6003 41.25Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default appmobile;
