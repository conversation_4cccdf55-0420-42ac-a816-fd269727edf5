import React from 'react';

const DataDrivenUiuxIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M40.1078 6.72656C39.9766 6.85781 39.4844 8.62969 39.4844 8.96875C39.4844 9.1875 39.8562 9.40625 40.2063 9.40625C40.4906 9.40625 40.6109 9.35156 40.6875 9.15469C40.8297 8.82656 41.125 7.48125 41.125 7.16406C41.125 6.65 40.4688 6.36563 40.1078 6.72656Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M43.6734 6.90178C43.4328 6.98928 41.8906 8.94709 41.6609 9.45022C41.4641 9.87678 41.7813 10.3033 42.2516 10.2596C42.5578 10.2268 42.7328 10.0627 43.5531 8.96897C44.0781 8.27991 44.5156 7.61272 44.5156 7.48147C44.5156 7.03303 44.1 6.73772 43.6734 6.90178Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M6.56263 10.3794C5.91732 10.5763 5.26107 11.0138 4.58295 11.6919C3.97045 12.3154 3.8392 12.5232 3.63138 13.1794C3.50013 13.5951 3.34701 14.2841 3.28138 14.7107C3.02982 16.5591 3.25951 29.2466 3.71889 38.1388C3.8392 40.3701 3.9267 40.6982 4.64857 41.3216C5.20638 41.8247 5.78607 42.0872 6.57357 42.2185C7.03295 42.2841 11.9329 42.3279 20.5189 42.3279H33.7751L33.8517 42.7326C33.972 43.4216 34.5626 44.7232 35.1314 45.5654C37.1767 48.6169 41.2783 50.1263 44.822 49.1091C46.9876 48.4966 49.4158 46.6591 50.6079 44.756C51.4064 43.4872 51.9314 41.7154 51.647 41.2669C51.5376 41.1029 50.8267 40.9388 47.0204 40.1841L43.7829 39.5497L44.0236 38.6747C44.1548 38.1826 44.6251 36.4982 45.0626 34.9341C45.5986 33.0091 45.8392 31.981 45.7845 31.7841C45.6861 31.3685 44.7892 31.1388 43.2361 31.1279L42.0001 31.1169V30.3513C42.0001 29.9247 41.9454 25.7466 41.8908 21.0654C41.7814 12.8622 41.7704 12.5232 41.5517 12.0419C41.2564 11.3747 40.7533 10.8607 40.0861 10.5435L39.5392 10.281L23.1876 10.2919C14.197 10.2919 6.71576 10.3357 6.56263 10.3794ZM39.6814 11.7576C39.8783 11.8779 40.1517 12.1513 40.2829 12.3591C40.5017 12.7091 40.5236 12.9169 40.5564 14.7326L40.6001 16.7341H23.1876H5.78607L5.81888 14.6669C5.8517 12.3044 5.89545 12.1841 6.78138 11.7248L7.27357 11.4841L23.297 11.506C39.1017 11.5388 39.3204 11.5388 39.6814 11.7576ZM40.6111 20.7482C40.6548 22.2466 40.6876 25.2435 40.6876 27.4201V31.3685L40.1189 31.5654C38.3798 32.1341 36.4548 33.5013 35.3829 34.9341C34.2345 36.4763 33.7095 37.9091 33.6111 39.8341L33.5454 41.0154H20.322C5.6767 41.0154 6.62826 41.0591 6.09232 40.3154L5.8517 39.9763L5.81888 29.006L5.79701 18.0466H23.1658H40.5454L40.6111 20.7482ZM44.2751 32.4622C44.4064 32.5497 43.9798 34.2341 42.7111 38.6091C42.3064 39.9982 42.2954 40.381 42.6345 40.5669C42.7329 40.6216 44.5267 40.9935 46.6048 41.4091L50.4001 42.1529L50.247 42.5685C49.1642 45.6638 46.2548 47.6872 42.9298 47.6982C38.7079 47.6982 35.3283 44.2857 35.3283 40.031C35.3283 36.5091 37.6361 33.5232 41.0814 32.5935C41.8689 32.3748 44.0017 32.2982 44.2751 32.4622Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M12.4906 12.8734C12.3594 12.8953 12.075 13.0812 11.8672 13.2781C11.5172 13.6062 11.4844 13.6937 11.4844 14.2516C11.4844 14.7437 11.5391 14.9187 11.7688 15.1922C12.3484 15.8813 13.3656 15.8813 13.9453 15.1922C14.2953 14.7656 14.3063 14.0437 13.9672 13.3437C13.7375 12.8734 13.7047 12.8516 13.2344 12.8406C12.9609 12.8297 12.6328 12.8406 12.4906 12.8734Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M8.37779 13.0151C7.92935 13.1573 7.55747 13.5401 7.41529 14.0213C7.31685 14.3604 7.32779 14.5135 7.48091 14.9182C7.57935 15.1807 7.77622 15.476 7.90747 15.5635C8.88091 16.187 10.1715 15.5854 10.1715 14.5026C10.1715 13.9557 9.82154 13.0698 9.55904 12.9713C9.32935 12.8838 8.7606 12.9057 8.37779 13.0151Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M16.2203 13.1137C15.6734 13.3653 15.4219 13.8028 15.4219 14.5028C15.4219 14.9293 15.4766 15.0606 15.7828 15.3668C16.2641 15.8481 16.8 15.9465 17.3469 15.684C18.2 15.2575 18.3969 14.5028 17.9266 13.5075C17.675 12.9825 17.6422 12.9606 17.1719 12.9278C16.8656 12.9168 16.5047 12.9825 16.2203 13.1137Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M23.9212 13.5406C23.6587 13.825 23.6915 14.2844 23.9759 14.4813C24.1947 14.6344 24.8728 14.6562 29.204 14.6562C34.6947 14.6562 34.6728 14.6562 34.6728 14C34.6728 13.3219 34.8369 13.3438 29.215 13.3438C24.3915 13.3438 24.0853 13.3547 23.9212 13.5406Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M12.1634 23.4716C12.0103 23.581 11.9228 23.756 11.9228 23.9529C11.9228 24.6419 11.3431 24.6091 22.9368 24.6091C33.3274 24.6091 33.4696 24.6091 33.6884 24.3904C33.8087 24.2701 33.9071 24.0732 33.9071 23.9529C33.9071 23.8326 33.8087 23.6357 33.6884 23.5154C33.4696 23.2966 33.3274 23.2966 22.9368 23.2966C13.5853 23.2966 12.3821 23.3185 12.1634 23.4716Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M11.1571 29.8484C11.004 30.1547 11.0259 30.4719 11.2228 30.6688C11.3759 30.8219 12.4587 30.8438 21.4821 30.8438C31.1181 30.8438 31.5884 30.8328 31.7524 30.6469C31.9821 30.3953 31.9931 30.0344 31.7634 29.8156C31.6103 29.6625 30.5274 29.6406 21.4384 29.6406C11.4962 29.6406 11.2665 29.6406 11.1571 29.8484Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M17.5 35.5469C17.3797 35.6672 17.2812 35.8641 17.2812 35.9844C17.2812 36.1047 17.3797 36.3016 17.5 36.4219C17.7078 36.6297 17.8609 36.6406 22.9469 36.6406C27.8797 36.6406 28.1969 36.6297 28.3609 36.4438C28.6234 36.1594 28.5906 35.7 28.3062 35.5031C28.0875 35.35 27.3875 35.3281 22.8922 35.3281C17.8609 35.3281 17.7078 35.3391 17.5 35.5469Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M44.2978 11.0031C44.0244 11.0797 43.6197 11.1453 43.4009 11.1453C42.8541 11.1562 42.5041 11.4953 42.6025 11.9C42.6353 12.0641 42.7228 12.2391 42.7775 12.3047C43.0291 12.5672 45.0525 12.2609 45.4025 11.9109C45.665 11.6484 45.665 11.2437 45.4134 11.0141C45.1837 10.8062 44.9869 10.7953 44.2978 11.0031Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M47.7196 32.0246C47.654 32.0683 46.8993 33.6543 46.0462 35.5355C44.4274 39.123 44.3509 39.3855 44.8759 39.5824C45.1493 39.6918 52.1712 38.9043 52.5868 38.7183C52.7837 38.6308 52.8274 38.5215 52.8274 38.1605C52.8274 37.1105 52.1931 35.4918 51.3399 34.3433C50.2353 32.8777 48.3103 31.6418 47.7196 32.0246ZM48.9337 33.7855C49.9509 34.4855 51.0665 36.0605 51.3837 37.2199C51.4931 37.5918 51.4821 37.6246 51.2634 37.6793C50.8915 37.7558 46.3196 38.2699 46.2868 38.2262C46.2431 38.1933 48.3649 33.4683 48.4306 33.4683C48.4524 33.4683 48.6821 33.6105 48.9337 33.7855Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default DataDrivenUiuxIcon;
