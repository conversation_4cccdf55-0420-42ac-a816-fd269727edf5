import React from 'react';

const ecomresponsive = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
        >
            <path
                d="M42.3283 3.59835C40.1846 3.65304 39.1127 3.71866 38.883 3.8171C38.4455 4.00304 38.0955 4.33117 37.7783 4.88898L37.5049 5.33741L37.5377 9.2421C37.5705 12.9609 37.5814 13.1468 37.7783 13.2999C37.9314 13.4093 38.3361 13.453 39.1455 13.453L40.3049 13.464L40.7971 14.3499C41.1689 15.0062 41.3986 15.2905 41.683 15.4327C42.2408 15.728 42.4814 15.6077 42.9408 14.7984C43.1486 14.4155 43.4768 13.9234 43.6627 13.7155L43.9908 13.3437H45.2705C47.1627 13.3437 49.9736 13.1359 50.3893 12.9609C50.8486 12.764 51.1549 12.2499 51.2424 11.5499C51.2752 11.2437 51.2643 9.5921 51.2205 7.87492C51.0893 3.47804 51.2096 3.60929 47.6658 3.56554C46.4408 3.54366 44.0455 3.56554 42.3283 3.59835ZM49.7877 5.00929C49.8861 5.10773 49.9299 5.99366 49.9627 8.41085C50.0064 11.5937 50.0064 11.6812 49.7986 11.8015C49.6783 11.8671 48.2236 11.9765 46.583 12.0421C43.2033 12.1843 43.1814 12.1952 42.4924 13.0812L42.1314 13.5734L41.6393 12.8077C41.3658 12.3921 41.0377 12.0202 40.9064 11.9765C40.7643 11.9327 40.2939 11.9327 39.8564 11.9765L39.0471 12.053L39.0252 9.49366C38.9814 5.49054 38.9814 5.38116 39.1783 5.17335C39.2768 5.07491 39.5502 4.96554 39.7799 4.91085C40.0096 4.8671 42.3174 4.84523 44.9205 4.84523C48.5408 4.8671 49.6783 4.89991 49.7877 5.00929Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M43.936 6.25623C43.7063 6.44217 43.5532 6.85779 43.6188 7.18592C43.7282 7.76561 44.7563 8.00623 45.1938 7.56873C45.5001 7.26248 45.4891 6.57342 45.1719 6.32186C44.8876 6.09217 44.2094 6.05936 43.936 6.25623Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M43.3671 8.62957C42.7984 8.89207 42.0218 9.66864 41.9015 10.1061C41.8577 10.2921 41.8249 10.4671 41.8468 10.478C41.8577 10.4999 42.9843 10.478 44.3296 10.4452L46.7906 10.3577L46.6921 10.0296C46.5499 9.53739 46.0468 8.9577 45.4999 8.67332C44.9093 8.35614 43.9906 8.33426 43.3671 8.62957Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M25.4295 7.79827C24.1061 8.14827 23.0889 9.01233 22.4764 10.3139C21.8092 11.703 21.853 13.0811 22.5858 14.0436L22.9358 14.5139L22.553 14.7217C21.8202 15.1155 20.8467 16.2092 20.3217 17.2483C19.5014 18.8233 19.217 19.9826 18.867 23.1873C18.7249 24.4561 18.2436 27.8358 17.8717 30.0561C17.7514 30.778 17.9374 31.4123 18.3858 31.8717L18.8124 32.2873L19.5999 32.2326C20.0374 32.1998 20.6608 32.1014 20.978 32.003C21.5358 31.8389 21.5795 31.8498 22.6733 32.178C23.942 32.5608 25.2545 32.6264 26.1514 32.342C26.5233 32.2326 27.0045 32.1998 27.967 32.2436C28.9295 32.2764 29.4217 32.2545 29.8702 32.1342C30.2639 32.0248 30.7342 31.9811 31.2811 32.0248C31.817 32.0576 32.4624 32.003 33.1624 31.8826C34.1139 31.7076 34.3327 31.6967 35.153 31.828C36.8155 32.0905 37.7124 31.7514 38.2374 30.6576C38.5108 30.1217 38.5545 29.8811 38.5874 28.7655C38.6967 25.8889 37.9311 20.3217 37.067 17.6967C35.9733 14.3389 33.9827 12.567 31.0295 12.2936L30.242 12.228V11.3311C30.2311 10.2045 29.9686 9.60296 29.1592 8.80452C28.1967 7.87484 26.7092 7.47015 25.4295 7.79827ZM27.2452 9.09984C28.2077 9.38421 28.9733 10.4014 28.9842 11.3858C28.9842 12.217 28.8202 12.5233 28.2952 12.6873C27.6827 12.8623 27.1467 13.1467 26.567 13.5842C26.0967 13.9451 26.0749 13.9451 25.2108 13.8905C24.6202 13.8576 24.2264 13.7701 24.0295 13.6389C23.2202 13.103 23.0561 11.9983 23.6358 10.8498C24.3686 9.38421 25.8342 8.67327 27.2452 9.09984ZM32.0358 13.7701C33.8186 14.328 35.0545 15.6951 35.8202 17.9701L36.1045 18.8233L35.3061 19.578L34.5077 20.3326L34.267 20.092C34.1358 19.9608 33.8077 19.4576 33.5561 18.9764C33.1186 18.167 33.053 18.1014 32.7139 18.0686C32.353 18.0358 32.2436 18.1233 30.8217 19.567L29.3124 21.0983L29.0499 20.5733C28.8967 20.2889 28.7108 19.7748 28.6124 19.4139C28.4374 18.7248 28.317 18.5936 27.8686 18.5936C27.6389 18.5936 27.3764 18.8233 26.5999 19.6545C26.053 20.2451 25.3858 21.0108 25.1014 21.3498C24.817 21.6998 24.5327 21.9842 24.467 21.9842C24.2811 21.9842 23.5155 21.1201 23.0124 20.3217C22.542 19.5889 22.5092 19.567 22.1155 19.5998C21.7436 19.6326 21.6889 19.6873 21.3389 20.3326L20.967 21.0436L20.7702 20.792C20.4967 20.4639 20.5077 20.267 20.8467 19.2717C21.4264 17.5326 22.5092 16.0998 23.6795 15.5201C24.1499 15.2905 24.3795 15.2576 25.3749 15.2576H26.5233L27.1249 14.8092C27.453 14.5576 27.9014 14.2623 28.1092 14.153C29.3014 13.5514 30.8327 13.3983 32.0358 13.7701ZM32.7795 20.2233C32.9217 20.4639 33.228 20.8905 33.4795 21.1748L33.9389 21.6998L33.8624 22.9686C33.7311 25.3639 33.3811 26.917 32.7249 28.1092C32.2655 28.9623 31.1061 30.1326 30.3514 30.5373C28.4811 31.5217 26.1952 31.0186 24.3905 29.203C23.1764 27.9889 22.4874 26.5123 21.9842 24.0514C21.667 22.5092 21.6889 22.192 22.1592 21.5686C22.1702 21.5467 22.542 21.8967 22.9686 22.3561C23.8983 23.3295 24.4014 23.6686 24.7624 23.5811C24.9045 23.5483 25.3092 23.1655 25.6592 22.728C26.0092 22.2905 26.578 21.6014 26.9389 21.1967L27.5842 20.4639L27.9561 21.1967C28.7545 22.7936 29.3124 23.1108 29.9905 22.3451C31.6092 20.5295 32.3202 19.7967 32.4733 19.7967C32.5061 19.7967 32.6483 19.9936 32.7795 20.2233ZM36.7936 22.4217C37.1764 24.9045 37.3077 26.228 37.3295 28.0326C37.3514 29.3889 37.3186 29.6405 37.1327 30.0233C36.7936 30.7014 36.5092 30.7889 35.2295 30.5592C34.2342 30.3842 34.103 30.3842 33.3592 30.5592C32.9217 30.6576 32.517 30.7342 32.4624 30.7342C32.4077 30.7342 32.6045 30.4498 32.9108 30.1108C33.5889 29.3561 34.2561 28.0983 34.5514 27.0155C34.803 26.1186 35.0545 24.303 35.1639 22.5858L35.2405 21.492L35.7983 20.9233C36.1811 20.5295 36.378 20.3873 36.4327 20.4858C36.4764 20.5514 36.6405 21.4264 36.7936 22.4217ZM20.628 23.7233C21.0983 26.8623 22.4217 29.4873 24.1936 30.7998L24.8499 31.2811H24.4561C24.2374 31.2811 23.6249 31.128 23.078 30.953C21.9077 30.5701 21.492 30.5483 20.3217 30.8436C18.9874 31.1936 18.9764 31.1717 19.403 28.4373C19.5561 27.5076 19.7749 25.9764 19.8952 25.0467C20.0264 24.117 20.1577 23.1326 20.1905 22.8701C20.2889 22.1592 20.4311 22.4545 20.628 23.7233Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M27.5076 33.9171C25.4076 34.0702 23.767 34.5405 21.6014 35.6124C19.9389 36.4327 18.5279 37.4171 17.2264 38.653L16.2967 39.539L16.0561 39.3312C14.8092 38.314 12.0639 37.789 10.1498 38.2155C7.57949 38.7843 5.67637 40.6218 4.96543 43.203C4.70293 44.1546 4.70293 46.0577 4.96543 47.0312C5.32637 48.3546 5.87324 49.2952 6.89043 50.3233C7.64512 51.089 7.99512 51.3515 8.7498 51.7233C9.92012 52.2921 11.1123 52.5437 12.3045 52.4562C14.9514 52.2702 16.8326 50.903 18.2982 48.0921L18.517 47.6655L19.4029 47.7312C19.8951 47.764 24.9701 47.8296 30.7014 47.8624C42.5357 47.9499 41.4639 48.0374 41.967 46.9655C42.3061 46.2546 42.2732 45.9812 41.7264 44.3405C41.0373 42.2515 39.7686 40.0421 38.1498 38.1062C35.6123 35.0546 31.9154 33.5999 27.5076 33.9171ZM31.3357 35.3827C31.6967 35.4812 32.0357 35.6015 32.0795 35.6452C32.2217 35.7874 31.5217 36.3124 30.6795 36.7062C29.3232 37.3515 27.9232 37.3952 26.4686 36.8593C25.8451 36.6187 24.8279 35.9515 24.8279 35.7765C24.8279 35.7218 25.2873 35.5687 25.8451 35.4483C26.392 35.328 26.9498 35.1968 27.0701 35.1749C27.5732 35.0546 30.6904 35.2077 31.3357 35.3827ZM25.0139 37.5921C27.0045 38.7515 29.6842 38.7952 31.6748 37.6796C32.0467 37.4718 32.6373 37.0343 32.9764 36.728L33.5998 36.1593L34.2779 36.5093C36.1045 37.4608 38.117 39.7468 39.5826 42.5577C40.1514 43.6405 40.8514 45.489 40.7311 45.5983C40.6326 45.6968 23.1654 45.6749 20.9889 45.5655L19.0639 45.478L18.9982 44.5702C18.9107 43.3562 18.4732 42.0765 17.817 41.1249L17.292 40.3593L18.2326 39.4952C19.4029 38.4015 20.7592 37.4608 22.2029 36.7499L23.3295 36.203L23.9201 36.7499C24.2482 37.0562 24.7404 37.428 25.0139 37.5921ZM13.9451 39.6921C16.2201 40.5015 17.7186 42.6124 17.7186 45.0077C17.7186 47.3921 16.242 49.4812 13.9451 50.3343C13.0154 50.6733 11.2764 50.6733 10.3357 50.3233C8.64043 49.7108 7.44824 48.4858 6.81387 46.7249C6.50762 45.8718 6.46387 44.3515 6.72637 43.4983C7.31699 41.5733 8.93574 40.0093 10.8498 39.528C11.6592 39.3202 13.1248 39.3968 13.9451 39.6921Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M11.6155 41.1906C11.3858 41.3438 11.3748 41.4313 11.3748 42.8859V44.4062H9.7342C8.23577 44.4062 8.09358 44.4281 7.9842 44.6141C7.82014 44.9313 7.85295 45.2266 8.0717 45.4234C8.23577 45.5766 8.53108 45.6094 9.8217 45.6094H11.3748V47.1187C11.3748 48.7703 11.4186 48.8906 11.9764 48.8906C12.5342 48.8906 12.578 48.7703 12.578 47.1187V45.6094H14.142C15.5311 45.6094 15.7389 45.5875 15.892 45.4125C16.0998 45.1828 16.1217 44.9203 15.9686 44.6141C15.8592 44.4281 15.717 44.4062 14.2186 44.4062H12.578V42.8859C12.578 41.4313 12.567 41.3438 12.3373 41.1906C12.2061 41.0922 12.042 41.0156 11.9764 41.0156C11.9108 41.0156 11.7467 41.0922 11.6155 41.1906Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default ecomresponsive;
