import React from 'react';

const Twitter = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="37"
            height="36"
            viewBox="0 0 37 36"
            fill="none"
        >
            <g clipPath="url(#clip0_1012_643651)">
                <path
                    d="M16.2147 0.10498C12.9663 0.526855 9.99209 1.77139 7.39053 3.78233C6.56787 4.42217 5.16865 5.80029 4.48662 6.64404C2.546 9.04873 1.25928 11.8894 0.71084 14.9831C0.513965 16.08 0.436621 18.7167 0.563184 19.919C0.865527 22.7245 1.84287 25.4737 3.36162 27.8081C4.05068 28.8628 4.65537 29.6151 5.59756 30.5784C8.54365 33.6019 12.2632 35.43 16.496 35.9222C17.4382 36.0347 19.7304 36.0136 20.7499 35.894C26.0093 35.2401 30.7062 32.294 33.6382 27.8081C35.1147 25.5511 36.0569 22.9565 36.3944 20.2495C36.5351 19.1105 36.5351 16.8956 36.3944 15.7495C35.7476 10.5323 32.8085 5.82139 28.3788 2.91045C26.0655 1.3917 23.4921 0.449512 20.7499 0.10498C19.6812 -0.0215836 17.2272 -0.0215836 16.2147 0.10498ZM16.9812 11.2565C19.0554 14.28 19.6601 15.1097 19.7304 15.0464C19.7796 15.0042 21.2843 13.2675 23.0772 11.1792L26.3397 7.38233H27.2819C28.1749 7.38233 28.2171 7.38936 28.1187 7.50186C24.3991 11.7839 20.539 16.3542 20.5671 16.4315C20.5952 16.4878 22.4444 19.2089 24.6944 22.4784C26.9374 25.748 28.7937 28.462 28.8147 28.5183C28.8429 28.6026 28.3718 28.6167 25.7983 28.6167C22.9718 28.6167 22.7327 28.6097 22.6343 28.4901C22.5569 28.4058 18.739 22.8722 17.1288 20.5097C17.0515 20.4042 16.6296 20.8683 13.5007 24.5034L9.96397 28.6167H9.05693C8.50146 28.6167 8.16397 28.5886 8.17803 28.5464C8.19209 28.5112 9.98506 26.4019 12.1718 23.8636C14.3585 21.3253 16.1515 19.223 16.1655 19.1948C16.1796 19.1667 14.3796 16.5159 12.1718 13.3026C9.971 10.0964 8.16397 7.45264 8.16397 7.42451C8.16397 7.40342 9.54912 7.38233 11.2437 7.38233H14.3233L16.9812 11.2565Z"
                    fill="var(--color-black-200)"
                />
                <path
                    d="M10.7444 8.8875C10.7726 8.94375 13.6624 13.0852 17.164 18.0984L23.5272 27.2039L24.8843 27.2109C25.6226 27.2109 26.2343 27.1898 26.2343 27.1688C26.2343 27.1477 23.3515 22.9992 19.8218 17.9578L13.4163 8.78906H12.0522C10.8991 8.78906 10.6952 8.80313 10.7444 8.8875Z"
                    fill="var(--color-black-200)"
                />
                <path
                    d="M8.34678 7.64297C8.41709 7.75547 10.196 10.357 12.3124 13.4297C14.4218 16.5023 16.1726 19.0547 16.2007 19.1039C16.2288 19.1531 14.7382 20.9461 12.3335 23.7445C10.1749 26.2547 8.36787 28.3641 8.31162 28.4273C8.22021 28.5398 8.27646 28.5469 9.09209 28.5469H9.971L13.5077 24.4336L17.0444 20.3203L17.5647 21.0727C17.853 21.4945 19.1187 23.3438 20.3843 25.1859L22.6835 28.5398L25.728 28.5469C27.3944 28.5469 28.7655 28.5328 28.7655 28.5188C28.7655 28.4977 26.9093 25.7836 24.6452 22.4859C22.3741 19.1883 20.5108 16.4531 20.4968 16.418C20.4757 16.3758 22.1772 14.3578 24.2655 11.925C26.3538 9.49922 28.0624 7.50234 28.0624 7.48125C28.0624 7.46719 27.6757 7.45312 27.2046 7.45312H26.3397L23.014 11.3203C21.1858 13.4508 19.6671 15.1734 19.639 15.1594C19.6108 15.1453 18.4015 13.4016 16.953 11.2922L14.3163 7.45312H11.2718H8.22021L8.34678 7.64297ZM19.871 17.9438C23.3937 22.9781 26.2976 27.1336 26.3257 27.1898C26.3749 27.2672 26.1288 27.2812 24.9546 27.2812H23.5272L17.5507 18.7242C14.2671 14.0203 11.3562 9.85781 11.096 9.47812L10.6179 8.78906H12.0452H13.4726L19.871 17.9438Z"
                    fill="#ECECEC"
                />
            </g>
            <defs>
                <clipPath id="clip0_1012_643651">
                    <rect width="36" height="36" fill="white" transform="translate(0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default Twitter;
