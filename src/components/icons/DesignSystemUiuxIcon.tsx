import React from 'react';

const DesignSystemUiuxIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.3027 7.31348C10.1465 7.3916 9.84375 7.61621 9.64844 7.81152C9.33594 8.11426 9.19922 8.16309 8.75 8.19238C8.10547 8.23145 7.71484 8.36816 7.71484 8.54395C7.71484 8.72949 8.25195 9.46191 8.49609 9.6084C8.64258 9.70605 8.69141 9.84277 8.69141 10.1553C8.69141 10.624 8.87695 11.4443 8.97461 11.4443C9.12109 11.4443 9.94141 10.8682 10.1172 10.6436C10.2832 10.4287 10.4102 10.3896 11.0156 10.3506C11.3965 10.3311 11.7676 10.2627 11.8359 10.1943C11.9922 10.0381 11.8262 9.70605 11.3281 9.15918C11.0156 8.82715 10.9375 8.66113 10.9375 8.37793C10.9375 7.96777 10.752 7.14746 10.6543 7.14746C10.625 7.15723 10.4688 7.22559 10.3027 7.31348Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M20.8006 9.93113C17.2167 10.4292 13.7889 12.1479 10.9764 14.8823L10.3905 15.4487L9.7069 15.3511C9.33581 15.3022 8.4862 15.2534 7.81237 15.2534C4.95104 15.2534 3.03698 16.1128 2.22643 17.7729C1.9237 18.3784 1.90417 18.4858 1.91393 19.4038C1.9237 20.2436 1.96276 20.4878 2.22643 21.1909C2.76354 22.6264 3.66198 23.8862 5.29284 25.4878L6.22057 26.4057L6.27917 27.7827C6.40612 30.771 7.16784 33.3979 8.60338 35.81C12.6854 42.6753 21.1424 45.9956 28.701 43.7007C31.162 42.9585 33.496 41.5913 35.5663 39.6772L36.3085 38.9839H37.8124C41.0057 38.9741 43.3397 38.3296 44.5506 37.1186C45.1756 36.4936 45.4296 35.9077 45.4881 34.98C45.6151 32.8803 44.2186 30.6733 41.4452 28.5737L40.7518 28.0464L40.6932 26.5522C40.6053 24.3257 40.1464 22.4507 39.2089 20.478L38.7889 19.6089L39.1503 19.1987C39.5702 18.7104 39.9901 17.8999 40.1268 17.2749C40.1854 17.0307 40.2147 16.2007 40.1952 15.4292C40.1659 14.2768 40.1268 13.9448 39.951 13.4956C39.16 11.5327 36.9725 10.3315 34.7753 10.6538C33.9061 10.7807 32.8514 11.2593 32.2264 11.8159L31.8065 12.187L30.5956 11.5718C27.5975 10.0581 24.1307 9.47215 20.8006 9.93113ZM26.8553 10.9272C28.0565 11.103 29.785 11.6011 30.8983 12.0991C31.328 12.2944 31.7089 12.4702 31.7284 12.48C31.7479 12.4995 31.6014 12.7632 31.4159 13.0757C30.4296 14.7065 30.371 16.6108 31.2694 18.31C31.9042 19.4917 33.3885 20.5366 34.8436 20.8198C36.0057 21.0542 37.4608 20.771 38.3495 20.146L38.7596 19.853L38.9745 20.4096C40.9276 25.5854 40.3026 31.0737 37.2264 35.6733C36.2206 37.1675 36.3573 37.0893 34.9217 36.9526C33.4764 36.8061 31.6796 36.5425 29.8339 36.2007C28.5546 35.9663 23.5253 34.8823 23.4569 34.8335C23.4374 34.8139 23.4667 34.5112 23.5155 34.1596C23.7499 32.3725 22.9881 30.8003 21.5038 30.0776C20.8299 29.7456 20.1464 29.6577 18.4667 29.687C17.6171 29.6968 16.7674 30.1167 16.1424 30.8198L15.6932 31.3374L14.0721 30.1557C12.3827 28.9253 10.3612 27.3432 9.13073 26.269L8.40807 25.6538V25.0093C8.38854 23.4858 8.96471 21.3374 9.87292 19.5698C12.1483 15.1069 16.1229 12.0991 21.0839 11.0932C23.2225 10.6636 24.746 10.6147 26.8553 10.9272ZM37.744 12.5679C38.7889 13.0659 39.619 14.0913 39.9315 15.2729C40.0389 15.6636 40.0389 15.8003 39.8924 16.2495C39.4237 17.6557 38.6131 18.5249 37.3241 18.9741C36.8846 19.1304 36.5819 19.1694 35.869 19.1401C35.078 19.1108 34.8924 19.062 34.3163 18.769C33.1249 18.1538 32.5194 17.1382 32.5194 15.7417C32.5194 14.5112 33.0272 13.4468 33.9256 12.8413C34.6385 12.353 35.1561 12.2261 36.2303 12.2554C37.0116 12.2846 37.246 12.3335 37.744 12.5679ZM9.87292 16.4155C8.55456 17.9878 7.25573 20.7319 6.78698 22.9194C6.69909 23.3491 6.6112 23.7593 6.5819 23.8276C6.53307 23.9936 5.27331 22.6167 4.66784 21.7182C4.0819 20.8491 3.71081 19.9214 3.71081 19.3257C3.72057 18.1245 4.81432 17.1772 6.84557 16.5815C7.5487 16.3862 8.10534 16.2788 9.86315 16.0054C9.99987 15.9761 10.1464 15.9565 10.1854 15.9468C10.2342 15.9468 10.0878 16.1518 9.87292 16.4155ZM9.50182 28.7788C10.6737 29.5405 12.7049 30.5757 14.1112 31.1225C14.7557 31.3764 15.2928 31.5913 15.3026 31.6011C15.3124 31.6108 15.2342 31.9038 15.1268 32.2456C14.8339 33.1636 14.912 34.2475 15.3124 35.1264C15.6542 35.8393 16.4159 36.7085 17.0311 37.0698C18.2811 37.8022 20.0975 37.8022 21.3475 37.0698C21.8651 36.7671 22.578 36.0151 22.8417 35.478C22.9589 35.2632 23.0663 35.0776 23.0858 35.0776C23.1053 35.0776 23.6327 35.3218 24.2674 35.6245C27.2264 37.021 30.7128 38.1636 33.3397 38.603C33.7499 38.6714 34.0819 38.7593 34.0819 38.8081C34.0819 38.935 33.0174 39.7358 32.1581 40.2632C30.5467 41.2397 28.8671 41.9038 26.953 42.3139C26.0839 42.5093 25.6346 42.5386 23.9745 42.5386C22.3143 42.5386 21.8651 42.5093 20.996 42.3139C16.6698 41.3862 13.0858 39.0229 10.7323 35.5268C9.38463 33.5346 8.4569 30.9468 8.24206 28.6225L8.1737 27.9096L8.41784 28.0659C8.54479 28.1538 9.04284 28.4761 9.50182 28.7788ZM41.8358 29.853C43.0272 31.4155 43.8085 32.978 43.9061 33.9936C44.1014 35.8393 42.451 36.855 39.0331 37.0014L37.7831 37.06L38.2616 36.2788C39.0038 35.0971 39.4432 34.1694 39.8436 32.9292C40.2049 31.8257 40.6249 29.7358 40.6249 29.0229C40.6249 28.3296 40.7421 28.4077 41.8358 29.853ZM20.2635 30.3022C21.4256 30.8882 22.0506 31.9917 21.953 33.271C21.7674 35.5464 19.287 36.5425 17.5975 35.0288C16.9237 34.4233 16.6698 33.8667 16.66 32.9878C16.66 32.1675 16.8749 31.6011 17.4413 30.9761C18.0467 30.312 19.0917 29.7456 19.4335 29.9018C19.5116 29.9311 19.8924 30.1167 20.2635 30.3022Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M46.5338 18.5439C46.3776 18.6221 46.0553 18.8662 45.8307 19.0811C45.6159 19.2959 45.3815 19.4424 45.3229 19.4131C45.1862 19.3252 44.1315 19.5205 44.0046 19.6475C43.8678 19.7842 44.1901 20.3604 44.6003 20.7217C44.8639 20.9561 44.9225 21.083 44.9225 21.4053C44.9225 21.8545 45.1081 22.6748 45.2057 22.6748C45.3717 22.6748 46.1921 22.0791 46.3385 21.8643C46.4753 21.6592 46.5827 21.6201 47.2272 21.5811C47.6276 21.5518 48.0085 21.4834 48.0573 21.4346C48.194 21.2979 48.0475 21.0146 47.5495 20.4092C47.2565 20.0674 47.1686 19.8721 47.1686 19.5986C47.1686 19.1982 46.9831 18.3779 46.8854 18.3779C46.8561 18.3877 46.6999 18.4561 46.5338 18.5439Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M6.00573 38.1831C5.83972 38.2906 5.56628 38.5054 5.3905 38.6812C5.12683 38.9546 4.99011 39.0035 4.51159 39.0328C3.30066 39.1109 3.21276 39.316 4.00378 40.1851C4.44323 40.6734 4.46276 40.7222 4.51159 41.3863C4.54089 41.7671 4.58972 42.1285 4.62878 42.1968C4.72644 42.3433 5.33191 42.021 5.74206 41.5913C6.02526 41.2984 6.13269 41.2495 6.57214 41.2398C7.07995 41.23 7.71472 41.0738 7.71472 40.9566C7.71472 40.8101 7.32409 40.2632 6.99206 39.941C6.6698 39.6285 6.6405 39.5503 6.6405 39.0523C6.6405 38.5054 6.51355 38.0074 6.37683 38.0074C6.328 38.0074 6.16198 38.0855 6.00573 38.1831Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default DesignSystemUiuxIcon;
