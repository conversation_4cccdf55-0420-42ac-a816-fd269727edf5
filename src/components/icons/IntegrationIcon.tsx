import React from 'react';

const IntegrationIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width={32} height={32} fill="none" viewBox="0 0 24 24" {...props}>
        <circle cx="12" cy="6" r="2" stroke="#222" strokeWidth={2} fill="var(--color-gray-200)" />
        <circle cx="6" cy="18" r="2" stroke="#222" strokeWidth={2} fill="var(--color-gray-200)" />
        <circle cx="18" cy="18" r="2" stroke="#222" strokeWidth={2} fill="var(--color-gray-200)" />
        <path d="M12 8v8m-4-2 8 0" stroke="#222" strokeWidth={2} />
    </svg>
);

export default IntegrationIcon;
