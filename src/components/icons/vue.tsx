import React from 'react';

const vue = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="46"
            height="40"
            viewBox="0 0 46 40"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337751)">
                <mask
                    id="mask0_1749_337751"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="46"
                    height="40"
                >
                    <path d="M45.5 0.5H0.5V39.5H45.5V0.5Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337751)">
                    <path
                        d="M36.4966 0.5H45.4958L22.9979 39.4647L0.5 0.5H17.7109L22.9979 9.53529L28.1724 0.5H36.4966Z"
                        fill="#41B883"
                    />
                    <path
                        d="M0.5 0.5L22.9979 39.4647L45.4958 0.5H36.4966L22.9979 23.8788L9.38666 0.5H0.5Z"
                        fill="#41B883"
                    />
                    <path
                        d="M9.38672 0.5L22.9979 23.9918L36.4967 0.5H28.1725L22.9979 9.53529L17.7109 0.5H9.38672Z"
                        fill="#35495E"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_1749_337751">
                    <rect width="45" height="39" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default vue;
