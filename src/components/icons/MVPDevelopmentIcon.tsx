import React from 'react';

const MVPDevelopmentIcon = ({ fill = 'white' }) => {
    return (
        <svg
            height="21"
            width="20"
            fill="none"
            viewBox="0 0 20 21"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_964_647374)">
                <path
                    d="M1.56594 15.3982C0.84192 16.3979 0.322273 17.5306 0.0367782 18.7315C-0.0177567 18.9706 -0.0107298 19.2196 0.0572011 19.4552C0.125132 19.6909 0.251745 19.9054 0.425186 20.0788C0.598626 20.2521 0.813222 20.3786 1.04888 20.4465C1.28454 20.5143 1.53356 20.5212 1.77261 20.4665C2.97165 20.1806 4.10262 19.6613 5.10094 18.9382C5.57038 18.4694 5.83437 17.8334 5.83484 17.17C5.8353 16.5066 5.57222 15.8701 5.10345 15.4007C4.63468 14.9313 3.99862 14.6673 3.33521 14.6668C2.6718 14.6663 2.03538 14.9294 1.56594 15.3982ZM3.92261 17.7557C3.25095 18.1859 2.52139 18.5181 1.75595 18.7424C1.98051 17.977 2.31272 17.2475 2.74261 16.5757C2.89978 16.4239 3.11028 16.3399 3.32878 16.3418C3.54728 16.3437 3.75629 16.4313 3.91079 16.5858C4.0653 16.7404 4.15294 16.9494 4.15484 17.1679C4.15674 17.3864 4.07274 17.5969 3.92094 17.754L3.92261 17.7557ZM15.0001 7.5832C15.0001 8.13573 14.7806 8.66563 14.3899 9.05633C13.9992 9.44704 13.4693 9.66653 12.9168 9.66653C12.3642 9.66653 11.8343 9.44704 11.4436 9.05633C11.0529 8.66563 10.8334 8.13573 10.8334 7.5832C10.8334 7.03066 11.0529 6.50076 11.4436 6.11006C11.8343 5.71936 12.3642 5.49986 12.9168 5.49986C13.4693 5.49986 13.9992 5.71936 14.3899 6.11006C14.7806 6.50076 15.0001 7.03066 15.0001 7.5832ZM17.4768 0.499862C15.4937 0.488073 13.5345 0.933468 11.7514 1.80147C9.96825 2.66947 8.40915 3.93673 7.19511 5.50486C5.87106 5.55064 4.57242 5.8812 3.38761 6.47403C2.01363 7.10672 0.891979 8.18259 0.202612 9.52903C0.040836 9.90467 -0.0248302 10.3147 0.0115423 10.7221C0.0479148 11.1294 0.185181 11.5213 0.410945 11.8624C0.638864 12.2118 0.950351 12.4988 1.31719 12.6974C1.68402 12.896 2.09462 13 2.51178 12.9999H4.58345C5.35585 13.0036 6.09555 13.3121 6.64172 13.8582C7.1879 14.4044 7.49639 15.1441 7.50011 15.9165V17.9882C7.49982 18.4052 7.60353 18.8157 7.80186 19.1825C8.00018 19.5493 8.28686 19.8609 8.63595 20.089C8.97698 20.3148 9.36888 20.4521 9.77625 20.4884C10.1836 20.5248 10.5936 20.4591 10.9693 20.2974C12.3157 19.608 13.3916 18.4863 14.0243 17.1124C14.6171 15.9276 14.9477 14.6289 14.9934 13.3049C16.5641 12.0877 17.8329 10.5248 18.7012 8.73753C19.5695 6.95028 20.014 4.98685 20.0001 2.99986C19.9991 2.66987 19.933 2.34331 19.8056 2.03892C19.6781 1.73453 19.4919 1.45828 19.2574 1.22602C19.023 0.993769 18.7451 0.810069 18.4395 0.685458C18.1339 0.560848 17.8068 0.497777 17.4768 0.499862ZM3.62178 11.3332H2.51178C2.37086 11.3343 2.23197 11.2996 2.10809 11.2324C1.98421 11.1652 1.8794 11.0677 1.80344 10.949C1.72883 10.8388 1.68322 10.7115 1.67084 10.5789C1.65846 10.4464 1.67971 10.3129 1.73261 10.1907C2.26957 9.20663 3.11315 8.42479 4.13511 7.96403C4.66314 7.69927 5.22062 7.49788 5.79595 7.36403C4.96764 8.62765 4.24065 9.95485 3.62178 11.3332ZM12.5384 16.3649C12.0777 17.3866 11.2962 18.2301 10.3126 18.7674C10.1903 18.8203 10.0566 18.8416 9.92395 18.8292C9.79125 18.8169 9.66382 18.7712 9.55344 18.6965C9.43474 18.6206 9.33724 18.5158 9.27005 18.3919C9.20287 18.268 9.1682 18.1291 9.16928 17.9882V16.8782C10.5476 16.2593 11.8748 15.5323 13.1384 14.704C13.0038 15.2795 12.8016 15.8369 12.5359 16.3649H12.5384ZM12.6509 13.0149C11.5233 13.7981 10.3328 14.4868 9.09178 15.074C8.92231 14.1641 8.48132 13.3268 7.82683 12.6723C7.17235 12.0178 6.33505 11.5768 5.42511 11.4074C6.01277 10.1674 6.70177 8.9781 7.48511 7.85153C10.2918 3.93653 13.2001 2.28903 17.5001 2.16653C17.7171 2.16644 17.9256 2.25103 18.0813 2.4023C18.2369 2.55357 18.3274 2.75958 18.3334 2.97653C18.2109 7.29986 16.5634 10.2082 12.6484 13.0149H12.6509Z"
                    fill={fill}
                />
            </g>
            <defs>
                <clipPath id="clip0_964_647374">
                    <rect height="20" width="20" fill={fill} transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default MVPDevelopmentIcon;
