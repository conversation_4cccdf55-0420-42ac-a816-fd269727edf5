import React from 'react';

const AgileIcon = () => {
    return (
        <svg
            height="50"
            width="50"
            fill="none"
            viewBox="0 0 50 50"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M37.5195 3.65182C37.1973 3.82761 36.9141 4.29636 36.9141 4.63815C36.9141 4.7944 36.875 4.97995 36.8359 5.04831C36.7969 5.11667 36.3086 5.18503 35.5469 5.22409C33.9551 5.31198 33.2031 5.51706 33.2031 5.86862C33.2031 6.17136 33.3105 6.33737 33.6719 6.56198C34.0039 6.76706 34.1406 6.78659 35.4785 6.77682C36.2695 6.77682 36.9141 6.79636 36.9141 6.82565C36.9141 6.85495 36.9629 7.49948 37.0215 8.25143C37.1094 9.40378 37.1582 9.67721 37.3535 10.019C37.6172 10.4682 37.9395 10.6245 38.0762 10.3706C38.2129 10.0971 38.3008 9.49167 38.418 8.00729L38.5254 6.59128L38.9648 6.54245C39.4531 6.48386 39.8926 6.22995 40.0586 5.92721C40.2051 5.65378 39.9805 5.50729 39.2773 5.42917C38.4473 5.33151 38.418 5.31198 38.3984 4.92136C38.3789 4.72604 38.3301 4.4819 38.2813 4.38425C38.2227 4.28659 38.1836 4.13034 38.1836 4.02292C38.1836 3.86667 37.9004 3.50534 37.7832 3.51511C37.7637 3.52487 37.6465 3.5737 37.5195 3.65182Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M12.9889 5.23468C12.1979 5.29327 12.0319 5.35186 11.8658 5.60577C11.7291 5.81085 11.7291 6.05499 11.8268 7.62725C11.9147 9.18975 11.9635 9.49249 12.1686 9.92218C12.442 10.4983 12.7545 10.6644 12.8815 10.3226C13.0084 9.99053 13.1061 9.23858 13.1744 8.03741C13.2135 7.43194 13.2623 6.92413 13.2916 6.89483C13.3112 6.8753 13.819 6.82647 14.4049 6.79718C15.9283 6.71905 16.8951 6.47491 16.8951 6.18194C16.8951 5.91827 16.6901 5.66436 16.3092 5.46905C16.0162 5.32257 15.6842 5.27374 14.7272 5.23468C14.0631 5.21514 13.2819 5.21514 12.9889 5.23468Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.2193 6.33783C22.8522 6.76752 16.9733 9.73627 16.7779 10.1074C16.7096 10.2245 16.651 11.9628 16.6217 14.8925C16.5826 19.4433 16.5826 19.4824 16.7877 19.7363C16.8951 19.8828 18.1842 20.6738 19.649 21.4941C21.1139 22.3144 22.8815 23.3105 23.5846 23.7011C24.2779 24.0917 24.9322 24.414 25.0299 24.414C25.274 24.414 25.4498 24.3163 28.3111 22.6074C29.6881 21.787 31.3287 20.8203 31.9635 20.4589C32.5885 20.0976 33.1549 19.7167 33.2233 19.5995C33.3014 19.4433 33.3111 18.1835 33.2623 14.8632C33.2233 12.3828 33.1744 10.2929 33.1451 10.2245C33.0084 9.86322 25.4303 6.13275 25.2936 6.35736C25.235 6.43549 25.2057 6.43549 25.1568 6.35736C25.0787 6.24018 24.5904 6.23041 24.2193 6.33783ZM25.5768 6.85541C25.899 7.08978 26.5045 7.49018 26.9244 7.74408C29.7174 9.4726 31.1529 10.3808 31.1529 10.4492C31.1529 10.4785 30.8795 10.6445 30.5475 10.8105C29.776 11.1816 25.9186 13.7109 25.6256 14.0331L25.4108 14.2675L24.3072 13.662C21.2115 11.9726 19.5221 11.1132 19.0143 10.957L18.4381 10.7714L19.7955 9.90228C20.5377 9.414 21.5924 8.74018 22.149 8.38861C22.6959 8.04682 23.5553 7.47064 24.0533 7.09955C24.5416 6.73822 24.9615 6.44525 24.9713 6.44525C24.9908 6.44525 25.2643 6.6308 25.5768 6.85541ZM20.6256 13.164C21.3971 13.6425 22.5104 14.3359 23.1061 14.707L24.1901 15.3808L24.2486 17.1288C24.2877 18.0956 24.317 19.3359 24.317 19.8925C24.317 20.4492 24.3658 21.2109 24.4244 21.5917C24.4928 21.9628 24.5123 22.2949 24.483 22.3242C24.4537 22.3535 23.2233 21.6894 21.7584 20.8495C20.2936 19.9999 18.9068 19.2089 18.6822 19.0722L18.2623 18.8378V15.5468C18.2623 13.7402 18.233 12.08 18.194 11.8749L18.1354 11.4941L18.6822 11.8945C18.985 12.1191 19.8541 12.6953 20.6256 13.164ZM31.9342 15.3222V18.8281L30.401 19.6874C29.5514 20.1562 28.1451 20.9472 27.2662 21.4453L25.6744 22.3535L25.7526 21.748C25.7916 21.416 25.8307 19.7656 25.8307 18.0859C25.8307 16.0253 25.86 15.039 25.9283 15.039C26.1627 15.039 29.4342 13.3398 30.6158 12.6074C31.319 12.1777 31.8951 11.8163 31.9147 11.8163C31.9244 11.8163 31.9342 13.3984 31.9342 15.3222Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M37.1197 20.7227C37.0514 21.0645 36.983 21.8164 36.9439 22.4121C36.9146 22.998 36.8463 23.5645 36.7975 23.6523C36.7291 23.7988 36.5631 23.8281 35.6646 23.8281C35.0006 23.8379 34.3951 23.8965 33.9947 24.0039C33.0377 24.2578 32.9596 24.5801 33.7115 25.0684L34.1314 25.3418H36.0064H37.8912L38.1353 25.0879L38.3892 24.834L38.2818 23.0078C38.1842 21.3477 38.1549 21.123 37.9107 20.6445C37.7154 20.2441 37.5982 20.1172 37.442 20.1172C37.2564 20.1172 37.2174 20.1953 37.1197 20.7227Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M11.9729 20.8694C11.8753 21.2014 11.7288 22.3147 11.6702 23.1643L11.6018 24.0237H11.2503C10.6253 24.0237 9.71708 24.7463 10.0784 24.9612C10.2444 25.0686 11.1722 25.2932 11.4456 25.2932C11.6995 25.2932 11.7093 25.3127 11.7483 25.9963C11.8167 26.9436 11.8948 27.3049 12.1097 27.4904C12.2757 27.6369 12.3147 27.6369 12.5882 27.5002C12.9886 27.2854 13.1643 26.9045 13.2425 26.0842L13.3108 25.3908H14.3851C15.4202 25.3908 16.4651 25.2053 16.7776 24.9709C16.9827 24.8147 16.9046 24.4631 16.6018 24.1701C16.2405 23.8186 15.5081 23.7112 14.1409 23.799C13.0374 23.8674 13.1351 23.9846 13.0472 22.4709C12.9983 21.7287 12.9397 21.4651 12.7444 21.1233C12.471 20.674 12.0706 20.5373 11.9729 20.8694Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M20.7215 25.4096C20.37 25.8393 20.2723 28.5541 20.5946 29.1303C20.8387 29.5795 21.2782 29.6088 21.6786 29.2084C22.0204 28.8666 22.0399 28.5346 21.8446 26.8745C21.7372 26.0346 21.6786 25.8393 21.4735 25.644C21.1707 25.351 20.8582 25.2534 20.7215 25.4096Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M28.3607 25.6543C28.2533 25.9082 28.1849 26.4355 28.1556 27.3438C28.1068 28.5645 28.1165 28.7012 28.3118 29.082C28.4974 29.4336 28.5658 29.4922 28.8392 29.4922C29.64 29.4824 29.845 28.8965 29.6302 27.1973C29.4544 25.7715 29.4349 25.7129 29.1712 25.5469C28.6829 25.2344 28.5365 25.2539 28.3607 25.6543Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.6196 26.9922C24.4048 27.2657 24.2681 28.252 24.2778 29.541C24.2876 30.6836 24.3169 30.9766 24.4731 31.2989C24.7173 31.8067 25.1274 31.8848 25.5181 31.4942C25.8794 31.1231 25.9282 30.625 25.7329 28.9453C25.5962 27.7637 25.5278 27.4512 25.3618 27.2364C25.1274 26.9434 24.7564 26.8262 24.6196 26.9922Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M41.2602 30.1074C41.0942 30.1757 40.1079 30.7128 39.063 31.3085C34.2485 34.0624 30.977 35.6054 29.3755 35.8788C29.0239 35.9472 29.0044 35.9277 29.0044 35.6933C29.0044 35.3027 28.6333 34.6093 28.2329 34.248C27.7641 33.8281 27.647 33.789 23.7895 32.6659C20.8891 31.8261 20.5083 31.7382 19.7759 31.7382C18.6919 31.7382 17.9204 31.9824 16.4067 32.8124C14.561 33.8183 14.3071 33.9355 13.0766 34.3163C12.4516 34.5117 11.9048 34.6679 11.8755 34.6679C11.8462 34.6679 11.8169 34.5507 11.8169 34.3945C11.8169 34.0038 11.5044 33.4765 11.1333 33.2617C10.7427 33.0273 10.3716 33.0273 8.71141 33.2519C7.77391 33.3788 7.42235 33.457 7.42235 33.5546C7.42235 33.7304 7.34422 33.7304 7.15868 33.5351C6.97313 33.3593 6.43602 33.3984 6.18211 33.6132C5.645 34.0624 5.46922 35.7421 5.43993 40.9765C5.42039 45.537 5.41063 45.4492 5.97703 45.957C6.46532 46.4062 6.59227 46.4257 8.67235 46.3964C9.7661 46.3867 10.7817 46.3281 10.9184 46.2792C11.6118 46.0156 12.0024 45.3027 12.0122 44.3163V43.6718L12.3345 43.7499C12.5005 43.7988 13.6333 43.9257 14.8345 44.0429C17.6567 44.2968 19.8345 44.6288 22.022 45.1269C23.5161 45.4589 24.0044 45.5273 25.2446 45.5761C26.4946 45.6152 26.8462 45.5956 27.6177 45.4199C30.313 44.8242 33.6723 42.9003 37.9399 39.5019C40.2251 37.6757 43.7993 34.4628 44.1802 33.8769C44.6782 33.1347 44.7368 32.1288 44.3364 31.3476C44.0923 30.8593 43.3696 30.2343 42.8911 30.0976C42.4126 29.9511 41.6509 29.9609 41.2602 30.1074ZM42.3638 31.5527C42.7251 31.6796 43.0669 32.1777 43.0669 32.5781C43.0669 32.9101 42.52 33.457 40.186 35.5077C34.9712 40.0683 30.6352 42.9199 27.6274 43.789C26.2407 44.1894 24.9614 44.1699 22.4419 43.7109C20.5669 43.3691 19.5903 43.2714 16.8462 43.164C15.0102 43.0956 14.102 43.1054 13.1645 43.1933L11.9145 43.3202V39.8925C11.9145 38.0175 11.8852 36.1816 11.8559 35.83L11.7876 35.1952L12.5591 35.0292C14.4048 34.6484 14.9712 34.4726 16.2602 33.8671C17.8911 33.1054 18.7798 32.8124 19.4927 32.8124C19.8833 32.8124 20.8989 33.0859 23.4966 33.8867C25.4106 34.4726 27.0806 35.0195 27.2075 35.1074C27.5981 35.3613 27.7544 35.7812 27.686 36.3769C27.6177 36.8749 27.5981 36.9042 27.2368 37.0507C26.7583 37.246 26.5434 37.246 25.9282 37.0507C25.3618 36.8847 23.9263 36.5917 22.813 36.4257C22.0415 36.3085 21.6509 36.3574 20.8794 36.6406C20.0493 36.9433 20.7915 37.6367 22.2661 37.9296C23.7993 38.2324 25.8501 38.5253 26.4653 38.5253C27.2954 38.5156 28.0571 38.1347 28.2427 37.6269C28.3403 37.3534 28.4087 37.3046 28.6723 37.3046C29.229 37.3046 30.5473 37.0117 31.5239 36.6699C33.2524 36.0742 36.4848 34.4824 40.3813 32.3242C41.1333 31.914 41.8169 31.5429 41.895 31.5136C41.9731 31.4843 42.0415 31.455 42.0513 31.455C42.061 31.4452 42.1977 31.4941 42.3638 31.5527ZM8.8286 33.9452C9.95164 34.0038 10.313 34.0527 10.4692 34.1796C10.6645 34.3359 10.6645 34.3652 10.5669 39.5409C10.5083 42.4023 10.4497 44.7656 10.4302 44.7753C10.3813 44.8242 9.32664 44.8437 8.13524 44.8339L7.03172 44.8242V42.1581C7.02196 40.7031 6.98289 38.9257 6.93407 38.2324C6.88524 37.5292 6.80711 36.3281 6.77782 35.5468C6.71922 34.1796 6.71922 34.121 6.93407 33.9062C7.09032 33.7402 7.17821 33.7109 7.2661 33.7792C7.33446 33.8378 8.03758 33.9159 8.8286 33.9452Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default AgileIcon;
