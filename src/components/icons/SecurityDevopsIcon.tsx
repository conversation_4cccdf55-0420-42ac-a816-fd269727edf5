import React from 'react';

const SecurityDevopsIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M20.4006 5.9712C18.4377 6.2837 16.885 7.33839 16.1721 8.84229C15.5666 10.1118 15.2639 12.0845 15.1662 15.2974L15.1076 17.2505L14.3654 17.7974C13.9553 18.1001 13.4475 18.4614 13.2326 18.5884C12.5002 19.0474 12.549 18.6665 12.5197 24.0181L12.4904 28.8521L10.8596 28.7642C9.96113 28.7153 8.55488 28.6763 7.74434 28.6763C6.48457 28.686 6.2209 28.7056 6.08418 28.8423C5.93769 28.9888 5.92793 29.4282 5.91816 33.188L5.9084 37.3579L5.5373 37.436C5.34199 37.4751 4.96113 37.6216 4.68769 37.7583C3.27168 38.4712 2.4709 39.8774 2.56855 41.4692C2.62715 42.5337 2.91035 43.2368 3.58418 43.9595C4.30684 44.7505 5.03926 45.1021 6.2209 45.2388C6.95332 45.3267 7.25605 45.3169 7.84199 45.1997C9.2873 44.9067 10.4299 44.0864 10.9963 42.9146C11.2502 42.397 11.2795 42.2505 11.2795 41.3228C11.2795 40.395 11.2502 40.2485 10.9963 39.731C10.4006 38.5103 9.02363 37.5435 7.74434 37.436L7.1291 37.3872V34.6333C7.1291 33.1196 7.0998 31.4009 7.06074 30.8247L7.00215 29.77L9.0334 29.8286C10.1467 29.8677 11.3771 29.9263 11.7482 29.9653L12.4318 30.0239L12.3732 32.1821C12.3342 33.3735 12.2756 34.9653 12.2463 35.7271L12.1779 37.104L11.7775 37.2603C11.5529 37.3482 11.2307 37.5044 11.0451 37.6118L10.7229 37.8169L11.2893 38.2661C12.2072 38.979 14.0822 40.1899 15.1662 40.7759C18.5451 42.5728 22.4025 43.6567 27.5881 44.2427C30.342 44.5649 30.0295 44.6235 31.8654 43.3735C32.7346 42.7876 33.6818 42.104 33.965 41.8599L34.4729 41.4009V36.8989C34.4826 32.895 34.59 28.8716 34.6877 28.7642C34.7072 28.7446 36.4748 28.647 38.6135 28.5396C41.2014 28.4224 42.5393 28.3149 42.6076 28.2466C42.6857 28.1685 42.7443 27.2798 42.7736 25.356C42.8029 23.8325 42.8518 22.563 42.8908 22.5239C42.9299 22.4849 43.2814 22.397 43.6721 22.3286C45.9182 21.938 47.4611 20.0728 47.4611 17.7388C47.4611 15.2095 45.8596 13.5005 43.2033 13.1978C41.5627 13.0122 40.2736 13.4028 39.1799 14.4185C37.2561 16.1958 37.2072 19.2427 39.0725 21.0982C39.6779 21.7036 40.6154 22.231 41.2893 22.3384L41.7092 22.4067L41.6408 24.8091C41.6115 26.1274 41.5627 27.2212 41.5529 27.2407C41.5139 27.2798 34.7658 27.5923 34.7365 27.5435C34.717 27.5239 34.7365 27.0454 34.7756 26.479C34.9416 24.0376 34.9807 23.0708 34.9221 22.8364C34.8732 22.6411 34.6682 22.4946 33.965 22.1528C33.467 21.9185 32.7639 21.6157 32.4025 21.4888L31.7385 21.2544V19.3013C31.7287 14.2915 30.635 11.1665 28.008 8.64698C26.2111 6.92823 24.2189 6.04932 21.885 5.93214C21.3674 5.90284 20.7033 5.92237 20.4006 5.9712ZM22.9299 7.20167C24.6193 7.54346 26.1623 8.42237 27.4514 9.7505C29.3947 11.7622 30.3127 14.0864 30.5764 17.7095C30.6838 19.1646 30.5471 23.022 30.3811 23.188C30.1955 23.3735 29.4631 23.4614 28.965 23.354C28.1447 23.188 28.174 23.2759 28.3205 21.6841C28.8186 16.3521 27.9982 13.0024 25.635 10.7075C24.8146 9.90675 23.9455 9.35987 23.0178 9.08643C22.4807 8.92042 22.1682 8.88136 21.3967 8.92042C20.5666 8.94971 20.3615 8.99854 19.8049 9.27198C18.3205 9.99464 17.4807 11.3716 17.0314 13.7837C16.8947 14.4966 16.8557 15.2192 16.8557 16.8794C16.8459 18.2954 16.8166 19.0571 16.7482 19.0571C16.3674 19.0474 16.3576 18.9692 16.3674 15.9614C16.3771 10.3267 16.9143 8.66651 19.0822 7.563C20.0686 7.06495 21.5627 6.91846 22.9299 7.20167ZM23.2717 10.4146C25.2053 11.3716 26.5627 13.354 27.0998 16.0298C27.2561 16.8013 27.4221 19.8384 27.3049 19.8384C27.2658 19.8384 26.6994 19.5942 26.0451 19.2915C24.6389 18.647 21.7678 17.4849 20.2932 16.9673L19.258 16.5962L19.1896 16.1372C19.1115 15.5513 19.2971 13.1489 19.4826 12.3384C19.7268 11.2837 20.1662 10.6099 20.8205 10.2778C21.426 9.96534 22.4807 10.0239 23.2717 10.4146ZM43.7893 14.5942C44.5412 14.936 45.1564 15.5513 45.5373 16.313C45.8107 16.8794 45.8498 17.0454 45.8498 17.7876C45.8498 18.8618 45.5373 19.604 44.7658 20.3267C43.9846 21.0689 43.5061 21.2544 42.383 21.2544C41.5822 21.2544 41.3771 21.2153 40.8986 20.9907C40.1857 20.6587 39.5119 19.9849 39.1799 19.272C38.9455 18.7837 38.9162 18.5982 38.9162 17.7876C38.926 17.0454 38.965 16.7622 39.1311 16.4009C39.5412 15.522 40.4494 14.731 41.4357 14.4087C42.0412 14.2134 43.135 14.3013 43.7893 14.5942ZM21.1232 18.5103C23.008 19.2134 24.6389 19.9067 26.1818 20.6392L27.217 21.1274L27.1779 21.5767C27.1291 22.0259 27.0217 22.6509 26.8947 23.188C26.8166 23.5396 27.0217 23.7739 27.7834 24.1743C28.34 24.4575 28.4279 24.4771 29.4436 24.4771C30.4299 24.4771 30.5666 24.4575 31.0842 24.1939C31.4357 24.0278 31.7287 23.8032 31.8459 23.6079C32.0803 23.2271 32.0803 23.1099 31.8361 22.8657C31.6506 22.6802 31.5725 22.4751 31.6896 22.4751C31.7775 22.4751 33.4963 23.1782 33.4963 23.2075C33.4963 23.2271 32.7639 23.6567 31.8654 24.1646L30.2248 25.0825L28.6623 24.9165C23.633 24.3599 20.5959 23.5982 17.8225 22.2017C16.6506 21.6157 15.3029 20.7271 14.5217 20.0337L13.9553 19.5259L14.424 19.1939L14.883 18.8716L15.0002 19.1353C15.1467 19.4868 15.6154 19.8872 16.0842 20.0532C16.3088 20.1216 16.885 20.1802 17.5295 20.1802C18.8576 20.1802 19.4436 19.9653 19.6779 19.4087C19.8732 18.9497 19.8635 18.8911 19.5803 18.6079C19.3068 18.3345 19.1506 17.8853 19.3361 17.8853C19.4045 17.8853 20.2053 18.1685 21.1232 18.5103ZM15.3225 22.0942C18.3596 24.1548 22.1779 25.3267 28.0178 25.9907C28.9553 26.0884 29.717 26.1958 29.7365 26.2056C29.7463 26.2251 29.717 26.6939 29.6779 27.2603C29.4045 31.0689 29.2775 39.5552 29.4533 42.2798L29.5217 43.2954L28.9943 43.2368C23.7697 42.5825 18.5646 40.5415 14.3166 37.4751L13.3498 36.7817L13.4182 35.0728C13.5744 31.1177 13.6623 26.9185 13.6721 23.9985V20.8833L13.9943 21.147C14.1604 21.2837 14.7658 21.7134 15.3225 22.0942ZM7.69551 38.7251C8.52559 39.1548 9.08223 39.897 9.2873 40.8247C9.58027 42.1919 8.52559 43.7349 7.08027 44.0571C4.88301 44.5552 2.95918 42.1235 3.95527 40.0923C4.24824 39.4868 4.92207 38.8618 5.49824 38.647C6.03535 38.4517 7.25605 38.4907 7.69551 38.7251Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default SecurityDevopsIcon;
