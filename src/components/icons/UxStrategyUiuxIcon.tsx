import React from 'react';

const UxStrategyUiuxIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.6238 3.82715C13.6102 4.2666 10.2801 7.33301 9.56718 11.2588C9.40117 12.1475 9.43046 14.0127 9.62578 15.126C10.1141 18.0459 11.784 21.4346 14.2156 24.4814C15.2312 25.7607 17.9754 28.5244 19.1375 29.4521C20.9734 30.9072 24.3523 32.9287 24.9676 32.9287C25.3289 32.9287 27.2039 31.9326 28.5711 31.0244C34.284 27.2061 38.7176 21.2686 40.0066 15.6924C40.1434 15.0869 40.1824 14.5205 40.1824 13.2021C40.1824 11.7178 40.1531 11.3955 39.9578 10.7314C39.0105 7.4502 36.2371 5.07715 32.8094 4.6084C32.3211 4.54004 31.9695 4.53027 31.9012 4.58887C31.8328 4.6377 31.657 4.72559 31.5008 4.77441C31.2371 4.85254 31.2664 4.87207 31.8914 4.97949C32.2625 5.04785 32.7312 5.15527 32.9461 5.2041L33.3172 5.30176L33.2586 6.49316C33.2293 7.14746 33.1707 9.10059 33.1414 10.8389L33.073 13.9932L32.2332 13.1631C31.3836 12.3428 31.0516 12.1475 30.6609 12.2744C30.5535 12.3135 30.0945 12.6943 29.6551 13.124L28.8543 13.915L28.7957 11.5322C28.7371 9.39355 28.6297 7.59668 28.4539 6.09277L28.3953 5.58496L28.8152 5.39941C29.1863 5.2334 29.6453 5.10645 31.1004 4.74512L31.3934 4.67676L31.0516 4.58887C30.4949 4.46191 29.1961 4.49121 28.5711 4.65723C27.3797 4.96973 26.1687 5.5752 25.407 6.22949L25.0457 6.54199L24.4402 5.96582C23.7859 5.36035 22.8094 4.74512 21.9598 4.41309C20.7098 3.91504 18.952 3.68066 17.6238 3.82715ZM21.2762 5.67285C22.409 6.05371 23.1707 6.50293 24.0203 7.31348C24.8992 8.13379 25.1434 8.14355 25.7977 7.40137C26.1687 6.97168 27.4773 5.97559 27.6531 5.97559C27.6727 5.97559 27.6141 6.40527 27.5164 6.92285C27.2234 8.50488 27.0672 15.5166 27.3113 16.3564C27.3895 16.6299 27.9559 16.835 28.2781 16.7178C28.4246 16.6592 29.0789 16.0537 29.7332 15.3701L30.9246 14.1299L32.2527 15.3799C33.034 16.1025 33.659 16.6201 33.7762 16.6201C33.8836 16.6201 34.0887 16.5127 34.2352 16.3955L34.4988 16.1611L34.3914 12.0205C34.3035 8.80762 34.2352 7.61621 34.0887 6.70801C33.9812 6.06348 33.9129 5.52637 33.9324 5.50684C34.0887 5.35059 36.1883 6.69824 36.8328 7.37207C38.4051 9.0127 39.1473 11.0146 39.0301 13.2998C38.8055 17.4014 36.4812 21.8252 32.4187 25.9072C30.202 28.1338 27.8094 29.9502 25.7879 30.9268L24.9871 31.3174L24.2742 30.9463C22.2332 29.8916 19.8895 28.1338 17.907 26.1514C13.8738 22.1084 11.5594 17.9385 11.1297 13.9346C10.9441 12.2744 11.198 10.8877 11.9207 9.49121C12.6922 7.99707 13.8543 6.86426 15.3777 6.12207C16.7156 5.45801 17.5555 5.28223 19.0887 5.33105C20.1824 5.37012 20.4852 5.40918 21.2762 5.67285Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M4.14062 4.29621C3.96484 4.4134 3.80859 4.77473 3.80859 5.06769C3.80859 5.45832 4.15039 5.78059 5.13672 6.30793C6.05469 6.81574 6.43555 6.84504 6.77734 6.47394C6.92383 6.31769 6.93359 6.24934 6.82617 6.05402C6.25977 4.98957 4.66797 3.94465 4.14062 4.29621Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M44.1406 5.05762C43.3398 5.8584 43.2617 5.96582 43.2617 6.29785C43.2617 6.71777 43.4082 7.0498 43.584 7.0498C43.8672 7.0498 45.1074 6.34668 45.5859 5.90723C46.0059 5.52637 46.1133 5.36035 46.1621 5.04785C46.2207 4.55957 46.0059 4.3252 45.4199 4.24707C45.0293 4.19824 45.0098 4.20801 44.1406 5.05762Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M3.87535 11.7079C3.54332 11.9227 3.4652 12.2059 3.61168 12.6649C3.80699 13.2509 4.18785 13.2899 5.9652 12.9481C6.22887 12.8993 6.54137 12.4696 6.54137 12.1376C6.54137 11.8349 5.95543 11.6298 4.97887 11.5809C4.29527 11.5419 4.09996 11.5614 3.87535 11.7079Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M44.4034 11.7274C43.5245 11.913 43.2804 12.1473 43.3487 12.7723C43.3682 12.9969 43.9932 13.1434 44.921 13.1532C45.6729 13.1532 45.8975 13.1141 46.1222 12.9677C46.4737 12.7333 46.5421 12.4208 46.337 12.0009C46.1026 11.493 45.7413 11.4442 44.4034 11.7274Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M5.56473 18.1728C5.18387 18.29 4.56863 18.6415 4.22684 18.9443C3.63113 19.4619 3.6116 20.0771 4.18777 20.3798C4.74442 20.663 4.98856 20.5556 5.74051 19.7158C6.16043 19.247 6.24832 19.1005 6.24832 18.788C6.23856 18.4267 6.06277 18.0751 5.89676 18.0947C5.84793 18.0947 5.70145 18.1337 5.56473 18.1728Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M43.7971 18.1825C43.4944 18.2997 43.3382 18.5438 43.4065 18.7586C43.5921 19.3543 44.7346 20.3602 45.3401 20.4579C45.8479 20.536 46.1897 20.2235 46.1897 19.6668C46.1897 19.2079 46.0237 19.0418 45.0081 18.4852C44.2366 18.075 44.1292 18.0457 43.7971 18.1825Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M40.7225 31.9423C40.5857 31.9911 39.9022 32.3525 39.2088 32.7529C34.8045 35.3115 31.3572 37.0009 29.6971 37.4306C29.3064 37.5283 28.9451 37.5966 28.9061 37.5673C28.867 37.5478 28.8084 37.3525 28.7693 37.1474C28.74 36.9423 28.5838 36.5908 28.4275 36.3759C27.9197 35.6728 27.9686 35.6923 22.9979 34.2372C20.9568 33.6415 20.6736 33.5829 19.8729 33.5829C18.7303 33.5829 18.1248 33.788 16.3377 34.7939C15.3318 35.3701 14.5994 35.6923 13.6522 36.0048C12.9393 36.249 12.3436 36.4443 12.3338 36.4443C12.324 36.4443 12.2947 36.2587 12.2557 36.0439C12.2068 35.79 12.0701 35.5068 11.8748 35.2919C11.6014 34.999 11.4842 34.9404 11.0154 34.9013C10.7225 34.8818 9.99981 34.9306 9.41387 35.0185C8.01739 35.2333 7.89043 35.2626 7.95879 35.3701C7.98809 35.4286 7.96856 35.4677 7.91973 35.4677C7.86114 35.4677 7.81231 35.5068 7.81231 35.5654C7.81231 35.6923 8.26153 35.7509 9.57012 35.7997C10.1854 35.829 10.7811 35.8779 10.8787 35.9169C11.0545 35.9853 11.0643 36.1318 10.9959 40.9658C10.9568 43.7001 10.8982 45.9951 10.8689 46.0732C10.8299 46.1904 10.5369 46.2099 9.23809 46.1904L7.66582 46.1611L7.59746 42.499C7.56817 40.4872 7.49004 38.3095 7.43145 37.665C7.28496 36.0439 7.29473 36.0048 7.57793 35.6728L7.83184 35.3701L7.56817 35.2529C7.35332 35.1552 7.25567 35.165 7.06036 35.2724C6.63067 35.5165 6.44512 35.8876 6.29864 36.747C6.12286 37.7236 5.91778 43.3388 5.98614 45.2333C6.0252 46.6298 6.11309 46.9423 6.56231 47.3427C7.05059 47.7919 7.17754 47.8115 9.1795 47.7822C11.3279 47.7529 11.699 47.665 12.1189 47.079C12.3143 46.8056 12.3631 46.6005 12.3924 45.9365C12.4217 45.2333 12.4412 45.1357 12.5877 45.1845C12.6756 45.2236 13.7791 45.3505 15.0389 45.4775C17.9783 45.7802 20.1658 46.1122 22.0701 46.5419C24.8338 47.1669 26.074 47.2158 27.8807 46.747C31.2498 45.8681 35.8104 42.8212 41.4939 37.6357C43.6522 35.663 43.8475 35.3993 43.8475 34.3056C43.8475 33.5146 43.6033 32.9775 43.0174 32.4599C42.4022 31.913 41.4939 31.7079 40.7225 31.9423ZM41.9236 33.6122C42.2654 33.8857 42.3924 34.2861 42.2361 34.6474C42.0408 35.1064 37.7537 38.8564 35.3025 40.7021C31.9627 43.2216 29.2088 44.7548 27.1092 45.2724C26.3572 45.4677 24.6189 45.497 23.7303 45.3408C23.4373 45.2822 22.6854 45.1552 22.0701 45.038C19.6385 44.6083 15.6541 44.4228 13.3787 44.6474C12.8709 44.6962 12.451 44.7255 12.4412 44.7158C12.4314 44.706 12.4022 42.9579 12.3631 40.8193L12.2947 36.9521L12.617 36.8837C14.3162 36.5419 15.3904 36.2099 16.4354 35.7118C18.5545 34.7158 19.7361 34.4618 20.6736 34.8232C20.8787 34.9013 22.3631 35.3701 23.9744 35.8681C27.4803 36.9326 27.6268 37.0204 27.49 38.0654L27.4314 38.5927L26.9432 38.7294C26.4939 38.8661 26.4061 38.8661 25.8494 38.6904C24.824 38.3779 22.4607 37.9775 21.9725 38.0458C20.8982 38.1826 20.4686 38.4658 20.7225 38.8759C21.0154 39.3544 21.9334 39.6083 24.9607 40.0576C26.0838 40.2236 26.4256 40.2431 26.8162 40.1552C27.4314 40.0185 27.9588 39.6669 28.115 39.2958C28.2322 39.0224 28.2713 39.0029 28.9354 38.954C29.9119 38.8661 31.0838 38.5146 32.8318 37.7626C34.2967 37.1376 37.8025 35.3408 40.0389 34.0712C40.6541 33.7197 41.2791 33.4267 41.4158 33.4267C41.5623 33.4169 41.7869 33.5048 41.9236 33.6122Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default UxStrategyUiuxIcon;
