import React from 'react';

const webecom = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M40 2.70509C39.8047 2.75392 38.0859 5.32228 38.0859 5.56642C38.0859 5.96681 38.6719 6.26954 38.9844 6.03517C39.3164 5.7715 40.7227 3.50587 40.7227 3.22267C40.7227 2.97853 40.4102 2.60743 40.2344 2.6465C40.2051 2.65626 40.1074 2.67579 40 2.70509Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M18.9456 4.45311C18.7015 4.46288 16.9729 4.53124 15.0882 4.5996C11.719 4.70702 11.4944 4.73632 10.303 5.12694C9.04326 5.5371 8.50615 6.00585 7.95927 7.17772C7.34404 8.49608 7.33427 8.68163 6.93388 20.6055C6.31865 39.375 6.30888 40.4687 6.84599 42.5488C7.24638 44.1308 8.25224 45.6055 9.46318 46.3769C10.3714 46.9629 10.8597 47.0703 12.7249 47.0508C13.6526 47.041 15.3323 47.0898 16.4554 47.1582C21.6604 47.4609 33.9456 47.1777 35.3616 46.7187C36.2894 46.4258 36.9241 45.8594 37.4026 44.9219C37.7054 44.3262 37.764 43.3398 37.8909 36.1816C38.0179 28.8867 38.0179 22.041 37.8909 15.0781C37.764 8.14452 37.7054 7.30468 37.3636 6.60155C37.0315 5.92772 36.2015 5.15624 35.5765 4.94139C34.5511 4.58007 32.3831 4.47264 25.7327 4.44335C22.2464 4.43358 19.1897 4.43358 18.9456 4.45311ZM30.469 5.56639C34.5022 5.76171 35.2737 5.88866 35.8792 6.50389C36.4261 7.0703 36.5628 7.46093 36.6019 8.73046C36.6409 9.79491 36.6312 9.8828 36.4749 9.82421C36.387 9.79491 30.3421 9.76561 23.0374 9.76561H9.76591V8.79882C9.76591 7.29491 10.1175 6.57225 11.055 6.11327C11.5433 5.87889 11.719 5.8496 13.8186 5.75194C19.6487 5.48827 27.0022 5.40038 30.469 5.56639ZM36.7483 14.3066C36.8362 18.7207 36.8362 34.2187 36.7483 36.4062L36.6897 38.0859H23.1936H9.69755L9.63896 36.6016C9.55107 34.6777 9.55107 18.3594 9.63896 14.1797L9.69755 10.9375L12.1487 10.9082C13.4964 10.8887 19.5706 10.8789 25.6448 10.8789L36.6897 10.8887L36.7483 14.3066ZM36.6604 41.3769C36.5921 44.0527 36.5042 44.4531 35.889 45.0683C35.2933 45.6738 34.8831 45.7617 31.7874 45.9473C26.2796 46.2793 18.6526 46.2402 13.2718 45.8496C11.3089 45.7129 10.9573 45.6152 10.4397 45.0488C9.89287 44.4629 9.83427 44.2285 9.75615 42.2851C9.71709 41.2891 9.67802 40.1855 9.67802 39.8144L9.66826 39.1601H23.1936H36.719L36.6604 41.3769Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M20.1761 7.24611C19.9418 7.56838 19.9808 8.06643 20.2347 8.24221C20.5375 8.45705 20.6742 8.43752 20.9476 8.15432C21.2113 7.90041 21.2797 7.35354 21.0746 7.14846C20.8695 6.94338 20.3422 7.00197 20.1761 7.24611Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M23.2422 7.42188C23.1348 7.5293 23.0469 7.70508 23.0469 7.8125C23.0469 7.91992 23.1348 8.0957 23.2422 8.20312C23.418 8.37891 23.5645 8.39844 24.541 8.39844C25.791 8.39844 26.0742 8.29102 26.0742 7.82227C26.0742 7.31445 25.8398 7.22656 24.5801 7.22656C23.5645 7.22656 23.418 7.24609 23.2422 7.42188Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M26.3185 18.3008C24.9708 18.7403 23.7404 19.5117 23.2423 20.2344L23.0079 20.586L22.5587 20.1953C20.9571 18.7598 18.506 18.7207 16.5431 20.1074C14.1896 21.7578 13.5841 24.6875 15.1466 26.7774C15.7521 27.5977 16.6212 28.2813 19.9025 30.4981C23.506 32.9297 23.3986 32.8614 23.672 32.7344C23.9454 32.6074 27.2755 30.3125 28.3693 29.4922C30.9669 27.5391 32.3634 25.918 32.7736 24.375C32.9493 23.7207 32.9493 22.4414 32.7638 21.7481C32.3243 20.0391 30.9279 18.6524 29.2384 18.2227C28.3497 17.9981 27.1486 18.0274 26.3185 18.3008ZM28.6329 19.2481C30.7325 19.6973 32.1193 21.6797 31.7482 23.7207C31.5529 24.7461 31.1427 25.4004 29.9318 26.6114C28.7892 27.7539 27.6954 28.6133 25.0978 30.4004L23.4864 31.5137L20.8009 29.7071C17.4904 27.4707 16.6017 26.7969 16.0743 26.0742C15.5568 25.3809 15.4005 24.8242 15.4493 23.9551C15.5275 22.4317 16.7579 20.9864 18.4669 20.4102C19.2384 20.1465 20.2247 20.1758 20.9181 20.4785C21.5138 20.752 22.2071 21.3965 22.4611 21.9336C22.7052 22.4317 22.9396 22.5879 23.2911 22.5C23.4864 22.4512 23.5646 22.334 23.6818 21.9239C23.965 20.9668 24.629 20.2832 25.8302 19.7168C26.9532 19.1895 27.7247 19.0625 28.6329 19.2481Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M21.6797 40.8789C21.2402 41.25 21.0938 41.6113 21.0938 42.2949C21.0938 43.5937 22.0605 44.3359 23.0078 43.7597C23.8965 43.2226 23.8965 41.3476 23.0078 40.8105C22.5684 40.5468 22.0508 40.5664 21.6797 40.8789Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M41.5527 6.21091C41.0645 6.56248 40.3125 7.08005 39.873 7.35349C39.1211 7.81248 39.0625 7.8613 39.0625 8.16404C39.0625 8.58396 39.4043 8.83787 39.7656 8.70115C40.4492 8.43748 42.998 6.67966 43.1055 6.3867C43.1348 6.2988 43.1641 6.17185 43.1641 6.09373C43.1641 5.89841 42.8027 5.56638 42.5977 5.56638C42.5 5.56638 42.0312 5.85935 41.5527 6.21091Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M39.7661 10.6641C39.5806 10.8496 39.6196 11.4062 39.8345 11.5234C40.0884 11.6504 42.6372 12.207 43.0181 12.207C43.1841 12.207 43.3696 12.1582 43.438 12.0898C43.5942 11.9336 43.5845 11.4746 43.4185 11.2988C43.272 11.1621 40.6353 10.5469 40.1567 10.5469C40.0103 10.5469 39.8345 10.5957 39.7661 10.6641Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default webecom;
