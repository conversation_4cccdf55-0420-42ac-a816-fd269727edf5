import React from 'react';

const softwarecustom = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path
                d="M23.7788 7.11921C23.1831 7.42195 21.601 8.75007 21.4448 9.0821C21.269 9.4532 21.5522 9.78523 22.6362 10.5079C23.935 11.377 24.2085 11.5235 24.8139 11.6114C25.3022 11.6895 25.4878 11.6114 25.4878 11.3282C25.4878 11.1817 24.687 10.3223 24.1206 9.87312L23.8764 9.67781L24.228 9.66804C24.9702 9.66804 26.6499 9.88288 27.7241 10.1173C29.7846 10.5762 31.4741 11.2989 33.1538 12.4317C34.3354 13.2325 36.103 15.0196 36.894 16.211C39.9214 20.7618 40.3608 26.6212 38.0366 31.5919C36.3471 35.2247 33.3882 37.9493 29.4917 39.4825C28.4272 39.9024 26.6303 40.4298 26.2495 40.4298C26.1518 40.4298 26.0737 40.4688 26.0737 40.5177C26.0737 40.8302 29.1303 40.7618 30.2632 40.4298C32.6069 39.7364 34.7553 38.3692 36.855 36.2598C39.101 33.9942 40.3803 31.5821 40.9858 28.4669C41.2007 27.3634 41.2202 24.2384 41.0249 23.1446C40.6538 20.9962 40.0874 19.3165 39.1499 17.4805C38.2319 15.7032 37.3823 14.5313 35.9858 13.1348C33.3686 10.5177 30.4389 9.15046 27.1284 9.01374C25.8393 8.95515 24.3257 9.0821 23.73 9.28718C23.5542 9.35554 23.603 9.27742 23.9253 9.00398C25.0776 8.04695 25.5854 7.35359 25.3901 7.03132C25.351 6.96296 25.0971 6.88484 24.8432 6.86531C24.4526 6.82624 24.2573 6.87507 23.7788 7.11921Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M17.9478 10.4492C14.8911 10.9668 11.9517 13.457 10.1548 17.0508C9.00247 19.375 8.40676 21.6895 8.32864 24.2383C8.27005 26.0059 8.38723 27.1777 8.78762 28.7598C9.34426 30.957 10.2232 32.6465 11.522 34.0039C11.981 34.4727 12.3521 34.8633 12.3423 34.8633C12.3423 34.8633 12.0982 34.7852 11.8052 34.6777C11.19 34.4727 10.0572 34.3262 9.75442 34.4238C9.5884 34.4824 9.55911 34.541 9.60794 34.7852C9.63723 34.9414 9.76419 35.2051 9.88137 35.3711C10.3501 36.0352 13.231 37.002 13.7583 36.6797C13.9536 36.5527 14.022 35.9863 14.0122 34.4727C14.0122 33.0859 13.9927 32.9199 13.7876 32.5488C13.6216 32.2363 13.5142 32.1289 13.3384 32.1289C13.1236 32.1289 13.0845 32.1973 12.9478 32.832C12.8599 33.2227 12.7525 33.7402 12.7134 33.9844L12.645 34.4141L12.1665 33.6914C11.0923 32.0117 10.2915 29.9316 9.94973 27.9199C9.68606 26.3672 9.72512 23.8379 10.0572 22.334C11.0337 17.7539 13.9829 13.5938 18.0161 11.0938C18.5044 10.791 18.9634 10.5078 19.0415 10.459C19.2271 10.3418 18.6216 10.3418 17.9478 10.4492Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M26.6491 14.5215C26.4245 14.7266 26.0827 15.1368 25.8678 15.4395L25.4967 15.9864L24.6862 16.0352C24.237 16.0645 23.7487 16.1426 23.5827 16.211C23.2995 16.3282 23.3288 16.3379 24.2663 16.5137C25.8581 16.7969 25.8092 16.8067 26.6589 15.9766L27.3913 15.2442L28.2604 15.6543L29.1198 16.0547L29.0612 17.1387C29.0319 17.7247 29.0319 18.2618 29.071 18.3106C29.1198 18.3692 29.6276 18.8672 30.2038 19.4239L31.2585 20.4297L32.1471 20.3614C32.6354 20.3223 33.0846 20.2735 33.153 20.254C33.2311 20.2247 33.3874 20.4786 33.5827 20.9375C33.7487 21.3379 33.8854 21.709 33.8854 21.7579C33.8854 21.8067 33.5827 22.0997 33.2018 22.4219C32.694 22.8516 32.4889 23.086 32.4206 23.3692C32.362 23.5645 32.3425 23.7793 32.3717 23.8282C32.4108 23.8868 32.4303 24.4727 32.4303 25.1465L32.4206 26.3575L33.1237 26.9629C33.5143 27.295 33.8757 27.5977 33.9245 27.6465C33.9733 27.6954 33.8952 28.0372 33.7389 28.4668L33.4753 29.209L32.4108 29.1797L31.3464 29.1602L30.3698 30.1856C29.2565 31.3575 29.1686 31.5528 29.2956 32.5684C29.3444 32.9395 29.3835 33.3204 29.3932 33.4082C29.3932 33.5059 29.1393 33.6719 28.6901 33.8672C28.2995 34.043 27.9479 34.1797 27.8991 34.1797C27.86 34.1797 27.5085 33.8477 27.1178 33.4473L26.4147 32.7247L24.9401 32.754C24.1198 32.7735 23.3678 32.8418 23.2604 32.9004C23.153 32.959 22.8405 33.3106 22.5573 33.6915C22.2839 34.0625 22.0007 34.375 21.9421 34.375C21.8835 34.375 21.4831 34.2383 21.0534 34.0821L20.2624 33.7793L20.2917 32.7149L20.321 31.6407L19.2663 30.6055L18.2116 29.5704L17.4303 29.629C17.0007 29.6582 16.5124 29.7071 16.3561 29.7364C16.0534 29.8047 16.0534 29.795 15.6921 28.9649C15.4967 28.5059 15.3307 28.1055 15.3307 28.0567C15.3307 28.0176 15.6628 27.6758 16.0729 27.295L16.8249 26.6114L16.8053 25.2637C16.7956 24.5215 16.7467 23.8086 16.6979 23.6915C16.6491 23.5645 16.2878 23.2032 15.8776 22.8907C15.4772 22.5782 15.1452 22.2461 15.1452 22.168C15.1452 22.0899 15.2917 21.6504 15.4675 21.2012L15.7897 20.3711L16.8835 20.4004L17.9772 20.4297L18.2409 20.1075C18.3874 19.9415 18.7975 19.4825 19.1686 19.0918C19.9108 18.3008 19.9108 18.2715 19.6764 17.0215C19.5983 16.6016 19.53 16.2012 19.53 16.1329C19.53 16.0743 20.0378 15.7618 20.653 15.459L21.776 14.8926L22.4108 15.5274C22.7624 15.8692 23.0456 16.1231 23.0456 16.0743C23.0456 16.0254 23.1139 16.045 23.1921 16.1133C23.28 16.1817 23.3678 16.211 23.3971 16.1817C23.485 16.1036 23.2995 15.8008 22.655 15C22.2448 14.4825 22.0007 14.2579 21.8542 14.2579C21.4147 14.2579 18.9733 15.3028 18.7878 15.5762C18.6901 15.7325 18.6803 15.9961 18.7389 16.7969L18.8268 17.8223L18.1725 18.5157L17.528 19.2188L16.3561 19.17C15.4675 19.1309 15.1452 19.1504 15.0182 19.2481C14.8717 19.3653 14.237 20.8106 13.7975 22.002C13.612 22.5293 13.7975 22.8614 14.6862 23.584L15.4186 24.17V25.1075L15.4284 26.045L14.5885 26.8067C13.8268 27.5 13.7585 27.5977 13.7975 27.8711C13.8757 28.3887 14.9303 30.8008 15.155 30.9961C15.36 31.1622 15.4967 31.1719 16.5026 31.1133L17.6354 31.0547L18.2507 31.6797L18.8757 32.3047L18.7975 33.2422C18.6901 34.7852 18.7292 34.834 20.5065 35.5079C21.0143 35.6934 21.61 35.918 21.8346 36.0059C22.3815 36.2207 22.6354 36.0743 23.4362 35.1075L24.071 34.3262H24.9303H25.78L26.5417 35.1368C27.1374 35.7715 27.3522 35.9375 27.5671 35.9375C27.8796 35.9375 30.5163 34.834 30.78 34.5997C31.0339 34.3653 31.0925 33.8575 30.9655 32.9395L30.8483 32.1094L31.4635 31.4454L32.0788 30.7813L33.0749 30.8301C34.4225 30.9082 34.5788 30.8399 34.8913 30C35.028 29.6387 35.3014 28.9747 35.487 28.5157C35.6823 28.0567 35.8385 27.5977 35.8385 27.5C35.8385 27.1973 35.4772 26.7481 34.7546 26.1719L34.0807 25.6348V24.8829C34.0807 24.4727 34.0612 24.0821 34.0417 24.004C34.0221 23.9356 34.3444 23.584 34.7643 23.2227C35.9069 22.2364 35.9069 22.2071 35.1061 20.4297C34.7448 19.6387 34.3737 18.9063 34.2663 18.8086C34.0905 18.6231 34.0319 18.6231 32.9577 18.7403L31.8249 18.8575L31.4147 18.4864C31.1901 18.2813 30.8581 17.9981 30.6725 17.8711L30.3503 17.627L30.4186 16.5821C30.4577 15.9082 30.4479 15.4786 30.3796 15.3516C30.321 15.2344 30.0475 15.0782 29.7643 14.9805C29.4811 14.8926 28.8854 14.668 28.446 14.4922C27.401 14.0625 27.1569 14.0625 26.6491 14.5215Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M22.8493 19.1797C20.8767 19.7461 19.0993 21.6016 18.6892 23.5254C18.406 24.8731 18.5817 26.3965 19.1677 27.6367C19.6071 28.5547 20.8083 29.7364 21.7751 30.2051C22.8493 30.7324 23.6013 30.8985 24.8024 30.8887C25.6228 30.8887 25.9841 30.8301 26.6091 30.625C28.1228 30.1367 29.2653 29.1406 29.9685 27.6953C30.447 26.6992 30.6032 26.0449 30.5935 25C30.5935 22.9492 29.5974 21.3086 27.8396 20.4297C27.0583 20.0293 26.6872 19.9219 26.8044 20.1074C26.8337 20.1563 26.7849 20.2149 26.6872 20.2344C26.57 20.2539 26.6384 20.3418 26.9411 20.5469C27.5857 20.9961 28.4646 22.002 28.7966 22.6758C29.2556 23.6035 29.4411 24.6582 29.3044 25.5274C29.0603 27.0801 28.1521 28.2031 26.4821 29.0332C25.6716 29.4336 25.6228 29.4434 24.656 29.4434C23.7087 29.4434 23.6403 29.4239 22.9274 29.0723C22.3903 28.8086 22.029 28.5449 21.6384 28.1153C19.7829 26.1133 19.7439 23.2324 21.5505 21.5821C22.6247 20.5957 23.9528 20.1172 25.613 20.1172C26.6872 20.1172 26.6872 20.1172 26.5114 19.9219C26.4138 19.8145 26.0427 19.5801 25.6911 19.4043C25.1345 19.1309 24.9392 19.0918 24.1677 19.0723C23.6696 19.0625 23.0935 19.1114 22.8493 19.1797Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.8203 23.7207C25.498 23.9356 24.9219 24.4434 24.5215 24.8438L23.7988 25.5762L23.3984 25.2344C22.9199 24.8242 22.041 24.5313 21.8262 24.7071C21.748 24.7754 21.6797 24.8731 21.6797 24.9317C21.6797 25.1172 22.3633 26.0645 22.9297 26.6602C23.7891 27.5586 23.8672 27.5391 25.9277 25.752C27.2363 24.6289 27.9004 23.6621 27.5293 23.4278C27.4512 23.3789 27.168 23.3399 26.8945 23.3399C26.4648 23.3399 26.2988 23.3985 25.8203 23.7207Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M17.9965 38.4473C17.7621 38.5352 17.8695 38.7305 18.5629 39.4434C19.266 40.1465 19.266 40.1465 18.934 40.0977C18.0648 39.961 12.0199 39.8242 9.47107 39.8828C5.27185 39.9805 3.42614 40.1953 3.37732 40.5762C3.35779 40.7422 4.12927 41.1035 4.88122 41.2793C5.50622 41.4258 6.0531 41.4551 8.64099 41.4453C11.9515 41.4258 18.3968 41.2403 18.9437 41.1426L19.2855 41.084L18.68 41.7383C17.9965 42.4707 17.8402 42.8321 18.1136 43.0371C18.641 43.4082 19.5785 43.0469 20.9847 41.9043C21.8246 41.2403 22.059 40.8692 21.9125 40.4492C21.8148 40.1758 20.2133 39.0137 19.3832 38.6133C18.934 38.3985 18.2992 38.3203 17.9965 38.4473Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M42.6562 38.4375C42.4902 38.5938 42.5977 38.8086 43.125 39.3555L43.6719 39.9316L41.1426 39.9121C37.8027 39.8828 36.6211 40.0781 36.6211 40.625C36.6211 40.8398 36.875 41.0645 37.3535 41.2695C38.0566 41.5625 40.6348 41.5527 43.4082 41.25C43.8672 41.2012 43.8867 41.2012 43.6914 41.3477C43.3594 41.5918 42.6758 42.5293 42.6758 42.7539C42.6758 43.0176 42.9492 43.1641 43.4277 43.1641C43.877 43.1641 44.5508 42.7832 45.5664 41.9727C46.4844 41.2305 46.6211 41.0645 46.6211 40.6543C46.6211 40.4199 46.5625 40.2539 46.4355 40.166C45.1758 39.2773 44.5996 38.8965 44.209 38.6914C43.7402 38.4473 42.793 38.291 42.6562 38.4375Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default softwarecustom;
