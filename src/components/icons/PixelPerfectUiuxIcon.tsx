import React from 'react';

const PixelPerfectUiuxIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M36.3672 2.70192C32.1891 3.04098 28.4594 3.87223 25.5609 5.09723C24.4562 5.55661 24.2812 5.72067 24.2812 6.24567C24.2812 6.80348 24.3687 7.00036 24.6859 7.12067C25.0141 7.24098 25.5719 7.10973 28.175 6.28942C31.7516 5.16286 34.4969 4.71442 38.1828 4.62692C40.775 4.56129 42.0875 4.63786 44.4609 4.98786C45.7516 5.18473 45.7625 5.18473 47.25 5.58942C48.7812 6.01598 49.6672 6.09254 50.4328 5.86286C50.8813 5.74254 51.0781 5.62223 51.1656 5.41442C51.8109 4.00348 49.6125 3.12848 44.5156 2.73473C43.1812 2.63629 37.45 2.61442 36.3672 2.70192Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M19.0094 6.01562C18.1891 6.26719 17.0735 7.33906 16.3954 8.53125C15.761 9.65781 15.925 9.55937 14.4157 9.63594C12.6219 9.72344 12.0532 9.99687 11.6485 10.9812C11.2219 12.0094 12.0204 13.2016 13.6938 14.0109L14.2079 14.2516L13.9344 14.7109C13.2563 15.8703 12.961 17.4891 13.2782 18.3203C13.5516 19.0312 13.8797 19.25 14.7 19.25C15.3235 19.25 15.4547 19.2063 16.2641 18.7031C16.7563 18.3969 17.5438 17.7844 18.025 17.3359L18.8891 16.5266L20.0266 17.3578C21.4922 18.4187 22.0829 18.7031 22.8704 18.7031C23.8875 18.7031 24.3907 18.1344 24.3907 16.975C24.3907 16.2641 24.2266 15.6844 23.7454 14.7656L23.4282 14.1531L24.1282 13.7922C26.3485 12.6438 26.5016 10.5219 24.4344 9.50469C23.8 9.1875 23.6032 9.14375 22.7938 9.14375L21.886 9.13281L21.4375 8.14844C20.7704 6.65 20.6391 6.43125 20.3438 6.22344C20.0047 5.98281 19.4032 5.88438 19.0094 6.01562ZM19.8188 7.85313C20.2016 9.26406 20.7266 10.6422 20.9782 10.8609C21.2735 11.1234 21.8641 11.3422 22.5313 11.4297C23.3407 11.5281 23.8657 11.6922 23.8219 11.8453C23.8 11.9219 23.3735 12.25 22.8704 12.5781C21.7219 13.3438 21.2188 13.8797 21.2188 14.3281C21.2188 14.7656 21.3938 15.0938 22.0829 15.9578C22.6844 16.7016 22.7938 16.9641 22.4 16.7125C20.2782 15.3234 19.6 14.9953 18.8344 14.9844C18.0688 14.9844 17.6204 15.2578 16.5157 16.3953C15.9469 16.9859 15.3563 17.5328 15.2141 17.6094L14.9516 17.7406L15.0172 17.1391C15.1157 16.3406 15.4766 15.1922 15.75 14.8422C16.3297 14.0984 15.7172 13.1359 14.2844 12.5344C13.6829 12.2828 13.2344 11.9328 13.2344 11.7141C13.2344 11.4625 13.6719 11.2656 14.2188 11.2656C14.886 11.2656 15.761 11.0469 16.3297 10.7406C16.975 10.3906 17.4016 9.82188 18.2 8.23594C18.7688 7.09844 19.1516 6.61719 19.4141 6.70469C19.4688 6.72656 19.6547 7.24063 19.8188 7.85313Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M51.5144 7.76523C46.1441 8.93555 36.6504 13.2559 30.8972 17.1496C27.0144 19.7746 23.8972 22.7277 22.0707 25.4949C20.9769 27.1574 20.1457 29.0168 19.7519 30.734C19.5879 31.4012 19.5988 31.4121 19.8504 31.6746C20.5394 32.3637 21.3269 31.6746 22.4535 29.3887C24.2801 25.7246 27.966 21.623 31.8816 18.8996C35.4254 16.4387 39.4285 14.3059 43.6394 12.6215C45.8379 11.7465 50.1363 10.3027 52.7066 9.55898C54.0957 9.1543 54.1832 9.12148 54.3144 8.78242C54.5332 8.26836 54.5004 8.02773 54.1832 7.77617C53.8113 7.48086 52.8269 7.48086 51.5144 7.76523Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M52.9371 10.3141C51.7558 10.8281 50.9902 11.2875 49.9074 12.1297C48.8683 12.9391 47.3808 14.4375 46.5824 15.4766C44.3402 18.3969 42.798 22.0172 42.4808 25.0687C42.4152 25.7141 42.4371 25.7906 42.6886 26.0859C42.9074 26.3484 43.0496 26.4141 43.4105 26.4141C44.0668 26.4141 44.2418 26.1734 44.6136 24.7187C45.0511 23.0344 45.4011 22.0719 46.1558 20.5078C47.7199 17.3031 50.2793 14.2406 53.4293 11.8125C54.6652 10.85 54.7964 10.6094 54.3589 10.1719C54.0636 9.87656 53.8996 9.89844 52.9371 10.3141Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M36.9141 25.5829C35.9625 26.1298 34.8469 27.4532 33.9063 29.1485C33.7094 29.5095 33.4688 29.8704 33.3813 29.936C33.2719 30.0235 32.8781 30.0782 32.3859 30.0782C31.0297 30.0782 29.75 30.2641 29.2031 30.5485C28.4813 30.9204 28 31.6751 28 32.4516C28 32.7798 28.0547 33.1516 28.1203 33.2829C28.5141 34.0157 29.4547 34.7923 30.7563 35.4485L31.5875 35.8641L31.2047 36.586C30.5156 37.8876 30.1656 39.0032 30.1109 40.086C30.0672 40.9063 30.1 41.136 30.275 41.4423C31.1172 42.9626 33.6547 42.2079 36.1156 39.6923C36.6953 39.1016 37.2094 38.6095 37.2531 38.6095C37.2859 38.6095 37.975 39.0688 38.7625 39.6376C40.5125 40.8845 40.9938 41.1688 41.8141 41.3985C42.7219 41.661 43.3125 41.5516 43.7391 41.0704C44.6688 40.0095 44.3406 38.0079 42.9188 36.0173C42.7438 35.7766 42.7547 35.7657 43.05 35.6563C43.8375 35.3501 44.3844 35.011 44.9641 34.4532C46.7031 32.7579 46.4844 31.0407 44.4172 30.0891C43.5313 29.6845 42.5031 29.4876 41.7156 29.5751L41.1578 29.6407L40.8734 29.1157C40.7203 28.8313 40.3922 28.0876 40.1516 27.4641C39.3969 25.5501 39.1125 25.2657 38.0406 25.2657C37.6031 25.2657 37.3297 25.3423 36.9141 25.5829ZM38.3797 26.3266C38.4453 26.5016 38.5438 26.8735 38.6094 27.1798C38.7953 28.0548 39.2656 29.4001 39.7578 30.472C40.1953 31.4235 40.25 31.4782 40.6984 31.6423C40.9609 31.7407 41.5188 31.8391 41.9344 31.8829C42.7984 31.9595 44.1 32.3641 44.1656 32.572C44.2422 32.8126 43.1375 33.7641 42.1313 34.322C41.6281 34.6063 41.1688 34.9345 41.125 35.0548C41.0813 35.186 40.9609 35.3501 40.8734 35.4266C40.3594 35.8532 40.6109 36.6626 41.6281 37.8438C42.2844 38.6204 42.6563 39.2438 42.6563 39.5938C42.6563 39.7141 42.6344 39.8126 42.6125 39.8126C42.5906 39.8126 41.7594 39.3423 40.775 38.7626C39.8016 38.172 38.6313 37.5266 38.1938 37.3188C37.4281 36.9579 37.3844 36.947 36.8813 37.0782C36.05 37.3079 35.8859 37.4282 34.5625 38.7735C32.7906 40.5673 32.025 41.0376 31.8719 40.4251C31.7188 39.8016 32.5938 37.0345 33.0969 36.5751C33.3375 36.3454 33.3703 36.236 33.3266 35.9079C33.1953 35.1313 32.8344 34.7813 31.3906 34.0157C29.9469 33.261 29.5641 32.911 29.6844 32.4516C29.7719 32.0798 30.3625 31.861 31.6313 31.7188C34.2781 31.4235 34.6609 31.1501 35.875 28.6016C36.5313 27.2345 36.8156 26.7641 37.1656 26.436C37.7453 25.922 38.1938 25.8891 38.3797 26.3266Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M14.4807 30.1219C13.0807 30.5594 11.1995 32.6922 9.67916 35.5688C9.3401 36.2141 9.00103 36.75 8.92447 36.7828C8.6401 36.8922 7.80885 36.9797 6.0151 37.0672C3.4776 37.2094 2.58072 37.5703 1.89166 38.7078C0.743221 40.6328 1.67291 42.0437 5.39166 44.0125C6.03697 44.3625 6.56197 44.6687 6.56197 44.6906C6.56197 44.7234 6.34322 45.1281 6.08072 45.5875C4.42916 48.4859 3.96978 51.5813 5.00885 52.7625C5.34791 53.1344 6.02603 53.375 6.81353 53.375C7.43697 53.375 7.66666 53.3203 8.2901 53.0031C9.71197 52.2922 11.451 50.8375 13.0151 49.0656L13.9229 48.0266L14.4807 48.3875C14.787 48.5844 15.6838 49.2078 16.4604 49.7656C19.0198 51.6141 20.562 52.3906 21.6557 52.3906C23.876 52.3906 24.3026 49.5359 22.5417 46.4516C22.2682 45.9922 21.8198 45.3469 21.5245 45.0188L20.9995 44.4281L21.4807 44.3625C22.5635 44.1984 24.4557 43.0172 25.2651 41.9891C25.8338 41.2563 25.9979 40.9391 26.151 40.25C26.4245 38.9922 25.812 37.9969 24.3026 37.2203C23.0885 36.5969 22.0276 36.3781 20.6057 36.4547L19.4792 36.5094L19.1073 35.7766C18.8995 35.3828 18.451 34.3438 18.112 33.4797C17.062 30.8219 16.7995 30.4172 16.001 30.1219C15.487 29.9469 15.0604 29.9469 14.4807 30.1219ZM15.5417 30.8438C15.8479 31.0078 16.0338 31.4344 16.351 32.7141C16.701 34.0813 17.2698 35.6234 17.8385 36.7719C18.0792 37.2422 18.2651 37.7125 18.2651 37.8219C18.2651 38.3141 18.9432 38.5547 20.8792 38.7188C21.5135 38.7734 22.3229 38.9266 22.6948 39.0469C23.4057 39.2875 24.3901 39.8453 24.3901 40.0203C24.3901 40.2609 23.6245 41.2234 23.0557 41.6719C22.7167 41.9453 21.8417 42.4812 21.1198 42.8641C19.8948 43.5094 19.5667 43.7937 19.7307 44.0563C19.7635 44.1109 19.6651 44.275 19.5229 44.4062C19.0526 44.8547 19.1948 45.4891 19.9932 46.4734C21.262 48.0375 22.0932 49.4266 22.0932 49.9844C22.0932 50.1484 22.0057 50.4 21.8963 50.5312L21.6995 50.7828L20.7698 50.2578C20.2667 49.9734 18.9651 49.2078 17.8823 48.5625C16.7995 47.9172 15.5417 47.2063 15.0932 46.9766C14.3713 46.6156 14.2401 46.5828 13.9448 46.6922C13.7588 46.7578 13.4963 46.8125 13.376 46.8125C13.0151 46.8125 12.3042 47.4031 10.3245 49.3391C9.2526 50.3891 8.2026 51.3078 7.86353 51.5047C6.3651 52.3687 5.88385 51.5594 6.50728 49.1969C6.64947 48.6281 6.9776 47.6328 7.22916 46.9766C7.62291 45.9594 7.73228 45.7844 8.00572 45.675C8.69478 45.4125 8.49791 44.4609 7.56822 43.5422C7.05416 43.0391 6.72603 42.8313 5.71978 42.4047C3.8276 41.5844 3.17135 41.0047 3.17135 40.1188C3.17135 39.5828 3.6526 39.1016 4.40728 38.8828C4.72447 38.7953 5.74166 38.6531 6.68228 38.5547C9.01197 38.325 9.41666 38.1938 10.1823 37.4172C10.6854 36.9031 10.9917 36.4109 11.8995 34.6063C13.1573 32.1125 13.6495 31.4234 14.4588 31.0188C15.0823 30.7016 15.2245 30.6797 15.5417 30.8438Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default PixelPerfectUiuxIcon;
