import React from 'react';

const TestingUiuxIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M25.3418 3.67644C21.1426 4.09636 14.8828 4.18425 7.25586 3.93034C3.53516 3.80339 3.11523 3.81316 2.90039 3.94987L2.66602 4.09636L2.60742 11.0104C2.56836 14.8092 2.53906 21.9284 2.53906 26.821C2.53906 35.1803 2.54883 35.737 2.71484 35.8737C2.93945 36.0788 4.63867 36.4889 6.15234 36.7038C7.88086 36.9479 14.7559 36.9577 18.1152 36.7038C19.4043 36.6159 20.6055 36.5085 20.7812 36.4792L21.0938 36.4303V41.3717C21.0938 44.4479 21.1328 46.3815 21.1914 46.4987C21.4941 47.0651 24.7363 47.5534 28.8574 47.6413C32.5879 47.7194 36.3379 47.5241 43.0078 46.8991C46.123 46.6061 46.377 46.5475 46.7676 46.0495C47.3047 45.3757 47.2754 46.0104 47.3828 29.8092L47.4707 14.8385L47.0898 14.3796C46.8848 14.1354 46.5234 13.8034 46.3086 13.6471L45.8984 13.3542L43.9648 13.5202C39.541 13.9108 35.3809 14.0475 31.5332 13.9303L29.0332 13.862L28.9844 9.3112L28.9453 4.77019L28.457 4.33073C27.5 3.45183 27.5391 3.46159 25.3418 3.67644ZM26.6602 6.5573V8.41277L15.1855 8.4323L3.71094 8.46159V6.70378V4.94597L7.24609 5.06316C14.5215 5.3073 20.9863 5.22917 25 4.84831C25.7227 4.77995 26.3965 4.72136 26.4941 4.71159C26.6504 4.70183 26.6602 4.84831 26.6602 6.5573ZM26.6602 11.655V13.7936L24.1211 13.7253C21.8262 13.6569 21.5625 13.6667 21.4062 13.8229C21.2305 13.9792 21.2207 14.4382 21.1523 24.653C21.1133 30.5124 21.0742 35.3171 21.0645 35.3268C21.0547 35.3366 19.9902 35.4147 18.7012 35.5026C11.8066 35.9811 7.8125 35.9128 4.60938 35.2487C3.67188 35.0632 3.60352 35.0339 3.65234 34.8385C3.68164 34.7214 3.71094 28.9792 3.71094 22.0749V9.51628H15.1855H26.6602V11.655ZM45.2148 16.4889V18.4225H33.7402H22.2656V16.5964V14.7702L23.1738 14.8092C29.8535 15.1706 39.0332 15.1217 43.3594 14.7116C44.082 14.6432 44.7949 14.5846 44.9512 14.5749L45.2148 14.5651V16.4889ZM45.1855 25.1608C45.1465 28.276 45.0879 34.1354 45.0488 38.1784C44.9902 45.5221 44.9902 45.5221 44.7852 45.571C44.5215 45.6296 39.6777 46.069 37.1582 46.2643C34.4434 46.4694 28.0664 46.5573 26.2207 46.4108C24.7266 46.2839 24.1992 46.2155 22.9785 45.9518L22.2656 45.8053V32.651V19.4968H33.7598H45.2441L45.1855 25.1608Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M4.8829 5.97107C4.2872 6.56677 4.65829 7.53357 5.46884 7.53357C6.14267 7.53357 6.55282 6.91833 6.25009 6.35193C6.14267 6.15662 5.47861 5.77576 5.2247 5.77576C5.14657 5.77576 4.99032 5.86365 4.8829 5.97107Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M8.54492 6.18606C8.23242 6.36185 8.11523 6.57669 8.11523 6.94778C8.11523 7.55325 8.59375 7.89505 9.17969 7.72903C9.57031 7.61185 9.75586 7.37747 9.76562 6.97708C9.76562 6.53763 9.67773 6.37161 9.36523 6.21536C9.04297 6.03958 8.80859 6.03958 8.54492 6.18606Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M11.6699 6.31287C11.4941 6.48865 11.4258 6.65466 11.4258 6.94763C11.4258 7.44568 11.6895 7.72888 12.1484 7.72888C12.6172 7.72888 12.7832 7.65076 12.9395 7.32849C13.1543 6.91834 13.1152 6.66443 12.7832 6.35193C12.4023 5.9906 11.9922 5.98084 11.6699 6.31287Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M13.5938 12.3871C13.3496 12.5922 9.57031 19.7308 9.57031 19.975C9.57031 20.3168 9.76562 20.5219 10.0977 20.5219C10.498 20.5219 10.6055 20.4047 11.1719 19.2914C11.416 18.8031 11.6797 18.3344 11.748 18.2465C11.8457 18.1097 12.1875 18.0511 13.5254 17.934C14.4434 17.8558 15.3418 17.7777 15.5273 17.7484L15.8691 17.7191L16.2988 18.7054C16.9043 20.0824 16.9336 20.1312 17.3145 20.1312C17.7051 20.1312 17.8711 19.9847 17.8711 19.6234C17.8711 19.1449 16.25 15.6683 15.0879 13.6273C14.2676 12.2015 14.043 12.016 13.5938 12.3871ZM14.6777 15.3363L15.332 16.5961L15.0195 16.6449C14.8438 16.6742 14.3066 16.7328 13.8184 16.7621C13.3398 16.7914 12.8223 16.8402 12.6758 16.8695C12.4219 16.9183 12.4414 16.8793 13.1641 15.5023C13.5742 14.7211 13.9355 14.0765 13.9746 14.0765C14.0039 14.0765 14.3164 14.6429 14.6777 15.3363Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M5.51774 25.1703C5.28337 25.4144 5.36149 25.9125 5.66423 26.059C5.84977 26.1566 7.06071 26.1859 10.7912 26.1859C16.1916 26.1859 16.1427 26.1859 16.1916 25.6C16.2502 24.9945 16.4064 25.014 10.7814 25.014C6.20133 25.014 5.66423 25.0336 5.51774 25.1703Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M6.01522 29.3698C5.78085 29.6042 5.81991 30.0632 6.09335 30.2389C6.28866 30.3757 6.78671 30.3854 9.86288 30.3659C13.232 30.3366 13.398 30.3268 13.5348 30.151C13.7203 29.8874 13.7105 29.5651 13.4957 29.3796C13.3297 29.2331 12.9293 29.2135 9.73593 29.2135C6.62069 29.2135 6.14218 29.2331 6.01522 29.3698Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M27.3632 16.3713C27.0605 16.7522 27.0898 17.1526 27.4316 17.5041C27.7538 17.8166 27.998 17.8557 28.4081 17.6409C28.7304 17.4846 28.8085 17.3186 28.8085 16.8498C28.8085 16.4592 28.5741 16.2639 27.9687 16.1662C27.6073 16.1174 27.5487 16.137 27.3632 16.3713Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M30.8008 16.2054C30.3907 16.4788 30.4102 17.2601 30.8301 17.5921C31.6016 18.1976 32.334 17.2015 31.6797 16.43C31.4356 16.137 31.0547 16.0394 30.8008 16.2054Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M24.0926 16.4011C23.868 16.6453 23.8875 17.2215 24.1121 17.5047C24.3367 17.7879 24.9032 17.8074 25.1961 17.5437C25.4598 17.3093 25.4696 16.616 25.2157 16.3914C24.9617 16.1668 24.2977 16.1765 24.0926 16.4011Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M31.4176 22.3673C30.9976 22.4064 30.7242 22.4748 30.6558 22.5724C30.5386 22.758 30.6461 34.3595 30.773 34.6037C30.8414 34.7306 31.0269 34.7599 31.8082 34.7697C33.5074 34.7794 34.5133 34.4572 35.3238 33.6466C35.9879 32.9826 36.2711 32.2697 36.2711 31.3127C36.2711 30.297 35.939 29.4962 35.2457 28.881L34.7769 28.4611L35.2554 27.9923C35.9586 27.3087 36.1832 26.7326 36.1832 25.6486C36.1832 24.8869 36.1441 24.7013 35.9195 24.2814C35.1676 22.8654 33.5562 22.172 31.4176 22.3673ZM33.6051 23.7248C34.1812 23.9689 34.7574 24.4865 34.9234 24.8869C35.1578 25.4435 35.0992 26.1662 34.7672 26.7423C34.3961 27.3966 33.8883 27.7384 33.2242 27.797C32.941 27.8263 32.5015 27.8654 32.2281 27.8947L31.7398 27.9337V25.756C31.7398 24.5646 31.7691 23.549 31.8179 23.5099C31.9351 23.3732 33.1363 23.5197 33.6051 23.7248ZM33.5172 29.174C33.7613 29.2521 34.1519 29.4767 34.3765 29.672C35.1773 30.3556 35.3629 31.2931 34.9039 32.2306C34.4742 33.09 33.5172 33.6076 32.3746 33.6076H31.8668L31.8082 32.4162C31.7203 30.883 31.7203 29.1642 31.8082 29.0861C31.9254 28.9591 33.0679 29.0177 33.5172 29.174Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M26.0437 37.9828C25.8386 38.2074 25.8386 38.3832 26.034 38.6664L26.1804 38.8812H31.2976H36.4148L36.5613 38.6664C36.7566 38.3832 36.7566 38.1586 36.5613 37.9633C36.4246 37.8265 35.8191 37.807 31.3074 37.807C26.4832 37.807 26.1902 37.8168 26.0437 37.9828Z"
                fill="var(--color-black-300)"
            />
            <path
                d="M25.7813 41.8597C25.7129 41.9379 25.6641 42.1136 25.6641 42.2504C25.6641 42.7972 25.6055 42.7875 29.3946 42.7875C32.6465 42.7875 32.8907 42.7777 33.0372 42.6117C33.2422 42.3871 33.2422 42.2113 33.0469 41.9281L32.9004 41.7132H29.4044C26.4063 41.7132 25.8887 41.7328 25.7813 41.8597Z"
                fill="var(--color-black-300)"
            />
        </svg>
    );
};

export default TestingUiuxIcon;
