import React from 'react';

const BugTestingIcon = () => {
    return (
        <svg
            height="56"
            width="56"
            fill="none"
            viewBox="0 0 56 56"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M45.1286 3.05135C44.9208 3.28103 44.6801 4.37478 44.5598 5.55603C44.4942 6.23416 44.5161 6.37635 44.6801 6.56228C44.9536 6.8576 45.4458 6.84666 45.6536 6.54041C45.752 6.40916 45.8286 6.0701 45.8395 5.79666C45.8395 5.52322 45.8833 5.05291 45.938 4.7576C46.2005 3.33572 46.2005 3.21541 45.927 3.01853C45.588 2.78885 45.3583 2.79978 45.1286 3.05135Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M31.3359 3.79527C27.2016 4.3312 23.2312 6.45308 20.4203 9.61401C16.7672 13.7265 15.2578 19.3812 16.3625 24.8062C16.7234 26.589 17.6859 29.05 18.5609 30.45C18.8234 30.8656 18.8344 30.8984 18.6484 31.0078C18.0031 31.3796 17.5328 31.85 17.2484 32.375C16.9422 32.9546 16.9312 33.0203 16.9859 34.1031L17.0406 35.2187L15.7937 36.5203L14.5469 37.8218L13.475 37.8656C11.9437 37.9203 11.5828 38.1281 9.67969 39.9984C8.82656 40.8296 7.19688 42.3828 6.0375 43.4546C4.87812 44.5265 3.81719 45.5656 3.675 45.7515C3.31406 46.2328 2.95312 47.3484 2.95312 48.0046C2.95312 48.5843 3.4125 50.1593 3.81719 50.9578C4.16719 51.6468 5.04219 52.4671 5.775 52.8062C6.27813 53.0359 6.55156 53.0906 7.21875 53.0906C8.225 53.0796 8.96875 52.8171 10.0297 52.0734C10.9047 51.4609 13.4859 49.0875 15.7609 46.8234C17.1719 45.4125 17.4125 45.1281 17.7734 44.3515C18.1125 43.6406 18.2 43.3234 18.2437 42.6562C18.2656 42.2078 18.2437 41.65 18.1781 41.4093C18.0469 40.9609 17.9594 41.0921 19.6875 39.2328C19.8625 39.0359 19.8844 39.0468 20.2563 39.2656C20.9125 39.6484 22.1266 39.8453 22.9688 39.7031C23.6906 39.5718 24.7844 39.0687 25.1016 38.7187C25.2766 38.5328 25.3094 38.5328 25.9547 38.8281C26.8844 39.2437 28.5141 39.7578 29.8156 40.0312C31.2047 40.3375 34.4969 40.4468 36.0391 40.25C42.2734 39.4734 47.6 35.6781 50.4766 29.9796C51.7891 27.3656 52.4672 23.7125 52.1937 20.6828C52.0297 18.889 51.8328 17.8828 51.2969 16.264C50.05 12.5125 47.6437 9.44995 44.0781 7.08745C41.1359 5.14058 38.85 4.22183 36.0609 3.83902C35.2625 3.72964 32.0688 3.69683 31.3359 3.79527ZM36.7609 5.23901C40.0859 5.94995 42.7 7.36089 45.1281 9.78901C47.5562 12.2062 49 14.9187 49.6781 18.3203C50.0172 20.0046 50.0172 22.6406 49.6672 24.2812C49.3281 25.9328 48.9672 26.9937 48.2344 28.4921C45.4016 34.3218 39.4625 37.8546 32.9219 37.5812C30.0016 37.4718 27.475 36.6953 25.0469 35.1859C21.1531 32.7468 18.5609 29.039 17.6094 24.5218C17.3141 23.1546 17.2047 20.6281 17.3797 19.3046C17.8609 15.6296 19.4797 12.3593 22.0938 9.73433C24.6859 7.14214 28.1422 5.4687 31.9375 4.98745C32.8453 4.87808 35.7328 5.0312 36.7609 5.23901ZM20.2891 32.8234C20.9891 33.6546 22.3453 34.8796 23.2641 35.525C23.7563 35.875 23.8219 35.9625 23.7672 36.2031C23.5703 37.0343 22.8594 37.4609 21.7766 37.3843C20.8797 37.3187 20.125 36.9578 19.425 36.2468C18.1453 34.9781 17.7953 33.3921 18.6156 32.6265C18.8453 32.4078 19.4469 32.0468 19.5781 32.0468C19.6109 32.0468 19.9281 32.3968 20.2891 32.8234ZM17.8938 38.4125C17.0078 39.3859 16.8219 39.5062 16.6578 39.2218C16.6141 39.1343 16.3953 38.9265 16.1766 38.7515L15.7719 38.4343L16.7562 37.3734L17.7406 36.3125L18.2547 36.8812L18.7797 37.45L17.8938 38.4125ZM14.3391 39.1781C15.3562 39.5171 16.1984 40.414 16.4172 41.3875C16.5266 41.8687 16.5156 42.0656 16.3625 42.6015C16.1984 43.1593 16.0453 43.3671 15.0062 44.4281C13.1359 46.3421 9.00156 50.2359 8.52031 50.5203C7.89687 50.8921 6.86875 50.9578 6.20156 50.6625C5.54531 50.3781 4.725 49.6015 4.47344 49.0546C4.35312 48.8031 4.26562 48.3656 4.26562 48.0046C4.26562 47.0093 4.60469 46.5062 6.50781 44.7781C7.41563 43.9578 9.03437 42.4046 10.1172 41.3437C11.6703 39.8234 12.1953 39.375 12.5781 39.2328C13.1797 39.025 13.8359 39.0031 14.3391 39.1781Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M32.7039 7.88605C26.8961 8.2798 21.9305 12.6767 20.7273 18.4735C20.1695 21.1532 20.5414 24.3032 21.7117 26.7642C23.593 30.7454 27.3336 33.5892 31.6101 34.2892C35.8648 34.9782 40.218 33.6001 43.2039 30.6142C48.3992 25.4189 48.4101 16.8657 43.2367 11.736C40.4258 8.94698 36.7617 7.61261 32.7039 7.88605ZM36.4883 9.39542C40.8961 10.4782 44.243 13.7157 45.457 18.0689C45.8398 19.436 45.9383 21.7985 45.6758 23.2204C45.0961 26.2939 43.4773 28.9407 41.093 30.7454C38.8726 32.4189 35.6461 33.4142 33.0867 33.1954C26.6117 32.6595 21.7664 27.5189 21.7664 21.197C21.7664 15.0173 26.1414 10.0735 32.4305 9.14386C33.1851 9.03448 35.6242 9.18761 36.4883 9.39542Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M36.1809 20.9669L32.1559 24.9919L30.4277 23.2638C28.9512 21.7982 28.6449 21.5466 28.3605 21.5466C27.9668 21.5466 27.759 21.7654 27.748 22.181C27.748 22.4216 28.1199 22.8373 29.8043 24.4669C31.5652 26.1732 31.9152 26.4685 32.1996 26.4685C32.4949 26.4685 33.0418 25.9544 36.8262 22.1701C41.3543 17.6529 41.409 17.5763 40.9277 17.1388C40.4574 16.7123 40.3699 16.7779 36.1809 20.9669Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M49.0766 4.72458C48.4531 5.55583 47.3594 7.33864 47.3594 7.52458C47.3594 7.83083 47.7312 8.11521 48.0922 8.07146C48.3547 8.04958 48.5516 7.81989 49.3609 6.61677C49.8859 5.84021 50.3125 5.09646 50.3125 4.96521C50.3125 4.30896 49.5031 4.16677 49.0766 4.72458Z"
                fill="var(--color-primary-900)"
            />
            <path
                d="M51.5919 8.34473C51.2529 8.53066 50.6732 8.91348 50.3013 9.17598C49.6888 9.63535 49.6451 9.69004 49.6779 10.051C49.6997 10.3354 49.7872 10.4775 50.006 10.5979C50.2794 10.7619 50.3232 10.751 50.7716 10.4119C51.0341 10.215 51.5919 9.8541 52.0076 9.60254C52.4341 9.35098 52.8388 9.07754 52.9154 8.9791C53.2216 8.60723 52.9263 7.98379 52.4341 7.98379C52.3247 7.98379 51.9419 8.14785 51.5919 8.34473Z"
                fill="var(--color-primary-900)"
            />
        </svg>
    );
};

export default BugTestingIcon;
