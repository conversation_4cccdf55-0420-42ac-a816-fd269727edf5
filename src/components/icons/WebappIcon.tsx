import React from 'react';

const WebappIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M40.0001 6.2975C39.8829 6.38539 39.7755 6.74672 39.6388 7.52797C39.5411 8.1432 39.297 9.10023 39.1016 9.6764C38.7306 10.8092 38.7403 11.0924 39.1993 11.2096C39.5704 11.2975 39.8145 11.1022 40.0391 10.4967C40.5274 9.19789 40.9669 7.01039 40.8399 6.53187C40.8106 6.43422 40.6837 6.30726 40.5567 6.25844C40.2442 6.14125 40.2052 6.15101 40.0001 6.2975Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M44.1794 8.20173C44.1209 8.24079 43.4568 8.94392 42.6951 9.77399C41.24 11.356 41.1033 11.6099 41.4646 11.981C41.8064 12.3228 42.0798 12.1373 43.574 10.5162C44.7849 9.21735 45.0193 8.90485 45.0193 8.66071C45.0193 8.4947 44.9705 8.30915 44.9021 8.24079C44.7751 8.11384 44.3455 8.08454 44.1794 8.20173Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.9589 12.3913C31.8847 12.401 25.2441 12.4596 18.203 12.5182C6.91397 12.6061 5.35147 12.6354 4.95108 12.7721C4.28702 12.9967 3.58389 13.6705 3.20304 14.4518L2.88077 15.1061L2.81241 27.9479C2.72452 41.9811 2.72452 41.8053 3.28116 42.9088C4.05264 44.4225 5.21475 44.9108 8.61319 45.1256C11.6991 45.3307 21.162 45.4381 26.4648 45.3405C32.9101 45.2135 37.4804 45.0573 38.6327 44.9401C41.328 44.6569 42.5292 43.3483 42.7733 40.4479C42.8515 39.4811 43.0761 25.0084 43.0956 19.5006C43.0956 17.4596 43.0663 16.7858 42.9296 16.0827C42.5878 14.3541 41.9335 13.5436 40.2636 12.8014L39.3066 12.3717L37.1093 12.362C35.8983 12.362 34.0331 12.3717 32.9589 12.3913ZM39.287 13.6315C39.6972 13.8073 40.1855 14.3248 40.4003 14.8131C40.537 15.1354 40.5761 15.5748 40.6054 17.2741L40.6444 19.3541H22.3144H3.99405L4.02335 17.2741L4.05264 15.1842L4.34561 14.7057C4.57022 14.3444 4.7753 14.1686 5.17569 13.9635L5.7128 13.6998L17.2851 13.612C36.4355 13.4752 38.9257 13.4752 39.287 13.6315ZM40.664 26.5514C40.6933 29.9303 40.7323 34.569 40.7421 36.8834L40.7714 41.0827L40.4882 41.6491C40.2831 42.069 40.0976 42.2936 39.7851 42.4889L39.3651 42.7623L36.4062 42.8209C34.7753 42.86 27.1288 42.8893 19.4042 42.8893C7.16788 42.8893 5.33194 42.8698 5.0878 42.7428C4.68741 42.5377 4.1503 41.8834 4.02335 41.4342C3.93546 41.1608 3.90616 38.3678 3.90616 31.0534C3.90616 25.5455 3.93546 20.8971 3.96475 20.7311L4.03311 20.4284H22.3144H40.5956L40.664 26.5514Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M14.7461 15.4776C14.2773 15.7901 14.043 16.2393 14.043 16.8252C14.043 18.2998 16.0254 18.8369 16.7969 17.5772C17.1387 17.0108 17.0215 16.1612 16.5332 15.7217C16.3867 15.585 15.3809 15.253 15.127 15.253C15.0977 15.253 14.9219 15.3506 14.7461 15.4776Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M10.2731 15.5456C9.84339 15.8093 9.53089 16.5124 9.59925 17.0398C9.73596 18.0651 10.7809 18.6315 11.7379 18.2019C12.0895 18.0554 12.2262 17.9187 12.402 17.5573C12.9 16.5124 12.3239 15.5651 11.0836 15.4089C10.693 15.3601 10.5367 15.3796 10.2731 15.5456Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M6.27938 15.6338C5.63485 16.0342 5.38095 17.0205 5.75204 17.6943C5.96688 18.0947 6.62118 18.4756 7.10946 18.4756C8.59384 18.4756 9.12118 16.3369 7.79306 15.7119C7.21688 15.4482 6.63095 15.4189 6.27938 15.6338Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M21.2499 15.7998C21.0741 15.9756 21.0546 16.2588 21.1913 16.5322C21.2889 16.7178 21.4745 16.7178 28.4178 16.7178C34.7557 16.7178 35.5468 16.6982 35.6835 16.5615C35.8885 16.3564 35.8788 16.0049 35.6639 15.8096C35.4979 15.6533 34.8534 15.6436 28.4374 15.6436C22.1678 15.6436 21.3866 15.6631 21.2499 15.7998Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.6875 25.4092C23.9355 25.6338 23.4375 26.3369 23.4375 27.167C23.4375 27.7236 23.5352 27.958 23.916 28.3682C24.2871 28.7783 24.6289 28.9248 25.1953 28.9248C25.8496 28.9248 26.3184 28.7002 26.6699 28.2119C27.6758 26.8252 26.3281 24.9111 24.6875 25.4092ZM25.6738 26.7178C25.957 27.0205 25.918 27.4307 25.5664 27.6846C25.332 27.8604 25.2734 27.8604 25 27.7529C24.4922 27.5381 24.3848 27.167 24.7168 26.7373C24.9707 26.415 25.3809 26.4053 25.6738 26.7178Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M15.5656 25.594C15.048 25.721 14.3937 26.0823 13.9152 26.5022L13.466 26.9026L13.2023 26.6487C12.1086 25.6135 9.86249 25.3889 8.33905 26.1702C7.39179 26.6487 6.40546 27.8499 6.10273 28.8753C5.87812 29.6565 5.91718 30.9749 6.18085 31.7659C6.75702 33.4553 7.8703 34.6467 10.6437 36.512C11.6887 37.2249 12.7434 37.9378 12.9777 38.1038C13.2805 38.3089 13.4758 38.3772 13.6125 38.3381C13.8078 38.2796 16.3273 36.2483 18.2512 34.6077C19.8039 33.2796 20.3605 32.4202 20.673 30.9163C21.1418 28.5823 19.7453 26.2385 17.5285 25.6624C16.923 25.4964 16.0832 25.4768 15.5656 25.594ZM17.8312 27.0003C19.7355 27.9768 20.1945 30.5354 18.8078 32.4104C18.4172 32.9475 17.3234 33.9046 14.2473 36.4241L13.5051 37.0296L11.5617 35.7405C9.09101 34.0999 8.15351 33.2112 7.50898 31.9124C7.09882 31.0823 7.07929 30.9944 7.07929 30.2034C7.08905 29.4905 7.12812 29.2757 7.36249 28.8069C8.05585 27.3616 9.62812 26.5413 11.0539 26.8733C11.6789 27.0198 11.9914 27.176 12.548 27.6057C13.3391 28.2014 13.8078 28.1428 14.7258 27.3323C15.5949 26.551 16.7277 26.4339 17.8312 27.0003Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M28.877 26.6592C28.6915 26.8643 28.6719 27.0986 28.8086 27.3721C28.9063 27.5479 29.0626 27.5576 33.1348 27.5576C36.2989 27.5576 37.3926 27.5283 37.4805 27.4404C37.6758 27.2451 37.627 26.6982 37.4122 26.5811C37.2852 26.5225 35.7618 26.4834 33.1251 26.4834C29.2872 26.4834 29.0235 26.4932 28.877 26.6592Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.5605 30.8681C23.8867 31.1708 23.4375 31.8837 23.4375 32.6357C23.4375 33.0166 23.8379 33.7197 24.2383 34.0419C24.668 34.374 25.4395 34.4912 26.0156 34.2958C26.4648 34.1494 27.002 33.5244 27.1387 32.9677C27.5098 31.4931 25.957 30.2333 24.5605 30.8681ZM25.9082 32.1669C26.1523 32.499 26.0645 32.8505 25.6738 33.1533C25.3809 33.3681 25.0977 33.3193 24.8047 32.997C24.5508 32.7333 24.5605 32.4306 24.8145 32.1083C25.0977 31.747 25.625 31.7861 25.9082 32.1669Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M29.0039 32.0498C28.8965 32.1572 28.8086 32.333 28.8086 32.4404C28.8086 32.5479 28.8965 32.7236 29.0039 32.8311C29.1895 33.0166 29.3262 33.0264 33.2422 33.0264C35.4688 33.0264 37.3633 32.9971 37.4512 32.9678C37.8125 32.8213 37.8223 32.167 37.4609 31.9326C37.4023 31.8936 35.5176 31.8545 33.2812 31.8545C29.3262 31.8545 29.1895 31.8643 29.0039 32.0498Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.2539 36.366C24.4434 36.5418 23.8379 37.3132 23.8281 38.1824C23.8281 38.739 24.0234 39.1687 24.4434 39.5398C25.4199 40.3894 26.8262 40.0965 27.3535 38.9441C27.9883 37.5476 26.7676 36.0437 25.2539 36.366ZM26.1914 37.6746C26.416 37.8894 26.4551 38.1336 26.3086 38.4754C26.084 39.0125 25.3223 39.0222 25.0488 38.4949C24.6777 37.782 25.5762 37.1277 26.1914 37.6746Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M29.3554 37.6748C29.1699 37.8506 29.1503 38.3096 29.3163 38.4756C29.4042 38.5635 30.498 38.5928 33.662 38.5928C37.7343 38.5928 37.8906 38.583 37.9882 38.4072C38.1347 38.124 38.1054 37.8604 37.9101 37.6846C37.7441 37.5381 37.3046 37.5186 33.6132 37.5186C30.0097 37.5186 29.4921 37.5381 29.3554 37.6748Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M44.8924 12.9768C43.1737 13.5334 42.9881 13.6604 43.1053 14.1389C43.2323 14.6467 43.4862 14.637 45.3514 14.0413C47.0604 13.4944 47.2655 13.3772 47.2655 12.967C47.2655 12.7522 46.9139 12.4202 46.7088 12.4299C46.6112 12.4397 45.8006 12.6838 44.8924 12.9768Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default WebappIcon;
