import React from 'react';

const AgileDevelopment = () => {
    return (
        <svg
            height="57"
            width="56"
            fill="none"
            viewBox="0 0 56 57"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_1961_344595)">
                <path
                    d="M28.0859 7.6519C27.0031 9.18315 26.1063 10.4957 26.0953 10.5832C26.0734 10.6597 26.9703 11.9832 28.0641 13.5144C29.5078 15.5269 30.1203 16.3035 30.2734 16.3035C30.4813 16.3035 30.4922 16.216 30.525 15.0675L30.5578 13.8207L31.0172 13.8972C31.925 14.0503 33.1391 14.4113 34.1125 14.8269C38.7719 16.7628 42.1625 20.8753 43.2563 25.9066C43.6063 27.5144 43.6063 30.3691 43.2453 31.9988C42.5891 35.0503 41.2438 37.5003 39.0344 39.7097C36.8031 41.941 34.4516 43.2097 31.2578 43.9316L30.1641 44.1832L16.0875 44.216L4 44.2597V47.2566V50.2644H16.4156C28.4141 50.2644 31.0063 50.2316 31.9359 50.1003C39.2969 49.0503 45.5641 43.8441 48.2984 36.4832C50.5734 30.3691 49.9063 23.3363 46.5156 17.791C45.6844 16.4238 44.8422 15.341 43.5734 14.0394C40.3797 10.7144 36.3328 8.60346 31.6953 7.83784L30.6016 7.66284L30.5469 6.29565C30.4703 4.28315 30.4813 4.27221 28.0859 7.6519Z"
                    fill="#091542"
                />
                <path
                    d="M22.0136 8.75566C13.2965 9.94785 9.08865 15.6088 7.17459 24.2275C6.13553 28.9525 6.70428 33.9729 8.79334 38.2713C9.47146 39.6494 9.8824 39.826 10.6699 40.6791L11.1949 41.2588L16.3355 41.226L21.4761 41.1932L20.5136 40.7338C13.9402 37.5729 11.484 32.3869 13.1793 25.3541C14.6012 19.426 16.5012 15.8432 22.4949 14.8588C23.1183 14.7604 23.6543 14.6619 23.6761 14.64C23.709 14.6182 23.6324 14.465 23.5012 14.301C23.3808 14.1369 22.9543 13.5135 22.5496 12.9229C21.9043 11.9822 21.8168 11.7854 21.8605 11.4791C21.9152 11.1947 22.8449 9.70723 23.6105 8.72285C23.7418 8.55879 23.4683 8.55879 22.0136 8.75566Z"
                    fill="#091542"
                />
                <path
                    d="M26.9075 20.1699C26.3388 20.3012 26.1857 20.5855 26.0982 21.5809C25.956 23.1559 25.4091 23.4184 24.2607 22.4668C23.5716 21.8871 23.2435 21.7449 22.8278 21.8543C22.4888 21.9418 21.8216 22.5105 21.3185 23.1449C20.706 23.9215 20.7388 24.2277 21.4935 25.1465C22.4232 26.284 22.281 26.8746 21.0341 26.9949C19.3497 27.1699 19.1966 27.3449 19.2075 28.9527C19.2185 30.5715 19.3388 30.7355 20.7388 30.8559C22.2044 30.9762 22.4888 31.523 21.6466 32.584C20.9903 33.4043 20.8591 33.6887 20.9466 34.0496C21.0232 34.4215 21.9528 35.4824 22.4669 35.7996C23.1232 36.2043 23.4622 36.1168 24.5232 35.2309C25.081 34.7715 25.4857 34.7168 25.8028 35.034C26.0216 35.2527 26.0872 35.4934 26.1857 36.5762C26.2294 37.0137 26.3169 37.2434 26.5247 37.4512C26.7872 37.7137 26.8638 37.7246 28.056 37.7246C29.2591 37.7246 29.3247 37.7137 29.5872 37.4402C29.8388 37.1996 29.8935 37.0246 29.9591 36.1934C30.0357 35.384 30.0903 35.1871 30.2982 35.023C30.7138 34.6949 31.0528 34.7824 31.8622 35.4277C32.9341 36.2918 33.2513 36.2371 34.4325 35.0121C35.045 34.3668 35.1107 34.2574 35.1107 33.8309C35.1107 33.459 35.0341 33.273 34.706 32.8793C33.82 31.8074 33.7544 31.6215 34.1591 31.1621C34.356 30.9324 34.531 30.8777 35.1107 30.823C36.6638 30.6809 36.8607 30.473 36.8607 28.9309C36.8607 27.3449 36.6638 27.1043 35.2747 26.984C34.5747 26.9293 34.356 26.8637 34.1591 26.6668C33.7872 26.2949 33.8419 25.9449 34.3888 25.2777C35.0013 24.523 35.1107 24.3152 35.1107 23.8777C35.1107 23.5824 34.9685 23.3855 34.2575 22.6637C33.47 21.8762 33.3607 21.8105 32.956 21.8105C32.5732 21.8105 32.3982 21.898 31.7747 22.4121C30.9544 23.0793 30.6482 23.1559 30.2544 22.7621C30.0466 22.5543 29.981 22.3246 29.9153 21.6902C29.8169 20.6402 29.6419 20.323 29.1169 20.1809C28.6466 20.0387 27.4653 20.0387 26.9075 20.1699ZM29.5325 25.3762C29.8607 25.5293 30.2872 25.7699 30.495 25.923C31.0419 26.3496 31.5888 27.2465 31.7857 28.0777C32.3107 30.1996 30.8778 32.3215 28.6247 32.7371C27.4216 32.9559 26.1091 32.4965 25.2232 31.5449C24.4247 30.7027 24.2278 30.1668 24.2278 28.9199C24.2278 27.673 24.4247 27.1371 25.2232 26.2949C26.3607 25.0699 28.0341 24.7199 29.5325 25.3762Z"
                    fill="url(#paint0_linear_1961_344595)"
                />
                <path
                    d="M51.9297 42.6293C51.8969 42.7059 51.8641 43.3074 51.8531 43.9746L51.8203 45.1777L49.1516 45.2105L46.4719 45.2324L45.3781 46.3043C44.1531 47.4855 43.4203 48.0762 42.0203 48.984C41.0141 49.6512 39.2094 50.559 38.1484 50.9527L37.4922 51.1934L44.6562 51.248C48.5938 51.2809 51.8313 51.3137 51.8531 51.3355C51.8641 51.3465 51.875 51.948 51.875 52.6699C51.875 53.884 51.8859 53.9824 52.0828 53.9824C52.2359 53.9824 52.8703 53.173 54.2703 51.1934C55.3641 49.6621 56.25 48.3387 56.25 48.2512C56.25 48.0215 52.3344 42.5637 52.1484 42.5199C52.0609 42.498 51.9625 42.5418 51.9297 42.6293Z"
                    fill="#091542"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_1961_344595"
                    gradientUnits="userSpaceOnUse"
                    x1="28.0338"
                    x2="28.0338"
                    y1="20.0728"
                    y2="37.7246"
                >
                    <stop stopColor="white" />
                    <stop offset="1" stopColor="#999999" />
                </linearGradient>
                <clipPath id="clip0_1961_344595">
                    <rect height="56" width="56" fill="white" transform="translate(0 0.919922)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default AgileDevelopment;
