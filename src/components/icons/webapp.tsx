import React from 'react';

const webapp = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="51"
            viewBox="0 0 50 51"
            fill="none"
        >
            <path
                d="M40.0001 6.29786C39.8829 6.38576 39.7755 6.74708 39.6388 7.52833C39.5411 8.14357 39.297 9.1006 39.1016 9.67677C38.7306 10.8096 38.7403 11.0928 39.1993 11.21C39.5704 11.2979 39.8145 11.1026 40.0391 10.4971C40.5274 9.19826 40.9669 7.01076 40.8399 6.53224C40.8106 6.43458 40.6837 6.30763 40.5567 6.2588C40.2442 6.14161 40.2052 6.15138 40.0001 6.29786Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M44.1794 8.20209C44.1209 8.24116 43.4568 8.94428 42.6951 9.77436C41.24 11.3564 41.1033 11.6103 41.4646 11.9814C41.8064 12.3232 42.0798 12.1376 43.574 10.5165C44.7849 9.21772 45.0193 8.90522 45.0193 8.66108C45.0193 8.49506 44.9705 8.30952 44.9021 8.24116C44.7751 8.1142 44.3455 8.08491 44.1794 8.20209Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M32.9589 12.3916C31.8847 12.4014 25.2441 12.46 18.203 12.5186C6.91397 12.6065 5.35147 12.6358 4.95108 12.7725C4.28702 12.9971 3.58389 13.6709 3.20304 14.4522L2.88077 15.1065L2.81241 27.9483C2.72452 41.9815 2.72452 41.8057 3.28116 42.9092C4.05264 44.4229 5.21475 44.9111 8.61319 45.126C11.6991 45.3311 21.162 45.4385 26.4648 45.3408C32.9101 45.2139 37.4804 45.0576 38.6327 44.9404C41.328 44.6572 42.5292 43.3486 42.7733 40.4483C42.8515 39.4815 43.0761 25.0088 43.0956 19.501C43.0956 17.46 43.0663 16.7861 42.9296 16.083C42.5878 14.3545 41.9335 13.544 40.2636 12.8018L39.3066 12.3721L37.1093 12.3623C35.8983 12.3623 34.0331 12.3721 32.9589 12.3916ZM39.287 13.6319C39.6972 13.8076 40.1855 14.3252 40.4003 14.8135C40.537 15.1358 40.5761 15.5752 40.6054 17.2744L40.6444 19.3545H22.3144H3.99405L4.02335 17.2744L4.05264 15.1846L4.34561 14.7061C4.57022 14.3447 4.7753 14.169 5.17569 13.9639L5.7128 13.7002L17.2851 13.6123C36.4355 13.4756 38.9257 13.4756 39.287 13.6319ZM40.664 26.5518C40.6933 29.9307 40.7323 34.5694 40.7421 36.8838L40.7714 41.083L40.4882 41.6494C40.2831 42.0694 40.0976 42.294 39.7851 42.4893L39.3651 42.7627L36.4062 42.8213C34.7753 42.8604 27.1288 42.8897 19.4042 42.8897C7.16788 42.8897 5.33194 42.8701 5.0878 42.7432C4.68741 42.5381 4.1503 41.8838 4.02335 41.4346C3.93546 41.1611 3.90616 38.3682 3.90616 31.0537C3.90616 25.5459 3.93546 20.8975 3.96475 20.7315L4.03311 20.4287H22.3144H40.5956L40.664 26.5518Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M14.7461 15.4776C14.2773 15.7901 14.043 16.2393 14.043 16.8252C14.043 18.2998 16.0254 18.8369 16.7969 17.5772C17.1387 17.0108 17.0215 16.1612 16.5332 15.7217C16.3867 15.585 15.3809 15.253 15.127 15.253C15.0977 15.253 14.9219 15.3506 14.7461 15.4776Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M10.2731 15.546C9.84339 15.8097 9.53089 16.5128 9.59925 17.0401C9.73596 18.0655 10.7809 18.6319 11.7379 18.2022C12.0895 18.0557 12.2262 17.919 12.402 17.5577C12.9 16.5128 12.3239 15.5655 11.0836 15.4093C10.693 15.3604 10.5367 15.38 10.2731 15.546Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M6.27938 15.6338C5.63485 16.0342 5.38095 17.0205 5.75204 17.6943C5.96688 18.0947 6.62118 18.4756 7.10946 18.4756C8.59384 18.4756 9.12118 16.3369 7.79306 15.7119C7.21688 15.4482 6.63095 15.4189 6.27938 15.6338Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M21.2499 15.7998C21.0741 15.9756 21.0546 16.2588 21.1913 16.5322C21.2889 16.7178 21.4745 16.7178 28.4178 16.7178C34.7557 16.7178 35.5468 16.6982 35.6835 16.5615C35.8885 16.3564 35.8788 16.0049 35.6639 15.8096C35.4979 15.6533 34.8534 15.6436 28.4374 15.6436C22.1678 15.6436 21.3866 15.6631 21.2499 15.7998Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.6875 25.4092C23.9355 25.6338 23.4375 26.3369 23.4375 27.167C23.4375 27.7236 23.5352 27.958 23.916 28.3682C24.2871 28.7783 24.6289 28.9248 25.1953 28.9248C25.8496 28.9248 26.3184 28.7002 26.6699 28.2119C27.6758 26.8252 26.3281 24.9111 24.6875 25.4092ZM25.6738 26.7178C25.957 27.0205 25.918 27.4307 25.5664 27.6846C25.332 27.8604 25.2734 27.8604 25 27.7529C24.4922 27.5381 24.3848 27.167 24.7168 26.7373C24.9707 26.415 25.3809 26.4053 25.6738 26.7178Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M15.5656 25.5947C15.048 25.7217 14.3937 26.083 13.9152 26.5029L13.466 26.9033L13.2023 26.6494C12.1086 25.6143 9.86249 25.3897 8.33905 26.1709C7.39179 26.6494 6.40546 27.8506 6.10273 28.876C5.87812 29.6572 5.91718 30.9756 6.18085 31.7666C6.75702 33.4561 7.8703 34.6475 10.6437 36.5127C11.6887 37.2256 12.7434 37.9385 12.9777 38.1045C13.2805 38.3096 13.4758 38.3779 13.6125 38.3389C13.8078 38.2803 16.3273 36.249 18.2512 34.6084C19.8039 33.2803 20.3605 32.4209 20.673 30.917C21.1418 28.583 19.7453 26.2393 17.5285 25.6631C16.923 25.4971 16.0832 25.4776 15.5656 25.5947ZM17.8312 27.001C19.7355 27.9776 20.1945 30.5361 18.8078 32.4111C18.4172 32.9483 17.3234 33.9053 14.2473 36.4248L13.5051 37.0303L11.5617 35.7412C9.09101 34.1006 8.15351 33.2119 7.50898 31.9131C7.09882 31.083 7.07929 30.9951 7.07929 30.2041C7.08905 29.4912 7.12812 29.2764 7.36249 28.8076C8.05585 27.3623 9.62812 26.542 11.0539 26.874C11.6789 27.0205 11.9914 27.1768 12.548 27.6065C13.3391 28.2022 13.8078 28.1436 14.7258 27.333C15.5949 26.5518 16.7277 26.4346 17.8312 27.001Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M28.877 26.6592C28.6915 26.8643 28.6719 27.0986 28.8086 27.3721C28.9063 27.5479 29.0626 27.5576 33.1348 27.5576C36.2989 27.5576 37.3926 27.5283 37.4805 27.4404C37.6758 27.2451 37.627 26.6982 37.4122 26.5811C37.2852 26.5225 35.7618 26.4834 33.1251 26.4834C29.2872 26.4834 29.0235 26.4932 28.877 26.6592Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.5605 30.8681C23.8867 31.1708 23.4375 31.8837 23.4375 32.6357C23.4375 33.0166 23.8379 33.7197 24.2383 34.0419C24.668 34.374 25.4395 34.4912 26.0156 34.2958C26.4648 34.1494 27.002 33.5244 27.1387 32.9677C27.5098 31.4931 25.957 30.2333 24.5605 30.8681ZM25.9082 32.1669C26.1523 32.499 26.0645 32.8505 25.6738 33.1533C25.3809 33.3681 25.0977 33.3193 24.8047 32.997C24.5508 32.7333 24.5605 32.4306 24.8145 32.1083C25.0977 31.747 25.625 31.7861 25.9082 32.1669Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M29.0039 32.0498C28.8965 32.1572 28.8086 32.333 28.8086 32.4404C28.8086 32.5479 28.8965 32.7236 29.0039 32.8311C29.1895 33.0166 29.3262 33.0264 33.2422 33.0264C35.4688 33.0264 37.3633 32.9971 37.4512 32.9678C37.8125 32.8213 37.8223 32.167 37.4609 31.9326C37.4023 31.8936 35.5176 31.8545 33.2812 31.8545C29.3262 31.8545 29.1895 31.8643 29.0039 32.0498Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.2539 36.3662C24.4434 36.542 23.8379 37.3135 23.8281 38.1826C23.8281 38.7393 24.0234 39.169 24.4434 39.5401C25.4199 40.3897 26.8262 40.0967 27.3535 38.9444C27.9883 37.5479 26.7676 36.044 25.2539 36.3662ZM26.1914 37.6748C26.416 37.8897 26.4551 38.1338 26.3086 38.4756C26.084 39.0127 25.3223 39.0225 25.0488 38.4951C24.6777 37.7822 25.5762 37.1279 26.1914 37.6748Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M29.3554 37.6748C29.1699 37.8506 29.1503 38.3096 29.3163 38.4756C29.4042 38.5635 30.498 38.5928 33.662 38.5928C37.7343 38.5928 37.8906 38.583 37.9882 38.4072C38.1347 38.124 38.1054 37.8604 37.9101 37.6846C37.7441 37.5381 37.3046 37.5186 33.6132 37.5186C30.0097 37.5186 29.4921 37.5381 29.3554 37.6748Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M44.8924 12.9775C43.1737 13.5342 42.9881 13.6611 43.1053 14.1396C43.2323 14.6475 43.4862 14.6377 45.3514 14.042C47.0604 13.4951 47.2655 13.3779 47.2655 12.9678C47.2655 12.7529 46.9139 12.4209 46.7088 12.4307C46.6112 12.4404 45.8006 12.6846 44.8924 12.9775Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default webapp;
