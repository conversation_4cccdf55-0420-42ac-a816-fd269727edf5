import React from 'react';

const d3 = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="46"
            height="44"
            viewBox="0 0 46 44"
            fill="none"
        >
            <g clipPath="url(#clip0_1749_337784)">
                <mask
                    id="mask0_1749_337784"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="46"
                    height="44"
                >
                    <path d="M45.5 0H0.5V44H45.5V0Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1749_337784)">
                    <path
                        d="M45.415 31.7995C45.4352 31.5976 45.4496 31.3939 45.4608 31.1891C45.4746 30.9466 31.2998 17.0928 31.2998 17.0928H30.9609C30.9609 17.0928 45.3283 32.7039 45.415 31.7995Z"
                        fill="url(#paint0_linear_1749_337784)"
                    />
                    <path
                        d="M15.1729 26.9936C15.1541 27.0361 15.1355 27.0787 15.1162 27.121C15.0961 27.1656 15.0754 27.2099 15.0548 27.2539C14.6031 28.2245 21.3805 35.0495 21.9256 34.2204C21.9504 34.1838 21.9754 34.1451 22.0001 34.1085C22.0277 34.0651 22.055 34.0234 22.0822 33.98C22.5182 33.2982 15.3665 26.5503 15.1729 26.9936Z"
                        fill="url(#paint1_linear_1749_337784)"
                    />
                    <path
                        d="M24.751 36.5908C24.7318 36.6343 24.5954 36.8718 24.4708 36.9973C24.4497 37.0408 31.0871 43.8215 31.0871 43.8215H31.6847C31.6847 43.8215 25.4541 36.8775 24.751 36.5908Z"
                        fill="url(#paint2_linear_1749_337784)"
                    />
                    <path
                        d="M45.4701 31.0658C45.1597 38.1542 39.4669 43.8218 32.5118 43.8218H31.5714L24.6257 36.7731C25.197 35.9431 25.7282 35.0832 26.201 34.1866H32.5118C34.5076 34.1866 36.1317 32.5144 36.1317 30.4574C36.1317 28.4014 34.5076 26.7283 32.5118 26.7283H28.7772C29.0618 25.1789 29.2173 23.5811 29.2173 21.9466C29.2173 20.2871 29.059 18.6667 28.7642 17.0925H31.0839L45.4292 31.653C45.4462 31.4578 45.4597 31.2628 45.4701 31.0658ZM4.27342 0H0.5V9.63528H4.27342C10.8639 9.63528 16.2263 15.1577 16.2263 21.9466C16.2263 23.7935 15.8264 25.5468 15.1157 27.1208L21.9997 34.1083C24.2601 30.6245 25.5814 26.442 25.5814 21.9466C25.5814 9.84532 16.0224 0 4.27342 0Z"
                        fill="url(#paint3_linear_1749_337784)"
                    />
                    <path
                        d="M32.5124 0H17.2324C20.9645 2.34721 24.0549 5.67546 26.1605 9.63528H32.5124C34.5082 9.63528 36.1324 11.3075 36.1324 13.3644C36.1324 15.4208 34.5082 17.093 32.5124 17.093H31.0851L45.4304 31.6534C45.4641 31.2585 45.4852 30.861 45.4852 30.4574C45.4852 27.2092 44.3537 24.2295 42.475 21.9104C44.3537 19.5923 45.4852 16.6121 45.4852 13.3644C45.4852 5.99559 39.6663 0 32.5124 0Z"
                        fill="url(#paint4_linear_1749_337784)"
                    />
                    <path
                        d="M31.5714 43.8221H17.3409C20.2054 41.9998 22.6818 39.5941 24.6257 36.7734L31.5714 43.8221ZM21.9997 34.1086L15.1163 27.1211C13.2145 31.3312 9.0704 34.2592 4.27342 34.2592H0.5V43.8936H4.27342C11.6578 43.8936 18.1757 40.0028 21.9997 34.1086Z"
                        fill="url(#paint5_linear_1749_337784)"
                    />
                </g>
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_1749_337784"
                    x1="-1167.27"
                    y1="-1351.48"
                    x2="1583.72"
                    y2="1534.72"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#F9A03C" />
                    <stop offset="1" stop-color="#F7974E" />
                </linearGradient>
                <linearGradient
                    id="paint1_linear_1749_337784"
                    x1="-1815.08"
                    y1="-1794.85"
                    x2="790.082"
                    y2="659.417"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#F9A03C" />
                    <stop offset="1" stop-color="#F7974E" />
                </linearGradient>
                <linearGradient
                    id="paint2_linear_1749_337784"
                    x1="-1585.42"
                    y1="-1857.61"
                    x2="708.796"
                    y2="766.629"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#F9A03C" />
                    <stop offset="1" stop-color="#F7974E" />
                </linearGradient>
                <linearGradient
                    id="paint3_linear_1749_337784"
                    x1="510.416"
                    y1="-79.8434"
                    x2="3605.93"
                    y2="4113.38"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#F26D58" />
                    <stop offset="1" stop-color="#F9A03C" />
                </linearGradient>
                <linearGradient
                    id="paint4_linear_1749_337784"
                    x1="464.87"
                    y1="122.119"
                    x2="3603.57"
                    y2="1962.94"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#B84E51" />
                    <stop offset="1" stop-color="#F68E48" />
                </linearGradient>
                <linearGradient
                    id="paint5_linear_1749_337784"
                    x1="1460.36"
                    y1="420.705"
                    x2="1504.74"
                    y2="2501.54"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#F9A03C" />
                    <stop offset="1" stop-color="#F7974E" />
                </linearGradient>
                <clipPath id="clip0_1749_337784">
                    <rect width="45" height="44" fill="white" transform="translate(0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default d3;
