import React from 'react';

const BackendIcon = () => {
    return (
        <svg
            height="51"
            width="50"
            fill="none"
            viewBox="0 0 50 51"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M22.6562 5.43396C18.6913 6.01013 15.5468 7.34802 12.5976 9.70154C11.3769 10.6781 9.90225 12.2211 9.03311 13.4418C7.99795 14.8871 6.91397 17.2894 6.54288 18.9886C6.43545 19.4476 6.3671 19.5648 6.17178 19.6136C6.0253 19.6429 5.90811 19.6234 5.85928 19.5453C5.82022 19.4769 5.56631 19.3988 5.31241 19.3793C4.72647 19.3207 4.26749 19.5941 3.93545 20.1801C3.45694 21.0297 3.56436 21.9769 4.21866 22.7094C4.62881 23.1879 5.26358 23.4515 5.94717 23.4515C6.45499 23.4515 7.23624 23.0511 7.59756 22.6117C8.33975 21.7133 8.13467 20.1703 7.20694 19.7113L6.85538 19.5355L7.09952 18.9789C8.12491 16.6547 8.89639 15.4047 10.3222 13.7152C13.8183 9.55505 18.203 7.2992 23.6815 6.84021C29.5409 6.35193 35.2148 8.64685 39.2773 13.1488C41.0155 15.0824 42.3827 17.182 43.0663 18.9789C43.1737 19.2621 43.2616 19.5062 43.2616 19.5258C43.2616 19.5355 43.1444 19.4672 42.998 19.35C42.6073 19.0668 41.8065 18.9398 41.621 19.1254C41.4159 19.3304 41.5429 19.5453 42.539 20.639C43.6327 21.8207 43.8866 22.0258 44.3163 22.0258C44.8144 22.0258 44.9608 21.7719 45.205 20.4633C45.4394 19.2426 45.4198 18.1879 45.1659 17.9828C44.9706 17.8265 44.3651 18.1097 44.1698 18.4515L44.0136 18.7347L43.6327 17.6117C42.3339 13.725 39.0038 9.86755 35.1464 7.77771C31.9335 6.03943 28.8964 5.27771 25.1952 5.29724C24.2577 5.30701 23.1151 5.3656 22.6562 5.43396ZM6.60147 20.639C6.89444 21.1957 6.72842 21.6547 6.15225 21.8695C5.83975 21.9867 5.4003 21.7719 5.27335 21.4594C5.14639 21.1078 5.32217 20.5023 5.62491 20.2582C5.88858 20.0531 5.90811 20.0531 6.18155 20.2094C6.3378 20.2972 6.52335 20.4926 6.60147 20.639Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.4138 8.54919C23.8767 8.7738 22.7341 9.34021 15.6443 12.9047C12.5388 14.4574 11.8845 14.8187 11.8064 15.0336C11.7478 15.1996 11.7185 17.8461 11.7478 22.4261C11.7771 29.2621 11.7869 29.5844 11.9822 30.3851C12.5877 32.8363 13.6424 34.7601 15.283 36.391C16.1814 37.2797 17.7146 38.4222 22.5291 41.7719C24.1306 42.8851 24.9119 43.3734 25.0974 43.3734C25.3806 43.3734 25.4685 43.3148 27.3924 41.9574C28.0955 41.4691 29.8533 40.2289 31.2986 39.2133C34.2283 37.1625 35.4783 36.0492 36.4256 34.6429C37.3924 33.1976 38.0955 31.4203 38.408 29.6625C38.5642 28.7543 38.574 15.1898 38.4177 14.9261C38.3006 14.7211 28.9451 10.0531 26.8064 9.13513C26.0056 8.78357 25.2634 8.4613 25.1756 8.40271C24.9705 8.29529 25.0584 8.27576 24.4138 8.54919ZM25.781 9.05701C26.9334 9.93591 32.2361 13.1195 35.283 14.7601L37.0213 15.6879L36.9822 22.3285C36.9431 28.764 36.9334 28.9984 36.7283 29.9457C36.2791 31.9769 35.2927 33.7738 33.8377 35.2191C33.408 35.6586 32.2752 36.5765 31.2986 37.2797C27.5486 39.9847 25.2146 41.6156 25.0974 41.6156C25.0388 41.6156 24.4236 41.225 23.7498 40.7562C23.0662 40.2777 21.5037 39.1937 20.2634 38.3441C17.2654 36.2836 17.0506 36.1273 16.2498 35.3265C14.6873 33.7543 13.7888 32.0844 13.2517 29.7504C13.0662 28.9203 13.0564 28.4906 13.0174 22.2308L12.9783 15.5902L14.7654 14.6039C16.6209 13.5785 24.2674 9.1449 24.492 8.96912C24.5603 8.92029 24.5896 8.85193 24.5603 8.82263C24.531 8.79333 24.6287 8.69568 24.7849 8.60779C24.9314 8.5199 25.0681 8.47107 25.0974 8.50037C25.1267 8.53943 25.4295 8.78357 25.781 9.05701Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M25.0002 16.332C21.9728 16.4199 17.5588 16.7617 17.5002 16.9179C17.4806 16.9667 17.422 16.9863 17.383 16.957C17.1974 16.8398 16.9924 17.5136 16.797 18.8222C16.6896 19.5742 16.6017 20.414 16.6017 20.6874C16.6017 21.5566 16.3283 21.5077 21.9924 21.5663C24.6974 21.5956 28.3498 21.5859 30.1174 21.5468C33.1545 21.4784 33.3302 21.4687 33.5256 21.2734C33.7111 21.0878 33.7209 20.9902 33.672 18.8417C33.6232 16.8788 33.6037 16.5859 33.4474 16.4199C33.2814 16.2343 33.1447 16.2245 30.6252 16.2441C29.1701 16.2441 26.6408 16.2929 25.0002 16.332ZM19.9709 17.2988C21.0451 17.3476 24.2384 17.4452 27.0802 17.5136L32.2267 17.6308V18.8222V20.0136L25.2736 20.0624C21.4455 20.0917 18.1935 20.1406 18.047 20.1796C17.7931 20.248 17.7931 20.248 17.7345 19.5742C17.6467 18.6659 17.4806 17.4062 17.4123 17.2499C17.3732 17.1425 17.4416 17.123 17.6955 17.162C17.8713 17.1913 18.8967 17.2499 19.9709 17.2988Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.3125 18.1488C19.707 18.305 19.4336 18.5589 19.4336 18.9593C19.4336 19.3597 19.707 19.5453 20.4492 19.6136C21.0352 19.6624 21.123 19.6527 21.3379 19.4476C21.4844 19.3109 21.582 19.1058 21.582 18.9593C21.582 18.5785 21.3867 18.0804 21.2402 18.0804C21.1719 18.0804 21.0547 18.0609 20.9766 18.0413C20.9082 18.0218 20.6055 18.0706 20.3125 18.1488Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M26.5626 22.348C22.5098 22.4261 17.5782 22.7679 17.5001 22.973C17.4805 23.0218 17.4219 23.0413 17.3829 23.012C17.1973 22.8949 16.9922 23.5784 16.7969 24.9359C16.6797 25.7171 16.6016 26.5667 16.6212 26.8304C16.6505 27.2406 16.6895 27.3284 16.9141 27.4359C17.2755 27.6019 25.2149 27.7093 29.9219 27.6019C33.1544 27.5335 33.3301 27.5238 33.5255 27.3284C33.711 27.1429 33.7208 27.0452 33.6719 24.8968C33.6231 22.9339 33.6036 22.6409 33.4473 22.4749C33.2813 22.2894 33.1544 22.2796 30.5762 22.2992C29.0919 22.3089 27.2852 22.3382 26.5626 22.348ZM20.0196 23.3538C21.0645 23.4027 24.2383 23.5003 27.0801 23.5687L32.2266 23.6859V24.8773V26.0687L25.1758 26.1077C21.2891 26.137 18.0469 26.1859 17.9493 26.2249C17.8126 26.2835 17.7833 26.2054 17.7344 25.6292C17.6954 25.2679 17.6172 24.555 17.5489 24.0667L17.4219 23.1585L17.7637 23.2171C17.959 23.2366 18.9747 23.305 20.0196 23.3538Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.3125 24.1937C19.707 24.3597 19.4336 24.6136 19.4336 25.0238C19.4336 25.434 19.8145 25.6488 20.5664 25.6488C21.2988 25.6488 21.582 25.4535 21.582 24.9554C21.582 24.5648 21.3965 24.1351 21.2305 24.1351C21.1621 24.1351 21.0547 24.1156 20.9766 24.0961C20.9082 24.0765 20.6055 24.1156 20.3125 24.1937Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M24.7559 28.4422C21.7286 28.5301 17.5587 28.8621 17.5001 29.0281C17.4805 29.077 17.4219 29.0965 17.3829 29.0672C17.1973 28.95 16.9922 29.6336 16.7969 30.991C16.6797 31.7723 16.6016 32.6219 16.6212 32.8856C16.67 33.7059 16.1036 33.657 25.2149 33.657H33.1837L33.4473 33.4129L33.711 33.1688L33.6719 30.9422C33.6231 28.9891 33.6036 28.6961 33.4473 28.5301C33.2813 28.3445 33.1446 28.3348 30.4786 28.3543C28.9454 28.3543 26.3672 28.4031 24.7559 28.4422ZM20.0684 29.409C21.1426 29.4578 24.3165 29.5555 27.129 29.6238L32.2266 29.741V30.9324V32.1238L25.1758 32.1629C21.2891 32.1922 18.0469 32.241 17.9493 32.2801C17.8126 32.3387 17.7833 32.2508 17.7247 31.5867C17.6856 31.1668 17.6075 30.4637 17.5391 30.0242L17.4219 29.2137L17.7637 29.2723C17.959 29.2918 18.9942 29.3602 20.0684 29.409Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M20.2637 30.2484C19.707 30.3949 19.4336 30.6683 19.4336 31.0785C19.4336 31.4789 19.8145 31.6937 20.5078 31.7035C20.9277 31.7035 21.123 31.6547 21.2793 31.5179C21.6406 31.1859 21.4941 30.2679 21.0547 30.1508C20.9473 30.1215 20.8301 30.1019 20.8008 30.1019C20.7715 30.1117 20.5371 30.1801 20.2637 30.2484Z"
                fill="var(--color-black-200)"
            />
            <path
                d="M42.6757 28.3929C41.6796 29.2425 41.7382 31.0296 42.7831 31.723C43.2421 32.0257 43.2518 31.9769 42.5292 33.4613C41.2206 36.1664 39.0233 38.8031 36.5136 40.6878C33.9647 42.5921 31.1327 43.7543 27.9003 44.2035C24.8046 44.6332 22.1288 44.4183 19.287 43.5003C16.2694 42.5238 13.8475 41.0101 11.5721 38.6761C9.74598 36.8109 8.73035 35.4437 7.85145 33.6761C7.54871 33.0902 7.26551 32.4945 7.21668 32.3675L7.12879 32.1234L7.47059 32.3285C7.89051 32.5726 8.25184 32.5921 8.33973 32.3773C8.43738 32.1234 8.40809 31.557 8.28113 31.3324C7.87098 30.5609 6.4159 29.2816 5.94715 29.2816C5.43934 29.2816 5.31238 29.5062 5.03895 30.9515C4.77527 32.3773 4.79481 32.9144 5.15613 33.1878C5.42957 33.4027 5.52723 33.3343 5.82996 32.68L6.12293 32.055L6.28895 32.6605C7.05067 35.4437 8.34949 37.6214 10.6444 39.9945C13.1346 42.5628 16.2596 44.389 19.6093 45.2386C23.1444 46.1371 26.7772 46.1273 30.2147 45.2289C33.5253 44.3597 36.5917 42.6117 39.0429 40.2093C41.2206 38.0902 42.4804 36.0296 43.3788 33.1683L43.7011 32.1429H44.2968C45.3124 32.1429 46.0936 31.4886 46.3085 30.4632C46.455 29.7503 46.2889 29.1937 45.7909 28.6957C45.4296 28.3343 45.4003 28.3246 44.8241 28.3441C44.4725 28.3636 44.1893 28.3246 44.121 28.2562C44.0526 28.1878 43.7694 28.139 43.496 28.139C43.0858 28.139 42.9198 28.1878 42.6757 28.3929ZM44.4725 28.9789C44.9901 29.2621 45.205 30.014 44.8729 30.3753C44.6288 30.6585 44.1796 30.7269 43.8866 30.5316C43.5643 30.3265 43.4667 30.0238 43.5741 29.6039C43.6718 29.2621 44.1503 28.6859 44.2382 28.8031C44.2675 28.8421 44.3749 28.9203 44.4725 28.9789Z"
                fill="var(--color-black-200)"
            />
        </svg>
    );
};

export default BackendIcon;
