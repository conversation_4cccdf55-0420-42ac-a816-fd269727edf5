import React from 'react';

const ClientSupportIcon = () => {
    return (
        <svg
            height="48"
            width="48"
            fill="none"
            viewBox="0 0 48 48"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_645_891281)">
                <path
                    d="M33.1578 12C29.155 12 25.91 15.2449 25.91 19.2478C25.91 22.5912 28.1739 25.4059 31.2524 26.2425C27.0244 26.7064 23.8113 28.9208 21.618 32.1613C19.1638 35.7873 18 40.6674 18 45.7927C18 46.2566 18.3761 46.6327 18.84 46.6327H47.0412C47.5051 46.6327 47.8812 46.2566 47.8812 45.7927C47.8812 40.5432 47.0795 35.6508 44.7905 32.048C42.7528 28.8409 39.5879 26.7363 35.0109 26.2565C38.116 25.4377 40.4056 22.6101 40.4056 19.2478C40.4056 15.2449 37.1606 12 33.1578 12ZM18.8508 44.9527C18.9916 39.4791 20.5166 34.4485 23.4941 31.1125C20.365 34.6185 18.8402 39.9961 18.8402 45.7925C18.8402 45.5114 18.8436 45.2316 18.8508 44.9527Z"
                    fill="var(--color-primary-900)"
                    fillRule="evenodd"
                    opacity="0.5"
                />
                <path
                    d="M25.1168 7C21.114 7 17.869 10.2449 17.869 14.2478C17.869 17.5912 20.1329 20.4059 23.2114 21.2425C18.9834 21.7064 15.7703 23.9208 13.577 27.1613C11.1228 30.7873 9.95898 35.6674 9.95898 40.7927C9.95898 41.2566 10.3351 41.6327 10.799 41.6327H39.0002C39.4641 41.6327 39.8402 41.2566 39.8402 40.7927C39.8402 35.5432 39.0385 30.6508 36.7495 27.048C34.7118 23.8409 31.5469 21.7363 26.9699 21.2565C30.075 20.4377 32.3646 17.6101 32.3646 14.2478C32.3646 10.2449 29.1196 7 25.1168 7ZM10.8098 39.9527C10.9506 34.4791 12.4756 29.4485 15.4531 26.1125C12.324 29.6185 10.7992 34.9961 10.7992 40.7925C10.7992 40.5114 10.8026 40.2316 10.8098 39.9527Z"
                    fill="var(--color-primary-900)"
                    fillRule="evenodd"
                />
                <mask
                    height="37"
                    id="mask0_645_891281"
                    style={{ maskType: 'luminance' }}
                    width="32"
                    x="-1"
                    y="0"
                    maskUnits="userSpaceOnUse"
                >
                    <path d="M30.6045 0H-0.395508V37H30.6045V0Z" fill="white" />
                    <path
                        d="M15.5351 1C11.4934 1 8.217 4.27643 8.217 8.31812C8.217 12.3598 11.4934 15.6362 15.5351 15.6362C19.5768 15.6362 22.8532 12.3598 22.8532 8.31812C22.8532 4.27643 19.5768 1 15.5351 1ZM29.6048 35.7924C29.6048 26.8809 26.3814 19.6566 15.3673 19.6566C5.73714 19.6566 1.12988 27.1867 1.12988 35.7924H29.6048Z"
                        fill="white"
                        fillRule="evenodd"
                    />
                </mask>
                <g mask="url(#mask0_645_891281)">
                    <path
                        d="M29.6048 35.7924H30.6048C30.6048 36.3447 30.1571 36.7924 29.6048 36.7924V35.7924ZM1.12988 35.7924V36.7924C0.577598 36.7924 0.129883 36.3447 0.129883 35.7924H1.12988ZM7.217 8.31812C7.217 3.72415 10.9411 0 15.5351 0V2C12.0457 2 9.217 4.82872 9.217 8.31812H7.217ZM15.5351 16.6362C10.9411 16.6362 7.217 12.9121 7.217 8.31812H9.217C9.217 11.8075 12.0457 14.6362 15.5351 14.6362V16.6362ZM23.8532 8.31812C23.8532 12.9121 20.129 16.6362 15.5351 16.6362V14.6362C19.0245 14.6362 21.8532 11.8075 21.8532 8.31812H23.8532ZM15.5351 0C20.129 0 23.8532 3.72415 23.8532 8.31812H21.8532C21.8532 4.82872 19.0245 2 15.5351 2V0ZM15.3673 18.6566C21.113 18.6566 25.0043 20.5558 27.4173 23.784C29.7883 26.956 30.6048 31.2467 30.6048 35.7924H28.6048C28.6048 31.4266 27.8096 27.6494 25.8153 24.9814C23.8631 22.3696 20.6356 20.6566 15.3673 20.6566V18.6566ZM0.129883 35.7924C0.129883 31.3248 1.32437 27.0525 3.85499 23.8745C6.40888 20.6672 10.2616 18.6566 15.3673 18.6566V20.6566C10.8427 20.6566 7.57685 22.4111 5.41957 25.1203C3.23901 27.8587 2.12988 31.6543 2.12988 35.7924H0.129883ZM29.6048 36.7924H1.12988V34.7924H29.6048V36.7924Z"
                        fill="var(--color-primary-900)"
                    />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_645_891281">
                    <rect height="48" width="48" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default ClientSupportIcon;
