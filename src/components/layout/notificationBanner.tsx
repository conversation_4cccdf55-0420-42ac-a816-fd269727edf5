'use client';
import { useState } from 'react';
import Notifiactions from '@/data/NotifiactionsData.json';

export default function NotificationBanner() {
    const [visible, setVisible] = useState(Notifiactions.displayMessage);

    if (!visible) return null;

    return (
        <div
            className="relative z-50 flex w-full items-center justify-center px-4 py-2 text-white"
            style={{
                background: 'linear-gradient(90deg, #001377 -6.46%, var(--color-black-300) 100%)',
                backdropFilter: 'blur(70px)',
                backgroundImage: `url(/images/header_message.png )`,
            }}
        >
            <p className="text-Poppins text-xs font-medium md:text-sm">{Notifiactions.messgae}</p>
            <button
                onClick={() => setVisible(false)}
                className="absolute right-4 ml-4 cursor-pointer text-white transition hover:text-gray-200"
                aria-label="Dismiss notification"
            >
                ✕
            </button>
        </div>
    );
}
