import Link from 'next/link';
import Image from 'next/image';
import { PlainIcon, RightArrowWithLineIcon } from '../icons/index';
import links from '@/data/LinksData.json';

export default function Footer() {
    return (
        <div className="w-full bg-[#1A1616]">
            <footer className="wrapper-gap lg:wrapper-gap mx-auto max-w-7xl px-6 text-white lg:px-12">
                <div className="- mb-5 flex flex-col lg:flex-row lg:gap-30">
                    <div className="mb-8 max-w-2xl">
                        <h2 className="text-unbounded mb-4 text-xl font-semibold sm:text-2xl md:mb-7">
                            Don’t Missed Subscribed!
                        </h2>
                        <div className="relative sm:max-w-sm md:max-w-md lg:w-96">
                            <input
                                type="email"
                                placeholder="Enter Email"
                                className="footer-input w-full border border-gray-600 px-4 py-2 pr-12 text-sm"
                            />
                            <button
                                type="submit"
                                className="absolute top-3 right-15 flex h-9 w-9 animate-pulse cursor-pointer items-center justify-center rounded-full bg-blue-600 transition hover:animate-none"
                            >
                                <PlainIcon width="17" height="16" fill="white" />
                            </button>
                        </div>
                    </div>

                    <div className="ml- flex flex-col gap-8 sm:flex-row">
                        <div className="lg:w-[15rem]">
                            <h3 className="text-unbounded mb-6 text-2xl md:text-xl">Company</h3>
                            <ul className="grid grid-cols-1 gap-6">
                                {links.CompanyLinks.map((link, index) => (
                                    <li
                                        key={index}
                                        className="font-unbounded text-sm text-[var(--color-gray-500)]"
                                    >
                                        <Link className="hover:text-blue-500" href={link.href}>
                                            {link.label}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-unbounded mb-4 text-2xl md:text-xl">
                                Our Solutions
                            </h3>
                            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:gap-6">
                                {links.serviceLinks.map((link, index) => (
                                    <Link
                                        key={index}
                                        href={link.href}
                                        className="font-unbounded text-sm text-[var(--color-gray-500)] hover:text-blue-500"
                                    >
                                        {link.label}
                                    </Link>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mb-6 overflow-hidden rounded-lg border border-[var(--color-gray-800)]">
                    <div className="">
                        <div className="flex flex-col justify-between border-b border-[var(--color-gray-800)] px-6 md:flex-row">
                            <div className="ml-2 flex items-center">
                                <Image
                                    src="/logo/acelan-white.png"
                                    loading="lazy"
                                    alt="acelan logo"
                                    height={50}
                                    width={130}
                                />
                            </div>
                            <div className="flex max-w-xl flex-col md:max-w-sm md:flex-1 md:flex-row md:justify-between">
                                <div className="text- mr-2 flex flex-col md:justify-center">
                                    <h4 className="text-unbounded text-sm font-medium text-gray-200">
                                        Phone
                                    </h4>
                                    <h2 className="text-xl font-bold">+91-90988 63208</h2>
                                </div>
                                <div className="flex flex-col justify-center py-4 text-left md:p-4">
                                    <h4 className="text-unbounded text-xs text-gray-200">
                                        Email Now
                                    </h4>
                                    <h2 className="text-xl font-semibold"><EMAIL></h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col items-center justify-between gap-2 md:flex-row">
                    <p className="text-unbounded text-center text-[10px] text-gray-300 md:text-left">
                        Copyright 2025 acelan | Designed By Acelan Technologies
                    </p>
                    <div className="text-unbounded flex items-center text-center text-[10px] text-gray-300 md:text-right">
                        <span className="flex items-center text-[10px]">
                            <RightArrowWithLineIcon
                                width={'12'}
                                height={'12'}
                                fill={'none'}
                                className="mr-1"
                            />
                            Our Business
                            <Link href="#" className="ml-1">
                                Policy, Terms & Condition
                            </Link>
                        </span>
                    </div>
                </div>
            </footer>
        </div>
    );
}
