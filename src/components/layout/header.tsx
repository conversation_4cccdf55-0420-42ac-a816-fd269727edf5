'use client';

import { useState } from 'react';
import Link from 'next/link';
import NotificationBanner from './notificationBanner';
import ServicesDropdown from '../comman/ServicesDropdown';
import {
    OpenLinksIcon,
    RightArrowIcon,
    HamburgerIcon,
    CloseIcon,
    getIconByKey,
} from '../icons/index';
import links from '@/data/LinksData.json';
import { usePathname, useRouter } from 'next/navigation';

export default function Header() {
    const [isMobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isMobileServicesOpen, setIsMobileServicesOpen] = useState<boolean>(false);

    const toggleMobileMenu: () => void = () => setMobileMenuOpen(!isMobileMenuOpen);
    const HeaderLogo = getIconByKey('headerLogo');

    // -------------------------
    const pathname = usePathname();
    const router = useRouter();

    const handleIndustryClick = (e: React.MouseEvent) => {
        e.preventDefault();
        if (pathname.startsWith('/services/')) {
            const section = document.getElementById('industries-we-serve');
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            router.push('/#industries-we-serve');
        }
    };

    const handleHowWeDoClick = (e: React.MouseEvent) => {
        e.preventDefault();
        if (pathname.startsWith('/services/')) {
            const section = document.getElementById('how-we-do');
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            router.push('/#how-we-do');
        }
    };

    // -------------------------------------------

    return (
        <header className="bg-white sticky top-0 w-full z-50">
            <NotificationBanner />

            <div className="mx-auto flex max-w-7xl items-center justify-between px-4 md:flex md:justify-between overflow-x-hidden">
                <div className="flex items-center">
                    <Link href="/" className="bounce-once-smooth">
                        <HeaderLogo width="180" height="60" fill="var(--color-black)" />
                    </Link>
                </div>

                <nav className="text-md font-outfit hidden h-[80px] items-stretch leading-[16px] font-[600] lg:flex">
                    {links.navLinks.map((link, index) => (
                        <div
                            key={index}
                            className="relative"
                            // onMouseEnter={() => link.dropdown && setShowDropdown(true)}
                            // onMouseLeave={() => link.dropdown && setShowDropdown(false)}
                        >
                            {link.dropdown ? (
                                <>
                                    <button className="ml-4 flex h-full items-center gap-1 text-[var(--color-primary-800)] hover:text-blue-700">
                                        {link.label}
                                        <span className='h-full flex items-center transition-transform  duration-200 hover:rotate-180' onMouseEnter={() => link.dropdown && setShowDropdown(true)}
                            onMouseLeave={() => link.dropdown && setShowDropdown(false)} >
                                        <OpenLinksIcon width="13" height="13" fill="black" />
                                        </span>
                                    </button>
                                </>
                            ) : link.label === 'Industry We Serve' ? (
                                <Link
                                    href="/services/app-dev#industries-we-serve"
                                    onClick={handleIndustryClick}
                                    className="ml-4 flex h-full items-center gap-1 text-[var(--color-primary-800)] hover:text-blue-700"
                                >
                                    {link.label}
                                </Link>
                            ) : link.label === 'How We Do' ? (
                                <Link
                                    href="/services/app-dev#how-we-do"
                                    onClick={handleHowWeDoClick}
                                    className="ml-4 flex h-full items-center gap-1 text-[var(--color-primary-800)] hover:text-blue-700"
                                >
                                    {link.label}
                                </Link>
                            ) : (
                                <Link
                                    href={link.href || '/'}
                                    className="ml-4 flex h-full items-center gap-1 text-[var(--color-primary-800)] hover:text-blue-700"
                                >
                                    {link.label}
                                </Link>
                            )}
                        </div>
                    ))}
                </nav>

                <div className="flex items-center">
                    <Link href="/contact-us" onClick={() => setMobileMenuOpen(false)}>
                        <button className="button flex cursor-pointer items-center justify-center gap-x-2 rounded-md bg-blue-700 px-6 py-3 text-sm whitespace-nowrap text-white hover:bg-blue-800">
                            <span className="font-poppins font-[600]">Contact us</span>
                            <RightArrowIcon
                                width="23"
                                height="14"
                                fill="white"
                                className="animate-pulse"
                            />
                        </button>
                    </Link>

                    <button
                        onClick={toggleMobileMenu}
                        className="z-50 pl-2 text-[var(--color-primary-800)] lg:hidden"
                        aria-label="Toggle Menu"
                    >
                        {isMobileMenuOpen ? (
                            <CloseIcon width="24" height="24" fill="none" />
                        ) : (
                            <HamburgerIcon width="24" height="24" fill="none" />
                        )}
                    </button>
                </div>
            </div>
            {showDropdown && (
                <div
                    className="relative z-50 w-full"
                    onMouseEnter={() => showDropdown && setShowDropdown(true)}
                    onMouseLeave={() => showDropdown && setShowDropdown(false)}
                >
                    <div className="absolute inset-x-0 bg-[#061052]">
                        <div className="mx-auto flex max-w-7xl items-center justify-center">
                            <ServicesDropdown
                                showDropdown={showDropdown}
                                setShowDropdown={setShowDropdown}
                                services={links.serviceLinks}
                                onClose={() => setShowDropdown(false)}
                            />
                        </div>
                    </div>
                </div>
            )}

            <div
                className={`fixed top-6 right-0 z-40 h-full w-45 transform bg-white shadow-lg transition-transform duration-300 ease-in-out md:top-10 md:w-60 ${
                    isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
                }`}
            >
                <div className="font-Poppins flex flex-col space-y-6 px-6 py-8 font-semibold text-[var(--color-primary-800)]">
                    {links.navLinks.map((link, index) => (
                        <div key={index}>
                            {link.dropdown ? (
                                <>
                                    {/* Button to toggle dropdown */}
                                    <button
                                        onClick={() =>
                                            setIsMobileServicesOpen(!isMobileServicesOpen)
                                        }
                                        className="flex w-full items-center justify-between gap-1 hover:text-blue-700"
                                    >
                                        <span>{link.label}</span>
                                        <OpenLinksIcon
                                            width="13"
                                            height="7"
                                            fill="none"
                                            className={`transition-transform duration-200 ${
                                                isMobileServicesOpen ? 'rotate-180' : 'rotate-0'
                                            }`}
                                        />
                                    </button>

                                    {isMobileServicesOpen && (
                                        <div className="space-y- mt-3 flex flex-col text-xs font-normal md:text-sm">
                                            {links.serviceLinks.map((service, i) => {
                                                const ServiceIcon = getIconByKey(service.iconKey);
                                                return (
                                                    <Link
                                                        key={i}
                                                        href={service.href}
                                                        className="mb-2 flex items-center gap-2 border-b-1 border-[var(--color-gray-300)] pb-2 hover:text-blue-700"
                                                        onClick={() => {
                                                            setMobileMenuOpen(false);
                                                            setIsMobileServicesOpen(false);
                                                        }}
                                                    >
                                                        {ServiceIcon && (
                                                            <ServiceIcon
                                                                width="16"
                                                                height="16"
                                                                fill="black"
                                                            />
                                                        )}
                                                        {service.label}
                                                    </Link>
                                                );
                                            })}
                                        </div>
                                    )}
                                </>
                            ) : (
                                <Link
                                    href={link.href || '/'}
                                    className="block hover:text-blue-700"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {link.label}
                                </Link>
                            )}
                        </div>
                    ))}
                </div>

                {/* <div className="font-Poppins flex flex-col space-y-6 px-6 py-8 font-semibold text-[var(--color-primary-800)]">
                    {links.navLinks.map((link, index) => (
                        <div key={index}>
                            {link.dropdown ? (
                                <button className="z-50 flex items-center gap-1 hover:text-blue-700">
                                    {link.label}
                                    <OpenLinksIcon width="13" height="7" fill="none" />
                                </button>
                            ) : (
                                <Link
                                    href={link.href || '/'}
                                    className="block hover:text-blue-700"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {link.label}
                                </Link>
                            )}
                        </div>
                    ))}
                </div> */}
            </div>
        </header>
    );
}
