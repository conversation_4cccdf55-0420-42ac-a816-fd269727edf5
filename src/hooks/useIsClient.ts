'use client';

import { useEffect, useState } from 'react';

/**
 * Custom hook to check if the component is running on the client side.
 * This helps prevent hydration mismatches by ensuring certain code only runs after hydration.
 *
 * @returns boolean - true if running on client, false during SSR
 */
export function useIsClient(): boolean {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    return isClient;
}

/**
 * Custom hook that provides a safe way to access window object.
 * Returns null during SSR and the actual window object on the client.
 *
 * @returns Window | null
 */
export function useWindow(): Window | null {
    const isClient = useIsClient();
    return isClient ? window : null;
}
